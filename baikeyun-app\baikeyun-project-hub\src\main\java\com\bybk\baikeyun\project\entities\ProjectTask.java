package com.bybk.baikeyun.project.entities;

import io.quarkus.hibernate.reactive.panache.PanacheEntityBase;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 项目任务实体类（子的子表）
 * 
 * 项目任务管理，支持层级结构
 * 
 * 主子关系:
 * - Project (主表) → ProjectTask (子表)
 * - ProjectSprint (主表) → ProjectTask (子表)
 * - ProjectTask (父) → ProjectTask (子) - 自关联形成层级
 * - ProjectTask (主表) → ProjectTaskItem (子表)
 * 
 * 外部对接:
 * - requirement_id → 需求管理模块
 * - requirement_item_id → 需求管理模块
 * - process_instance_id → 流程管理模块
 * 
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */
@Entity
@Table(name = "project_task", schema = "project_hub",
    uniqueConstraints = {
        @UniqueConstraint(name = "uk_task_code", columnNames = {"project_id", "task_code"})
    }
)
@Getter
@Setter
public class ProjectTask extends PanacheEntityBase {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "id", length = 36, nullable = false)
    private String id;

    // ==================== 项目关联 ====================

    /**
     * 所属项目ID
     */
    @Column(name = "project_id", length = 36, nullable = false)
    private String projectId;

    /**
     * 所属迭代ID（可为空）
     */
    @Column(name = "sprint_id", length = 36)
    private String sprintId;

    // ==================== 基本信息 ====================

    /**
     * 任务编码
     */
    @Column(name = "task_code", length = 50, nullable = false)
    private String taskCode;

    /**
     * 任务标题
     */
    @Column(name = "task_title", length = 500, nullable = false)
    private String taskTitle;

    /**
     * 任务描述
     */
    @Column(name = "task_description", columnDefinition = "TEXT")
    private String taskDescription;

    // ==================== 任务分类 ====================

    /**
     * 任务类型: feature, bug, task, story, epic
     */
    @Column(name = "task_type", length = 50, nullable = false)
    private String taskType;

    /**
     * 任务类别: frontend, backend, database, devops, test
     */
    @Column(name = "task_category", length = 50)
    private String taskCategory;

    // ==================== 层级关系 ====================

    /**
     * 父任务ID（自关联）
     */
    @Column(name = "parent_task_id", length = 36)
    private String parentTaskId;

    /**
     * 根任务ID
     */
    @Column(name = "root_task_id", length = 36)
    private String rootTaskId;

    /**
     * 层级深度（0表示根节点）
     */
    @Column(name = "level")
    private Integer level = 0;

    /**
     * 层级路径: /task1/task2/task3
     */
    @Column(name = "path", length = 500)
    private String path;

    // ==================== 任务状态 ====================

    /**
     * 状态: todo, in_progress, review, testing, done, blocked
     */
    @Column(name = "status", length = 20)
    private String status = "todo";

    /**
     * 优先级: P0, P1, P2, P3
     */
    @Column(name = "priority", length = 10)
    private String priority = "P2";

    // ==================== 敏捷属性 ====================

    /**
     * 故事点
     */
    @Column(name = "story_points")
    private Integer storyPoints;

    /**
     * 预估工时
     */
    @Column(name = "estimated_hours", precision = 10, scale = 2)
    private BigDecimal estimatedHours;

    /**
     * 实际工时
     */
    @Column(name = "actual_hours", precision = 10, scale = 2)
    private BigDecimal actualHours;

    // ==================== 责任人 ====================

    /**
     * 负责人
     */
    @Column(name = "assigned_to", length = 100)
    private String assignedTo;

    /**
     * 报告人
     */
    @Column(name = "reporter", length = 100)
    private String reporter;

    /**
     * 评审人
     */
    @Column(name = "reviewer", length = 100)
    private String reviewer;

    // ==================== 时间计划 ====================

    /**
     * 计划开始日期
     */
    @Column(name = "planned_start_date")
    private LocalDate plannedStartDate;

    /**
     * 计划结束日期
     */
    @Column(name = "planned_end_date")
    private LocalDate plannedEndDate;

    /**
     * 实际开始日期
     */
    @Column(name = "actual_start_date")
    private LocalDate actualStartDate;

    /**
     * 实际结束日期
     */
    @Column(name = "actual_end_date")
    private LocalDate actualEndDate;

    // ==================== 外部关联（对接其他模块） ====================

    /**
     * 关联需求ID（对接需求管理模块）
     */
    @Column(name = "requirement_id", length = 36)
    private String requirementId;

    /**
     * 关联需求子项ID（对接需求管理模块）
     */
    @Column(name = "requirement_item_id", length = 36)
    private String requirementItemId;

    /**
     * 关联流程实例ID（对接流程管理模块）
     */
    @Column(name = "process_instance_id", length = 36)
    private String processInstanceId;

    // ==================== 审计信息 ====================

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    /**
     * 创建人
     */
    @Column(name = "created_by", length = 100)
    private String createdBy;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 更新人
     */
    @Column(name = "updated_by", length = 100)
    private String updatedBy;

    // ==================== 扩展信息 ====================

    /**
     * 扩展元数据（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private Map<String, Object> metadata;

    // ==================== 关联关系 ====================

    /**
     * 所属项目（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", insertable = false, updatable = false)
    private Project project;

    /**
     * 所属迭代（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sprint_id", insertable = false, updatable = false)
    private ProjectSprint sprint;

    /**
     * 父任务（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_task_id", insertable = false, updatable = false)
    private ProjectTask parentTask;

    /**
     * 子任务列表（一对多关系）
     */
    @OneToMany(mappedBy = "parentTask", fetch = FetchType.LAZY)
    private List<ProjectTask> childTasks;

    /**
     * 任务子项列表（一对多关系）
     */
    @OneToMany(mappedBy = "task", fetch = FetchType.LAZY)
    private List<ProjectTaskItem> taskItems;

    // ==================== 生命周期回调 ====================

    /**
     * 持久化前自动生成ID
     */
    @PrePersist
    public void prePersist() {
        if (this.id == null) {
            this.id = UUID.randomUUID().toString();
        }
        if (this.createdAt == null) {
            this.createdAt = LocalDateTime.now();
        }
        if (this.updatedAt == null) {
            this.updatedAt = LocalDateTime.now();
        }
    }

    /**
     * 更新前自动更新时间
     */
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}

