package com.bybk.baikeyun.mdm.entity;

import io.quarkus.hibernate.reactive.panache.PanacheEntityBase;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 编码规则实体
 * 
 * 统一管理主数据编码规则和业务数据编码规则
 * 
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */
@Entity
@Table(name = "core_code_rule", schema = "public")
@Getter
@Setter
public class CodeRule extends PanacheEntityBase {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "id", length = 36, nullable = false)
    private String id;

    /**
     * 规则分类（master_data-主数据编码，business_data-业务数据编码）
     * 暂时标记为 @Transient，因为 core_code_rule 表中没有此字段
     */
    @Transient
    private String ruleCategory;

    /**
     * 规则类型（唯一标识，如：customer, supplier, material, order, contract等）
     */
    @Column(name = "rule_type", length = 50, nullable = false, unique = true)
    private String ruleType;

    /**
     * 规则名称
     */
    @Column(name = "rule_name", length = 200, nullable = false)
    private String ruleName;

    /**
     * 规则描述
     * 暂时标记为 @Transient，因为 core_code_rule 表中没有此字段
     */
    @Transient
    private String description;

    /**
     * 编码前缀
     */
    @Column(name = "prefix", length = 50)
    private String prefix;

    /**
     * 日期格式（yyyy, yyyyMM, yyyyMMdd）
     */
    @Column(name = "date_format", length = 20)
    private String dateFormat;

    /**
     * 序列号长度（补零位数）
     */
    @Column(name = "sequence_length")
    private Integer sequenceLength = 4;

    /**
     * 分隔符
     */
    @Column(name = "separator", length = 10)
    private String separator = "-";

    /**
     * 编码后缀
     */
    @Column(name = "suffix", length = 50)
    private String suffix;

    /**
     * 重置类型（YEARLY, MONTHLY, DAILY, NEVER）
     */
    @Column(name = "reset_type", length = 20)
    private String resetType = "YEARLY";

    /**
     * 编码示例
     * 暂时标记为 @Transient，因为 core_code_rule 表中没有此字段
     */
    @Transient
    private String codeExample;

    /**
     * 是否启用
     * 暂时标记为 @Transient，因为 core_code_rule 表中没有此字段
     */
    @Transient
    private Boolean isEnabled = true;

    /**
     * 状态（active-启用，inactive-禁用）
     */
    @Column(name = "status", length = 20, nullable = false)
    private String status = "active";

    /**
     * 排序号
     * 暂时标记为 @Transient，因为 core_code_rule 表中没有此字段
     */
    @Transient
    private Integer sortOrder = 0;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 创建人ID
     * 暂时标记为 @Transient，因为 core_code_rule 表中没有此字段
     */
    @Transient
    private String createdBy;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 更新人ID
     * 暂时标记为 @Transient，因为 core_code_rule 表中没有此字段
     */
    @Transient
    private String updatedBy;

    /**
     * 备注
     * 暂时标记为 @Transient，因为 core_code_rule 表中没有此字段
     */
    @Transient
    private String remark;

    @PrePersist
    public void prePersist() {
        if (this.id == null) {
            this.id = UUID.randomUUID().toString();
        }
        if (this.createdAt == null) {
            this.createdAt = LocalDateTime.now();
        }
        if (this.status == null) {
            this.status = "active";
        }
        if (this.isEnabled == null) {
            this.isEnabled = true;
        }
        if (this.sequenceLength == null) {
            this.sequenceLength = 4;
        }
        if (this.separator == null) {
            this.separator = "-";
        }
        if (this.resetType == null) {
            this.resetType = "YEARLY";
        }
        if (this.sortOrder == null) {
            this.sortOrder = 0;
        }
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}

