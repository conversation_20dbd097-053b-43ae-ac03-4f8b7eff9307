package com.bybk.baikeyun.api.process.entities;

import io.quarkus.hibernate.reactive.panache.PanacheEntityBase;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 流程定义实体类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-20
 * @implNote 存储流程定义的元数据，包括版本、状态、分类等信息
 */
@Getter
@Setter
@Entity
@Table(name = "process_definition", schema = "processhub",
       uniqueConstraints = {
           @UniqueConstraint(name = "uk_definition_key_version", columnNames = {"definition_key", "version"})
       })
@NamedQuery(name = "ProcessDefinition.findAll", 
            query = "SELECT p FROM ProcessDefinition p ORDER BY p.createdAt DESC")
@NamedQuery(name = "ProcessDefinition.findByKey", 
            query = "SELECT p FROM ProcessDefinition p WHERE p.definitionKey = :key ORDER BY p.version DESC")
@NamedQuery(name = "ProcessDefinition.findLatestByKey", 
            query = "SELECT p FROM ProcessDefinition p WHERE p.definitionKey = :key AND p.isLatestVersion = true")
@NamedQuery(name = "ProcessDefinition.findByStatus", 
            query = "SELECT p FROM ProcessDefinition p WHERE p.status = :status ORDER BY p.createdAt DESC")
public class ProcessDefinition extends PanacheEntityBase {
    
    // ==================== 主键 ====================
    
    @Id
    @Column(name = "id", columnDefinition = "varchar(36)")
    private String id;
    
    @PrePersist
    public void setIdToUUID() {
        if (id == null) {
            id = UUID.randomUUID().toString();
        }
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (updatedAt == null) {
            updatedAt = LocalDateTime.now();
        }
    }
    
    @PreUpdate
    public void setUpdatedAt() {
        updatedAt = LocalDateTime.now();
    }
    
    // ==================== 业务标识 ====================
    
    /**
     * 流程定义唯一键（业务标识）
     * 如 "purchase_approval", "leave_request"
     */
    @Column(name = "definition_key", nullable = false, length = 100)
    private String definitionKey;
    
    /**
     * 流程定义名称
     */
    @Column(name = "name", nullable = false, length = 200)
    private String name;
    
    /**
     * 版本号（从1开始递增）
     */
    @Column(name = "version", nullable = false)
    private Integer version = 1;
    
    // ==================== 状态和分类 ====================
    
    /**
     * 流程定义状态
     * draft, development, review, testing, ready, published, running, deprecated, archived
     */
    @Column(name = "status", nullable = false, length = 20)
    private String status = "draft";
    
    /**
     * 流程分类
     * approval, workflow, automation, integration, custom
     */
    @Column(name = "category", nullable = false, length = 50)
    private String category;
    
    // ==================== 描述信息 ====================
    
    /**
     * 流程描述
     */
    @Column(name = "description", columnDefinition = "text")
    private String description;
    
    /**
     * 标签（JSON数组）
     * 如 ["采购", "审批", "重要"]
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "tags", columnDefinition = "jsonb")
    private List<String> tags;
    
    /**
     * 图标URL或图标名称
     */
    @Column(name = "icon", length = 200)
    private String icon;
    
    // ==================== 流程数据引用 ====================
    
    /**
     * 流程图数据引用ID（MongoDB文档ID）
     * 指向 MongoDB 中存储的完整流程图JSON数据
     */
    @Column(name = "flow_data_ref", length = 100)
    private String flowDataRef;
    
    /**
     * 流程图数据大小（字节）
     */
    @Column(name = "flow_data_size")
    private Long flowDataSize;
    
    /**
     * 流程图数据校验和（MD5）
     */
    @Column(name = "flow_data_checksum", length = 64)
    private String flowDataChecksum;
    
    /**
     * 是否压缩存储
     */
    @Column(name = "flow_data_compressed")
    private Boolean flowDataCompressed = false;
    
    // ==================== 版本信息 ====================
    
    /**
     * 是否为最新版本
     */
    @Column(name = "is_latest_version")
    private Boolean isLatestVersion = true;
    
    /**
     * 父版本ID（用于版本追溯）
     */
    @Column(name = "parent_version_id", columnDefinition = "varchar(36)")
    private String parentVersionId;
    
    /**
     * 版本标签（语义化版本）
     * 如 "v1.0.0", "v2.1.3"
     */
    @Column(name = "version_tag", length = 50)
    private String versionTag;

    // ==================== 主子流程信息 ====================

    /**
     * 流程类型
     * standalone: 独立流程
     * main: 主流程
     * sub: 子流程
     */
    @Column(name = "process_type", length = 20)
    private String processType = "standalone";

    /**
     * 父流程ID（仅子流程有值）
     */
    @Column(name = "parent_process_id", columnDefinition = "varchar(36)")
    private String parentProcessId;

    /**
     * 是否可复用（可被其他流程调用）
     */
    @Column(name = "is_reusable")
    private Boolean isReusable = false;

    /**
     * 被调用次数统计
     */
    @Column(name = "call_count")
    private Integer callCount = 0;

    /**
     * 流程层级（0=根流程，1=一级子流程，2=二级子流程...）
     */
    @Column(name = "process_level")
    private Integer processLevel = 0;

    /**
     * 根流程ID（顶层主流程的ID）
     */
    @Column(name = "root_process_id", columnDefinition = "varchar(36)")
    private String rootProcessId;

    // ==================== 审计信息 ====================
    
    /**
     * 创建人ID
     */
    @Column(name = "creator", nullable = false, length = 100)
    private String creator;
    
    /**
     * 创建人姓名
     */
    @Column(name = "creator_name", length = 200)
    private String creatorName;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 更新人ID
     */
    @Column(name = "updated_by", length = 100)
    private String updatedBy;
    
    /**
     * 发布时间
     */
    @Column(name = "published_at")
    private LocalDateTime publishedAt;
    
    /**
     * 发布人ID
     */
    @Column(name = "published_by", length = 100)
    private String publishedBy;
    
    // ==================== 统计信息 ====================
    
    /**
     * 实例总数
     */
    @Column(name = "instance_count")
    private Integer instanceCount = 0;
    
    /**
     * 活跃实例数
     */
    @Column(name = "active_instance_count")
    private Integer activeInstanceCount = 0;
    
    /**
     * 总执行时间（毫秒）
     */
    @Column(name = "total_execution_time")
    private Long totalExecutionTime = 0L;
    
    /**
     * 平均执行时间（毫秒）
     */
    @Column(name = "avg_execution_time")
    private Long avgExecutionTime = 0L;
    
    /**
     * 成功率（百分比）
     */
    @Column(name = "success_rate")
    private Double successRate = 0.0;
    
    // ==================== 扩展字段 ====================
    
    /**
     * 扩展元数据（JSON格式）
     * 
     * 结构示例：
     * {
     *   "businessDomain": "采购管理",
     *   "department": "采购部",
     *   "sla": {
     *     "maxDuration": 86400000,
     *     "warningThreshold": 43200000
     *   },
     *   "customFields": {
     *     "approvalLevel": 3,
     *     "requiresSignature": true
     *   }
     * }
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private java.util.Map<String, Object> metadata;
    
    // ==================== 关联关系 ====================
    
    /**
     * 关联的流程版本列表
     */
    @OneToMany(mappedBy = "processDefinition", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ProcessVersion> versions;
    
    /**
     * 关联的流程节点列表
     */
    @OneToMany(mappedBy = "processDefinition", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ProcessNode> nodes;
    
    /**
     * 关联的流程边列表
     */
    @OneToMany(mappedBy = "processDefinition", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ProcessEdge> edges;
}

