package com.bybk.baikeyun.api.process.entities;

import io.quarkus.hibernate.reactive.panache.PanacheEntityBase;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 流程边属性表
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-20
 * @deprecated 已被 {@link EdgePropertyValue} 替代，请使用基于类型模板的新方案
 * @see EdgePropertyValue
 * @see EdgePropertyTemplate
 * @see EdgeTypeDefinition
 * @implNote 存储边的扩展属性，采用EAV模式（实体-属性-值）
 *           <p><b>注意：此类已废弃，新方案提供以下优势：</b></p>
 *           <ul>
 *             <li>类型安全：每种边类型有固定的属性定义</li>
 *             <li>高性能：属性模板缓存</li>
 *             <li>易扩展：新增类型只需添加属性模板</li>
 *             <li>易维护：属性定义集中管理</li>
 *             <li>动态UI：前端根据模板自动渲染表单</li>
 *           </ul>
 */
@Deprecated(since = "2.0", forRemoval = true)
@Getter
@Setter
@Entity
@Table(name = "process_edge_property", schema = "processhub",
       indexes = {
           @Index(name = "idx_edge_property", columnList = "edge_id,property_key"),
           @Index(name = "idx_edge_property_key", columnList = "property_key"),
           @Index(name = "idx_edge_property_type", columnList = "property_type")
       })
@NamedQuery(name = "ProcessEdgeProperty.findByEdgeId",
            query = "SELECT p FROM ProcessEdgeProperty p WHERE p.processEdge.id = :edgeId ORDER BY p.sortOrder")
@NamedQuery(name = "ProcessEdgeProperty.findByEdgeAndKey",
            query = "SELECT p FROM ProcessEdgeProperty p WHERE p.processEdge.id = :edgeId AND p.propertyKey = :key")
public class ProcessEdgeProperty extends PanacheEntityBase {
    
    // ==================== 主键 ====================
    
    @Id
    @Column(name = "id", columnDefinition = "varchar(36)")
    private String id;
    
    @PrePersist
    public void setIdToUUID() {
        if (id == null) {
            id = UUID.randomUUID().toString();
        }
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
    }
    
    // ==================== 关联边 ====================
    
    /**
     * 关联的流程边
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "edge_id", nullable = false, columnDefinition = "varchar(36)")
    private ProcessEdge processEdge;
    
    // ==================== 属性信息 ====================
    
    /**
     * 属性键
     * 如 "condition", "priority", "weight", "description"
     */
    @Column(name = "property_key", nullable = false, length = 100)
    private String propertyKey;
    
    /**
     * 属性名称（显示名称）
     */
    @Column(name = "property_name", length = 200)
    private String propertyName;
    
    /**
     * 属性类型
     * string, number, boolean, date, json, array, object, expression
     */
    @Column(name = "property_type", nullable = false, length = 50)
    private String propertyType = "string";
    
    /**
     * 属性值（字符串格式）
     */
    @Column(name = "property_value", columnDefinition = "text")
    private String propertyValue;
    
    /**
     * 属性值（JSON格式，用于复杂类型）
     * 
     * 结构示例：
     * {
     *   "value": {...},
     *   "metadata": {
     *     "source": "user_input",
     *     "validatedAt": "2025-01-20T10:00:00Z"
     *   }
     * }
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "property_value_json", columnDefinition = "jsonb")
    private java.util.Map<String, Object> propertyValueJson;
    
    /**
     * 属性描述
     */
    @Column(name = "description", columnDefinition = "text")
    private String description;
    
    // ==================== 验证规则 ====================
    
    /**
     * 是否必填
     */
    @Column(name = "required")
    private Boolean required = false;
    
    /**
     * 默认值
     */
    @Column(name = "default_value", columnDefinition = "text")
    private String defaultValue;
    
    /**
     * 验证规则（JSON格式）
     * 
     * 结构示例：
     * {
     *   "min": 0,
     *   "max": 100,
     *   "pattern": "^[A-Za-z0-9]+$",
     *   "enum": ["high", "medium", "low"]
     * }
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "validation_rules", columnDefinition = "jsonb")
    private java.util.Map<String, Object> validationRules;
    
    // ==================== 显示配置 ====================
    
    /**
     * 是否可见
     */
    @Column(name = "visible")
    private Boolean visible = true;
    
    /**
     * 是否可编辑
     */
    @Column(name = "editable")
    private Boolean editable = true;
    
    /**
     * 排序顺序
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    /**
     * 分组名称（用于属性分组显示）
     */
    @Column(name = "group_name", length = 100)
    private String groupName;
    
    /**
     * UI组件类型
     * input, textarea, select, checkbox, radio, expression-editor
     */
    @Column(name = "ui_component", length = 50)
    private String uiComponent = "input";
    
    /**
     * UI组件配置（JSON格式）
     * 
     * 结构示例：
     * {
     *   "placeholder": "请输入条件表达式...",
     *   "language": "juel",
     *   "options": [
     *     {"label": "高", "value": "high"},
     *     {"label": "中", "value": "medium"},
     *     {"label": "低", "value": "low"}
     *   ]
     * }
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "ui_config", columnDefinition = "jsonb")
    private java.util.Map<String, Object> uiConfig;
    
    // ==================== 元数据 ====================
    
    /**
     * 属性来源
     * system, user, import, calculated
     */
    @Column(name = "source", length = 50)
    private String source = "user";
    
    /**
     * 是否为系统属性（系统属性不可删除）
     */
    @Column(name = "is_system")
    private Boolean isSystem = false;
    
    /**
     * 扩展元数据（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private java.util.Map<String, Object> metadata;
    
    // ==================== 审计信息 ====================
    
    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    /**
     * 创建人
     */
    @Column(name = "created_by", length = 100)
    private String createdBy;
}

