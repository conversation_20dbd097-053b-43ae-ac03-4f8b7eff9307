package com.bybk.baikeyun.api.process.entities;

import io.quarkus.hibernate.reactive.panache.PanacheEntityBase;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * 边版本实体类
 * 
 * <AUTHOR>
 * @version 4.0
 * @since 2025-01-20
 * @implNote 存储边的版本快照，支持边级别的版本追踪和对比
 */
@Getter
@Setter
@Entity
@Table(name = "process_edge_version", schema = "processhub")
@NamedQuery(name = "ProcessEdgeVersion.findByEdgeId",
            query = "SELECT ev FROM ProcessEdgeVersion ev WHERE ev.edgeId = :edgeId ORDER BY ev.versionNumber DESC")
@NamedQuery(name = "ProcessEdgeVersion.findByProcessVersion",
            query = "SELECT ev FROM ProcessEdgeVersion ev WHERE ev.processVersionId = :versionId")
public class ProcessEdgeVersion extends PanacheEntityBase {
    
    // ==================== 主键 ====================
    
    @Id
    @Column(name = "id", columnDefinition = "varchar(36)")
    private String id;
    
    @PrePersist
    public void setIdToUUID() {
        if (id == null) {
            id = UUID.randomUUID().toString();
        }
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
    }
    
    // ==================== 关联信息 ====================
    
    /**
     * 边ID
     */
    @Column(name = "edge_id", nullable = false, columnDefinition = "varchar(36)")
    private String edgeId;
    
    /**
     * 流程版本ID
     */
    @Column(name = "process_version_id", nullable = false, columnDefinition = "varchar(36)")
    private String processVersionId;
    
    // ==================== 版本信息 ====================
    
    /**
     * 版本号
     */
    @Column(name = "version_number", nullable = false)
    private Integer versionNumber;
    
    /**
     * 版本标签
     */
    @Column(name = "version_tag", length = 50)
    private String versionTag;
    
    // ==================== 边快照 ====================
    
    /**
     * 边键
     */
    @Column(name = "edge_key", nullable = false, length = 100)
    private String edgeKey;
    
    /**
     * 边类型
     */
    @Column(name = "edge_type", nullable = false, length = 50)
    private String edgeType;
    
    /**
     * 源节点
     */
    @Column(name = "from_node", nullable = false, length = 100)
    private String fromNode;
    
    /**
     * 目标节点
     */
    @Column(name = "to_node", nullable = false, length = 100)
    private String toNode;
    
    /**
     * 标签
     */
    @Column(name = "label", length = 200)
    private String label;
    
    // ==================== 属性快照 ====================
    
    /**
     * 边属性快照（JSON格式）
     * 
     * 结构示例：
     * {
     *   "conditionExpression": "${amount > 10000}",
     *   "priority": 1
     * }
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "properties", columnDefinition = "jsonb")
    private Map<String, Object> properties;
    
    // ==================== 样式快照 ====================
    
    /**
     * 边样式快照（JSON格式）
     * 
     * 结构示例：
     * {
     *   "stroke": "#1890ff",
     *   "strokeWidth": 2,
     *   "strokeDasharray": "5,5"
     * }
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "style", columnDefinition = "jsonb")
    private Map<String, Object> style;
    
    // ==================== 变更信息 ====================
    
    /**
     * 变更类型
     * add: 新增边
     * modify: 修改边
     * delete: 删除边
     */
    @Column(name = "change_type", length = 20)
    private String changeType;
    
    /**
     * 变更描述
     */
    @Column(name = "change_description", columnDefinition = "text")
    private String changeDescription;
    
    /**
     * 变更字段详情（JSON格式）
     * 
     * 结构示例：
     * {
     *   "conditionExpression": {
     *     "old": "${amount > 5000}",
     *     "new": "${amount > 10000}"
     *   }
     * }
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "changed_fields", columnDefinition = "jsonb")
    private Map<String, Object> changedFields;
    
    // ==================== 审计信息 ====================
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 创建人ID
     */
    @Column(name = "created_by", length = 100)
    private String createdBy;
    
    // ==================== 关联关系 ====================
    
    /**
     * 关联的边
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "edge_id", insertable = false, updatable = false)
    private ProcessEdge edge;
    
    /**
     * 关联的流程版本
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "process_version_id", insertable = false, updatable = false)
    private ProcessVersion processVersion;
}

