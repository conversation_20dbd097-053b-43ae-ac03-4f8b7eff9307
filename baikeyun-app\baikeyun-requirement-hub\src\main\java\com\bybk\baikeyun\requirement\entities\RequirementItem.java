package com.bybk.baikeyun.requirement.entities;

import io.quarkus.hibernate.reactive.panache.PanacheEntityBase;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 需求子项实体类
 * 
 * 需求的具体子项，形成主子结构，类似流程工厂的ProcessNode
 * 
 * 主子关系:
 * - Requirement (主表) → RequirementItem (子表)
 * - RequirementItem (父) → RequirementItem (子) - 自关联形成层级
 * 
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */
@Entity
@Table(name = "requirement_item", schema = "requirement_hub")
@Getter
@Setter
public class RequirementItem extends PanacheEntityBase {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "id", length = 36, nullable = false)
    private String id;

    // ==================== 需求关联 ====================

    /**
     * 所属需求ID（主表关联）
     */
    @Column(name = "requirement_id", length = 36, nullable = false)
    private String requirementId;

    // ==================== 基本信息 ====================

    /**
     * 子项编码
     */
    @Column(name = "item_code", length = 50, nullable = false)
    private String itemCode;

    /**
     * 子项标题
     */
    @Column(name = "item_title", length = 500, nullable = false)
    private String itemTitle;

    /**
     * 子项描述
     */
    @Column(name = "item_description", columnDefinition = "TEXT")
    private String itemDescription;

    // ==================== 子项分类 ====================

    /**
     * 子项类型: task, subtask, checklist, acceptance_criteria
     */
    @Column(name = "item_type", length = 50, nullable = false)
    private String itemType;

    /**
     * 子项类别: functional, technical, test, deployment
     */
    @Column(name = "item_category", length = 50)
    private String itemCategory;

    // ==================== 层级关系 ====================

    /**
     * 父子项ID（自关联）
     */
    @Column(name = "parent_item_id", length = 36)
    private String parentItemId;

    /**
     * 根子项ID
     */
    @Column(name = "root_item_id", length = 36)
    private String rootItemId;

    /**
     * 层级深度（0表示根节点）
     */
    @Column(name = "level")
    private Integer level = 0;

    /**
     * 层级路径: /item1/item2/item3
     */
    @Column(name = "path", length = 500)
    private String path;

    /**
     * 同级排序
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    // ==================== 业务属性 ====================

    /**
     * 状态: pending, in_progress, completed, blocked, cancelled
     */
    @Column(name = "status", length = 20)
    private String status = "pending";

    /**
     * 优先级: P0, P1, P2, P3
     */
    @Column(name = "priority", length = 10)
    private String priority = "P2";

    // ==================== 责任人 ====================

    /**
     * 负责人
     */
    @Column(name = "assigned_to", length = 100)
    private String assignedTo;

    /**
     * 评审人
     */
    @Column(name = "reviewer", length = 100)
    private String reviewer;

    // ==================== 工作量 ====================

    /**
     * 预估工时
     */
    @Column(name = "estimated_hours", precision = 10, scale = 2)
    private BigDecimal estimatedHours;

    /**
     * 实际工时
     */
    @Column(name = "actual_hours", precision = 10, scale = 2)
    private BigDecimal actualHours;

    // ==================== 时间计划 ====================

    /**
     * 计划开始日期
     */
    @Column(name = "planned_start_date")
    private LocalDate plannedStartDate;

    /**
     * 计划结束日期
     */
    @Column(name = "planned_end_date")
    private LocalDate plannedEndDate;

    /**
     * 实际开始日期
     */
    @Column(name = "actual_start_date")
    private LocalDate actualStartDate;

    /**
     * 实际结束日期
     */
    @Column(name = "actual_end_date")
    private LocalDate actualEndDate;

    // ==================== 工业场景 ====================

    /**
     * 车间ID
     */
    @Column(name = "workshop_id", length = 50)
    private String workshopId;

    /**
     * 产线ID
     */
    @Column(name = "production_line_id", length = 50)
    private String productionLineId;

    /**
     * 设备ID列表（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "device_ids", columnDefinition = "jsonb")
    private Map<String, Object> deviceIds;

    /**
     * 工艺步骤
     */
    @Column(name = "process_step", length = 100)
    private String processStep;

    /**
     * 数据采集点（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "data_points", columnDefinition = "jsonb")
    private Map<String, Object> dataPoints;

    // ==================== 技术属性 ====================

    /**
     * 技术细节（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "technical_details", columnDefinition = "jsonb")
    private Map<String, Object> technicalDetails;

    /**
     * 验收标准（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "acceptance_criteria", columnDefinition = "jsonb")
    private Map<String, Object> acceptanceCriteria;

    /**
     * 测试用例（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "test_cases", columnDefinition = "jsonb")
    private Map<String, Object> testCases;

    // ==================== 依赖关系 ====================

    /**
     * 依赖的其他子项ID列表（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "dependencies", columnDefinition = "jsonb")
    private Map<String, Object> dependencies;

    /**
     * 阻塞因素（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "blockers", columnDefinition = "jsonb")
    private Map<String, Object> blockers;

    // ==================== 审计信息 ====================

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    /**
     * 创建人
     */
    @Column(name = "created_by", length = 100)
    private String createdBy;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 更新人
     */
    @Column(name = "updated_by", length = 100)
    private String updatedBy;

    // ==================== 扩展信息 ====================

    /**
     * 扩展元数据（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private Map<String, Object> metadata;

    // ==================== 关联关系 ====================

    /**
     * 所属需求（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "requirement_id", insertable = false, updatable = false)
    private Requirement requirement;

    /**
     * 父子项（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_item_id", insertable = false, updatable = false)
    private RequirementItem parentItem;

    /**
     * 子子项列表（一对多关系）
     */
    @OneToMany(mappedBy = "parentItem", fetch = FetchType.LAZY)
    private List<RequirementItem> childItems;

    // ==================== 生命周期回调 ====================

    /**
     * 持久化前自动生成ID
     */
    @PrePersist
    public void prePersist() {
        if (this.id == null) {
            this.id = UUID.randomUUID().toString();
        }
        if (this.createdAt == null) {
            this.createdAt = LocalDateTime.now();
        }
        if (this.updatedAt == null) {
            this.updatedAt = LocalDateTime.now();
        }
    }

    /**
     * 更新前自动更新时间
     */
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}

