2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.redis.max-pool-size" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.redis.max-pool-waiting" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.http.ssl.certificate.key-store-type" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.log.category."com.bybk.baikeyun.app"" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.log.category."io.vertx.pgclient"" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.log.console.charset" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.log.category."org.hibernate.type.descriptor.sql.BasicBinder"" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.redis.reconnect-interval" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.redis.timeout" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.console.enable" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.redis.client-name" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.file.format" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.file.rotation.max-backup-index" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.file.rotation.file-suffix" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.log.file.charset" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.redis.password" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.redis.database" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.log.category."org.baikeyun".appenders" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.file.rotation.rotate-on-boot" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.log.console.encoding" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.file.level" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.log.category."org.hibernate.SQL"" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.console.format" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.log.category."io.quarkus.reactive.pg.client"" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.redis.reconnect-attempts" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.log.category."org.hibernate.tool.schema"" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.file.enable" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.log.log4j2.configurationFile" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.redis.hosts" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:05 null.null(null)evel [Quarkus Main Thread] null.null(null)ogger{36} - Unrecognized configuration key "quarkus.file.path" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typosg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "uk_edge_type_property_key" of relation "edge_property_template" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "uk_edge_property_key" of relation "edge_property_value" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "idx_edge_type_code" of relation "edge_type_definition" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "uk_node_type_property_key" of relation "node_property_template" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "uk_node_property_key" of relation "node_property_value" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "idx_node_type_code" of relation "node_type_definition" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "uk_definition_key_version" of relation "process_definition" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions(SqlExceptionHelper.java:145)evel [vert.x-eventloop-thread-0] org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions(SqlExceptionHelper.java:145)ogger{36} - SQL Error: 0, SQLState: 42P07sg
2025-10-08 21:28:09 org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions(SqlExceptionHelper.java:150)evel [vert.x-eventloop-thread-0] org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions(SqlExceptionHelper.java:150)ogger{36} - ERROR: relation "idx_property_key" already exists (42P07)sg
2025-10-08 21:28:09 org.hibernate.reactive.provider.service.ReactiveGenerationTarget.logCommandFailure(ReactiveGenerationTarget.java:107)evel [vert.x-eventloop-thread-0] org.hibernate.reactive.provider.service.ReactiveGenerationTarget.logCommandFailure(ReactiveGenerationTarget.java:107)ogger{36} - HR000021: DDL command failed [org.hibernate.exception.SQLGrammarException: error executing SQL statement [ERROR: relation "idx_property_key" already exists (42P07)] [
    create index idx_property_key 
       on processhub.process_node_property (property_key)]]sg
2025-10-08 21:28:09 org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions(SqlExceptionHelper.java:145)evel [vert.x-eventloop-thread-0] org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions(SqlExceptionHelper.java:145)ogger{36} - SQL Error: 0, SQLState: 42P07sg
2025-10-08 21:28:09 org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions(SqlExceptionHelper.java:150)evel [vert.x-eventloop-thread-0] org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions(SqlExceptionHelper.java:150)ogger{36} - ERROR: relation "idx_property_type" already exists (42P07)sg
2025-10-08 21:28:09 org.hibernate.reactive.provider.service.ReactiveGenerationTarget.logCommandFailure(ReactiveGenerationTarget.java:107)evel [vert.x-eventloop-thread-0] org.hibernate.reactive.provider.service.ReactiveGenerationTarget.logCommandFailure(ReactiveGenerationTarget.java:107)ogger{36} - HR000021: DDL command failed [org.hibernate.exception.SQLGrammarException: error executing SQL statement [ERROR: relation "idx_property_type" already exists (42P07)] [
    create index idx_property_type 
       on processhub.process_node_property (property_type)]]sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "uk_parent_child_node_dep" of relation "process_subprocess_dependency" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "uk_parent_child_node" of relation "process_subprocess_relation" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions(SqlExceptionHelper.java:145)evel [vert.x-eventloop-thread-0] org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions(SqlExceptionHelper.java:145)ogger{36} - SQL Error: 0, SQLState: 42P07sg
2025-10-08 21:28:09 org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions(SqlExceptionHelper.java:150)evel [vert.x-eventloop-thread-0] org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions(SqlExceptionHelper.java:150)ogger{36} - ERROR: relation "idx_created_at" already exists (42P07)sg
2025-10-08 21:28:09 org.hibernate.reactive.provider.service.ReactiveGenerationTarget.logCommandFailure(ReactiveGenerationTarget.java:107)evel [vert.x-eventloop-thread-0] org.hibernate.reactive.provider.service.ReactiveGenerationTarget.logCommandFailure(ReactiveGenerationTarget.java:107)ogger{36} - HR000021: DDL command failed [org.hibernate.exception.SQLGrammarException: error executing SQL statement [ERROR: relation "idx_created_at" already exists (42P07)] [
    create index idx_created_at 
       on processhub.process_version (created_at)]]sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "ukpyadn2e3xipo328gs8ae6sh11" of relation "project" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "uk_member_user" of relation "project_member" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "uk_milestone_code" of relation "project_milestone" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "uk_sprint_code" of relation "project_sprint" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "uk_task_code" of relation "project_task" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "ukn6pc0prpxmyjh6rn36gjoftbr" of relation "requirement" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "uke6277e99vdh6lhyi9wk13qhnn" of relation "requirement_change" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "uk5fm4i229ke8a5rlpul7dcu5dp" of relation "requirement_project" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "uk_requirement_relation" of relation "requirement_relation" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "ukdhrwiixpiihv970kgh80lu4w6" of relation "requirement_review" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 null.null(null)evel [vert.x-eventloop-thread-0] null.null(null)ogger{36} - Backend notice: severity='NOTICE', code='00000', message='constraint "uk_requirement_version" of relation "requirement_version" does not exist, skipping', detail='null', hint='null', position='null', internalPosition='null', internalQuery='null', where='null', file='tablecmds.c', line='12655', routine='ATExecDropConstraint', schema='null', table='null', column='null', dataType='null', constraint='null'sg
2025-10-08 21:28:09 io.quarkus.scheduler.runtime.SimpleScheduler.<init>(SimpleScheduler.java:104)evel [Quarkus Main Thread] io.quarkus.scheduler.runtime.SimpleScheduler.<init>(SimpleScheduler.java:104)ogger{36} - No scheduled business methods found - Simple scheduler will not be startedsg
2025-10-08 21:28:10 io.quarkus.bootstrap.runner.Timing.printStartupTime(Timing.java:109)evel [Quarkus Main Thread] io.quarkus.bootstrap.runner.Timing.printStartupTime(Timing.java:109)ogger{36} - baikeyun-app-boot 0.1.0 on JVM (powered by Quarkus 3.18.1) started in 14.885s. Listening on: http://localhost:8085 and https://localhost:8447sg
2025-10-08 21:28:10 io.quarkus.bootstrap.runner.Timing.printStartupTime(Timing.java:113)evel [Quarkus Main Thread] io.quarkus.bootstrap.runner.Timing.printStartupTime(Timing.java:113)ogger{36} - Profile dev activated. Live Coding activated.sg
2025-10-08 21:28:10 io.quarkus.bootstrap.runner.Timing.printStartupTime(Timing.java:115)evel [Quarkus Main Thread] io.quarkus.bootstrap.runner.Timing.printStartupTime(Timing.java:115)ogger{36} - Installed features: [agroal, cdi, config-yaml, flyway, hibernate-orm, hibernate-orm-panache, hibernate-reactive, hibernate-reactive-panache, hibernate-validator, jdbc-postgresql, narayana-jta, reactive-pg-client, reactive-routes, rest, rest-jackson, scheduler, security, security-jpa, smallrye-context-propagation, smallrye-jwt, smallrye-openapi, swagger-ui, vertx]sg
