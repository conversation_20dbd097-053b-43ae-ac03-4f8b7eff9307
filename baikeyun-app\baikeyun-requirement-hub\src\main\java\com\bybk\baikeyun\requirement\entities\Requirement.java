package com.bybk.baikeyun.requirement.entities;

import io.quarkus.hibernate.reactive.panache.PanacheEntityBase;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 需求实体类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-20
 * @implNote 工业信息化项目的需求管理核心实体
 */
@Getter
@Setter
@Entity
@Table(name = "requirement", schema = "requirement_hub")
@NamedQuery(name = "Requirement.findByCode",
            query = "SELECT r FROM Requirement r WHERE r.requirementCode = :code")
@NamedQuery(name = "Requirement.findByProject",
            query = "SELECT r FROM Requirement r WHERE r.projectId = :projectId")
@NamedQuery(name = "Requirement.findByStatus",
            query = "SELECT r FROM Requirement r WHERE r.status = :status")
public class Requirement extends PanacheEntityBase {
    
    // ==================== 主键 ====================
    
    @Id
    @Column(name = "id", columnDefinition = "varchar(36)")
    private String id;
    
    @PrePersist
    public void setIdToUUID() {
        if (id == null) {
            id = UUID.randomUUID().toString();
        }
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (updatedAt == null) {
            updatedAt = LocalDateTime.now();
        }
    }
    
    // ==================== 基本信息 ====================
    
    /**
     * 需求编号（自动生成）
     * 格式: REQ-2025-0001
     */
    @Column(name = "requirement_code", nullable = false, unique = true, length = 50)
    private String requirementCode;
    
    /**
     * 需求标题
     */
    @Column(name = "title", nullable = false, length = 500)
    private String title;
    
    /**
     * 需求描述
     */
    @Column(name = "description", columnDefinition = "text")
    private String description;
    
    // ==================== 项目关联 ====================
    
    /**
     * 项目ID
     */
    @Column(name = "project_id", nullable = false, columnDefinition = "varchar(36)")
    private String projectId;
    
    // ==================== 需求分类 ====================
    
    /**
     * 需求类型
     * epic: 史诗
     * feature: 特性
     * story: 用户故事
     * task: 任务
     * defect: 缺陷
     * change_request: 变更请求
     */
    @Column(name = "requirement_type", nullable = false, length = 50)
    private String requirementType;
    
    /**
     * 需求分类
     * business: 业务需求
     * functional: 功能需求
     * non_functional: 非功能需求
     * data: 数据需求
     */
    @Column(name = "requirement_category", length = 50)
    private String requirementCategory;
    
    // ==================== 层级关系 ====================
    
    /**
     * 父需求ID
     */
    @Column(name = "parent_id", columnDefinition = "varchar(36)")
    private String parentId;
    
    /**
     * 根需求ID
     */
    @Column(name = "root_id", columnDefinition = "varchar(36)")
    private String rootId;
    
    /**
     * 需求层级
     */
    @Column(name = "level")
    private Integer level = 0;
    
    /**
     * 需求路径
     * 格式: /root/parent/current
     */
    @Column(name = "path", length = 500)
    private String path;
    
    // ==================== 业务属性 ====================
    
    /**
     * 业务价值
     * high: 高
     * medium: 中
     * low: 低
     */
    @Column(name = "business_value", length = 20)
    private String businessValue = "medium";
    
    /**
     * 优先级
     * P0: 最高
     * P1: 高
     * P2: 中
     * P3: 低
     */
    @Column(name = "priority", length = 10)
    private String priority = "P2";
    
    /**
     * 紧急程度
     * urgent: 紧急
     * important: 重要
     * normal: 普通
     */
    @Column(name = "urgency", length = 20)
    private String urgency = "normal";
    
    /**
     * 复杂度
     * simple: 简单
     * medium: 中等
     * complex: 复杂
     */
    @Column(name = "complexity", length = 20)
    private String complexity = "medium";
    
    // ==================== 工业场景 ====================
    
    /**
     * 车间ID
     */
    @Column(name = "workshop_id", columnDefinition = "varchar(36)")
    private String workshopId;
    
    /**
     * 车间名称
     */
    @Column(name = "workshop_name", length = 200)
    private String workshopName;
    
    /**
     * 产线ID
     */
    @Column(name = "production_line_id", columnDefinition = "varchar(36)")
    private String productionLineId;
    
    /**
     * 产线名称
     */
    @Column(name = "production_line_name", length = 200)
    private String productionLineName;
    
    /**
     * 关联设备列表（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "device_ids", columnDefinition = "jsonb")
    private Map<String, Object> deviceIds;
    
    /**
     * 关联工艺流程（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "process_flow", columnDefinition = "jsonb")
    private Map<String, Object> processFlow;
    
    /**
     * 数据采集点（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "data_points", columnDefinition = "jsonb")
    private Map<String, Object> dataPoints;
    
    /**
     * 接口规范（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "interface_spec", columnDefinition = "jsonb")
    private Map<String, Object> interfaceSpec;
    
    // ==================== 工作量估算 ====================
    
    /**
     * 估算工时
     */
    @Column(name = "estimated_hours")
    private Double estimatedHours;
    
    /**
     * 实际工时
     */
    @Column(name = "actual_hours")
    private Double actualHours;
    
    /**
     * 故事点
     */
    @Column(name = "story_points")
    private Integer storyPoints;
    
    /**
     * 成本估算
     */
    @Column(name = "cost_estimate", precision = 15, scale = 2)
    private BigDecimal costEstimate;
    
    // ==================== 状态管理 ====================
    
    /**
     * 当前状态
     * draft: 草稿
     * pending_review: 待评审
     * in_review: 评审中
     * approved: 已批准
     * in_development: 开发中
     * dev_complete: 开发完成
     * in_testing: 测试中
     * test_complete: 测试完成
     * pending_acceptance: 待验收
     * accepted: 已验收
     * closed: 已关闭
     * rejected: 已拒绝
     * cancelled: 已取消
     * on_hold: 已搁置
     */
    @Column(name = "status", length = 30)
    private String status = "draft";
    
    /**
     * 状态变更原因
     */
    @Column(name = "status_reason", columnDefinition = "text")
    private String statusReason;
    
    // ==================== 人员分配 ====================
    
    /**
     * 需求提出人
     */
    @Column(name = "proposer", length = 100)
    private String proposer;
    
    /**
     * 产品负责人
     */
    @Column(name = "product_owner", length = 100)
    private String productOwner;
    
    /**
     * 开发负责人
     */
    @Column(name = "dev_owner", length = 100)
    private String devOwner;
    
    /**
     * 测试负责人
     */
    @Column(name = "test_owner", length = 100)
    private String testOwner;
    
    /**
     * 验收人
     */
    @Column(name = "acceptor", length = 100)
    private String acceptor;
    
    // ==================== 时间管理 ====================
    
    /**
     * 计划开始日期
     */
    @Column(name = "planned_start_date")
    private LocalDate plannedStartDate;
    
    /**
     * 计划结束日期
     */
    @Column(name = "planned_end_date")
    private LocalDate plannedEndDate;
    
    /**
     * 实际开始日期
     */
    @Column(name = "actual_start_date")
    private LocalDate actualStartDate;
    
    /**
     * 实际结束日期
     */
    @Column(name = "actual_end_date")
    private LocalDate actualEndDate;
    
    /**
     * 里程碑
     */
    @Column(name = "milestone", length = 100)
    private String milestone;
    
    // ==================== 验收标准 ====================
    
    /**
     * 验收标准
     */
    @Column(name = "acceptance_criteria", columnDefinition = "text")
    private String acceptanceCriteria;
    
    /**
     * 测试用例（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "test_cases", columnDefinition = "jsonb")
    private Map<String, Object> testCases;
    
    // ==================== 附加信息 ====================
    
    /**
     * 标签列表（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "tags", columnDefinition = "jsonb")
    private Map<String, Object> tags;
    
    // ==================== 审计信息 ====================
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 创建人
     */
    @Column(name = "created_by", length = 100)
    private String createdBy;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 更新人
     */
    @Column(name = "updated_by", length = 100)
    private String updatedBy;
    
    // ==================== 扩展信息 ====================
    
    /**
     * 扩展元数据（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private Map<String, Object> metadata;
    
    // ==================== 关联关系 ====================
    
    /**
     * 关联的项目
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", insertable = false, updatable = false)
    private RequirementProject project;
    
    /**
     * 父需求
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", insertable = false, updatable = false)
    private Requirement parent;

    /**
     * 子需求列表（一对多关系）
     *
     * 注意：使用懒加载，避免一次性加载大量数据
     * 建议使用响应式查询：
     * Requirement.find("parent_id", requirementId).list()
     */
    @OneToMany(mappedBy = "parent", fetch = FetchType.LAZY)
    private List<Requirement> children;

    /**
     * 需求变更列表（一对多关系）
     */
    @OneToMany(mappedBy = "requirement", fetch = FetchType.LAZY)
    private List<RequirementChange> changes;

    /**
     * 需求版本列表（一对多关系）
     */
    @OneToMany(mappedBy = "requirement", fetch = FetchType.LAZY)
    private List<RequirementVersion> versions;

    /**
     * 需求追溯列表（一对多关系）
     */
    @OneToMany(mappedBy = "requirement", fetch = FetchType.LAZY)
    private List<RequirementTrace> traces;

    /**
     * 需求附件列表（一对多关系）
     */
    @OneToMany(mappedBy = "requirement", fetch = FetchType.LAZY)
    private List<RequirementAttachment> attachments;

    /**
     * 需求评论列表（一对多关系）
     */
    @OneToMany(mappedBy = "requirement", fetch = FetchType.LAZY)
    private List<RequirementComment> comments;

    /**
     * 需求状态历史列表（一对多关系）
     */
    @OneToMany(mappedBy = "requirement", fetch = FetchType.LAZY)
    private List<RequirementStatusHistory> statusHistories;

    /**
     * 需求子项列表（一对多关系）
     *
     * 这是主子结构的核心关联，类似ProcessDefinition → ProcessNode
     *
     * 注意：使用懒加载，避免一次性加载大量数据
     * 建议使用响应式查询：
     * RequirementItem.find("requirement_id = ?1 ORDER BY path", requirementId).list()
     */
    @OneToMany(mappedBy = "requirement", fetch = FetchType.LAZY)
    private List<RequirementItem> items;

    // ==================== 统计字段 ====================

    /**
     * 需求子项总数
     */
    @Column(name = "total_items")
    private Integer totalItems = 0;

    /**
     * 已完成的需求子项数
     */
    @Column(name = "completed_items")
    private Integer completedItems = 0;
}

