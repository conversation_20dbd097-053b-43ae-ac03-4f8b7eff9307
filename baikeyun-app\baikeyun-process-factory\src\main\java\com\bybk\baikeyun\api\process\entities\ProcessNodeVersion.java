package com.bybk.baikeyun.api.process.entities;

import io.quarkus.hibernate.reactive.panache.PanacheEntityBase;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * 节点版本实体类
 * 
 * <AUTHOR>
 * @version 4.0
 * @since 2025-01-20
 * @implNote 存储节点的版本快照，支持节点级别的版本追踪和对比
 */
@Getter
@Setter
@Entity
@Table(name = "process_node_version", schema = "processhub")
@NamedQuery(name = "ProcessNodeVersion.findByNodeId",
            query = "SELECT nv FROM ProcessNodeVersion nv WHERE nv.nodeId = :nodeId ORDER BY nv.versionNumber DESC")
@NamedQuery(name = "ProcessNodeVersion.findByProcessVersion",
            query = "SELECT nv FROM ProcessNodeVersion nv WHERE nv.processVersionId = :versionId")
public class ProcessNodeVersion extends PanacheEntityBase {
    
    // ==================== 主键 ====================
    
    @Id
    @Column(name = "id", columnDefinition = "varchar(36)")
    private String id;
    
    @PrePersist
    public void setIdToUUID() {
        if (id == null) {
            id = UUID.randomUUID().toString();
        }
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
    }
    
    // ==================== 关联信息 ====================
    
    /**
     * 节点ID
     */
    @Column(name = "node_id", nullable = false, columnDefinition = "varchar(36)")
    private String nodeId;
    
    /**
     * 流程版本ID
     */
    @Column(name = "process_version_id", nullable = false, columnDefinition = "varchar(36)")
    private String processVersionId;
    
    // ==================== 版本信息 ====================
    
    /**
     * 版本号
     */
    @Column(name = "version_number", nullable = false)
    private Integer versionNumber;
    
    /**
     * 版本标签
     */
    @Column(name = "version_tag", length = 50)
    private String versionTag;
    
    // ==================== 节点快照 ====================
    
    /**
     * 节点键
     */
    @Column(name = "node_key", nullable = false, length = 100)
    private String nodeKey;
    
    /**
     * 节点类型
     */
    @Column(name = "node_type", nullable = false, length = 50)
    private String nodeType;
    
    /**
     * 节点名称
     */
    @Column(name = "name", nullable = false, length = 200)
    private String name;
    
    /**
     * 节点描述
     */
    @Column(name = "description", columnDefinition = "text")
    private String description;
    
    /**
     * X坐标
     */
    @Column(name = "position_x")
    private Double positionX;
    
    /**
     * Y坐标
     */
    @Column(name = "position_y")
    private Double positionY;
    
    /**
     * 宽度
     */
    @Column(name = "width")
    private Double width;
    
    /**
     * 高度
     */
    @Column(name = "height")
    private Double height;
    
    /**
     * 是否为分组
     */
    @Column(name = "is_group")
    private Boolean isGroup = false;
    
    /**
     * 分组键
     */
    @Column(name = "group_key", length = 100)
    private String groupKey;
    
    /**
     * 分类
     */
    @Column(name = "category", length = 50)
    private String category;
    
    // ==================== 属性快照 ====================
    
    /**
     * 节点属性快照（JSON格式）
     * 
     * 结构示例：
     * {
     *   "assignee": "user1",
     *   "candidateUsers": ["user2", "user3"],
     *   "priority": "high",
     *   "formKey": "approval_form"
     * }
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "properties", columnDefinition = "jsonb")
    private Map<String, Object> properties;
    
    // ==================== 样式快照 ====================
    
    /**
     * 节点样式快照（JSON格式）
     * 
     * 结构示例：
     * {
     *   "fill": "#1890ff",
     *   "stroke": "#0050b3",
     *   "strokeWidth": 2
     * }
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "style", columnDefinition = "jsonb")
    private Map<String, Object> style;
    
    // ==================== 变更信息 ====================
    
    /**
     * 变更类型
     * add: 新增节点
     * modify: 修改节点
     * delete: 删除节点
     * move: 移动节点
     */
    @Column(name = "change_type", length = 20)
    private String changeType;
    
    /**
     * 变更描述
     */
    @Column(name = "change_description", columnDefinition = "text")
    private String changeDescription;
    
    /**
     * 变更字段详情（JSON格式）
     * 
     * 结构示例：
     * {
     *   "assignee": {
     *     "old": "user1",
     *     "new": "user2"
     *   },
     *   "priority": {
     *     "old": null,
     *     "new": "high"
     *   }
     * }
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "changed_fields", columnDefinition = "jsonb")
    private Map<String, Object> changedFields;
    
    // ==================== 审计信息 ====================
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 创建人ID
     */
    @Column(name = "created_by", length = 100)
    private String createdBy;
    
    // ==================== 关联关系 ====================
    
    /**
     * 关联的节点
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "node_id", insertable = false, updatable = false)
    private ProcessNode node;
    
    /**
     * 关联的流程版本
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "process_version_id", insertable = false, updatable = false)
    private ProcessVersion processVersion;
}

