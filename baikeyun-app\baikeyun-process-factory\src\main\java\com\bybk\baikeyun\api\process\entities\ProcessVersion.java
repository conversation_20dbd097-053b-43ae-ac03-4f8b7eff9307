package com.bybk.baikeyun.api.process.entities;

import io.quarkus.hibernate.reactive.panache.PanacheEntityBase;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * 流程版本实体类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-20
 * @implNote 存储流程定义的版本历史，支持版本对比、回滚等功能
 */
@Getter
@Setter
@Entity
@Table(name = "process_version", schema = "processhub",
       indexes = {
           @Index(name = "idx_definition_version", columnList = "definition_id,version_number"),
           @Index(name = "idx_version_tag", columnList = "version_tag"),
           @Index(name = "idx_version_created_at", columnList = "created_at")
       })
@NamedQuery(name = "ProcessVersion.findByDefinitionId",
            query = "SELECT v FROM ProcessVersion v WHERE v.processDefinition.id = :definitionId ORDER BY v.versionNumber DESC")
@NamedQuery(name = "ProcessVersion.findByVersionTag",
            query = "SELECT v FROM ProcessVersion v WHERE v.versionTag = :versionTag")
public class ProcessVersion extends PanacheEntityBase {
    
    // ==================== 主键 ====================
    
    @Id
    @Column(name = "id", columnDefinition = "varchar(36)")
    private String id;
    
    @PrePersist
    public void setIdToUUID() {
        if (id == null) {
            id = UUID.randomUUID().toString();
        }
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
    }
    
    // ==================== 关联流程定义 ====================
    
    /**
     * 关联的流程定义
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "definition_id", nullable = false, columnDefinition = "varchar(36)")
    private ProcessDefinition processDefinition;
    
    // ==================== 版本信息 ====================
    
    /**
     * 版本号（从1开始递增）
     */
    @Column(name = "version_number", nullable = false)
    private Integer versionNumber;
    
    /**
     * 版本标签（语义化版本）
     * 如 "v1.0.0", "v2.1.3"
     */
    @Column(name = "version_tag", length = 50)
    private String versionTag;
    
    /**
     * 版本类型
     * major, minor, patch
     */
    @Column(name = "version_type", length = 20)
    private String versionType = "minor";
    
    /**
     * 版本描述/变更说明
     */
    @Column(name = "description", columnDefinition = "text")
    private String description;
    
    /**
     * 变更日志（JSON格式）
     * 
     * 结构示例：
     * {
     *   "changes": [
     *     {
     *       "type": "add",
     *       "target": "node",
     *       "nodeKey": "task_approve_1",
     *       "description": "添加审批节点"
     *     },
     *     {
     *       "type": "modify",
     *       "target": "edge",
     *       "edgeKey": "edge_1",
     *       "field": "condition",
     *       "oldValue": "${amount > 1000}",
     *       "newValue": "${amount > 5000}",
     *       "description": "修改条件表达式"
     *     }
     *   ]
     * }
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "change_log", columnDefinition = "jsonb")
    private Map<String, Object> changeLog;
    
    // ==================== 流程数据引用 ====================
    
    /**
     * 流程图数据引用ID（MongoDB文档ID）
     */
    @Column(name = "flow_data_ref", length = 100)
    private String flowDataRef;
    
    /**
     * 流程图数据大小（字节）
     */
    @Column(name = "flow_data_size")
    private Long flowDataSize;
    
    /**
     * 流程图数据校验和（MD5）
     */
    @Column(name = "flow_data_checksum", length = 64)
    private String flowDataChecksum;
    
    /**
     * 是否为完整快照（false表示增量存储）
     */
    @Column(name = "is_snapshot")
    private Boolean isSnapshot = true;
    
    /**
     * 基准版本ID（增量存储时指向基准版本）
     */
    @Column(name = "base_version_id", columnDefinition = "varchar(36)")
    private String baseVersionId;
    
    /**
     * 增量数据引用ID（MongoDB文档ID）
     */
    @Column(name = "delta_data_ref", length = 100)
    private String deltaDataRef;
    
    // ==================== 统计信息 ====================
    
    /**
     * 节点数量
     */
    @Column(name = "node_count")
    private Integer nodeCount = 0;
    
    /**
     * 边数量
     */
    @Column(name = "edge_count")
    private Integer edgeCount = 0;
    
    /**
     * 变更数量（相对于上一版本）
     */
    @Column(name = "change_count")
    private Integer changeCount = 0;
    
    // ==================== 审计信息 ====================
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 创建人ID
     */
    @Column(name = "created_by", nullable = false, length = 100)
    private String createdBy;
    
    /**
     * 创建人姓名
     */
    @Column(name = "created_by_name", length = 200)
    private String createdByName;
    
    /**
     * 备注
     */
    @Column(name = "remark", columnDefinition = "text")
    private String remark;
    
    // ==================== 扩展元数据 ====================

    /**
     * 扩展元数据（JSON格式）
     *
     * 结构示例：
     * {
     *   "commitHash": "a1b2c3d4",
     *   "branch": "feature/approval-flow",
     *   "tags": ["stable", "production"],
     *   "reviewers": ["user1", "user2"],
     *   "approvedAt": "2025-01-20T10:00:00Z"
     * }
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private Map<String, Object> metadata;

    // ==================== 主子流程版本管理 (v4.0) ====================

    /**
     * 父版本ID（版本继承关系）
     */
    @Column(name = "parent_version_id", columnDefinition = "varchar(36)")
    private String parentVersionId;

    /**
     * 是否为兼容性升级
     */
    @Column(name = "is_compatible_upgrade")
    private Boolean isCompatibleUpgrade = true;

    /**
     * 破坏性变更列表（JSON格式）
     *
     * 结构示例：
     * {
     *   "deletedNodes": ["node_1", "node_2"],
     *   "deletedProperties": [
     *     {"node": "node_3", "property": "oldParam"}
     *   ],
     *   "typeChanges": [
     *     {"node": "node_4", "oldType": "userTask", "newType": "serviceTask"}
     *   ]
     * }
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "breaking_changes", columnDefinition = "jsonb")
    private Map<String, Object> breakingChanges;

    /**
     * 版本迁移指南
     */
    @Column(name = "migration_guide", columnDefinition = "text")
    private String migrationGuide;

    /**
     * 版本状态
     * draft: 草稿
     * review: 审核中
     * testing: 测试中
     * published: 已发布
     * deprecated: 已废弃
     * archived: 已归档
     */
    @Column(name = "version_status", length = 20)
    private String versionStatus = "draft";

    /**
     * 发布时间
     */
    @Column(name = "published_at")
    private LocalDateTime publishedAt;

    /**
     * 废弃时间
     */
    @Column(name = "deprecated_at")
    private LocalDateTime deprecatedAt;

    /**
     * 生命周期结束时间
     */
    @Column(name = "end_of_life_at")
    private LocalDateTime endOfLifeAt;

    /**
     * 子流程依赖信息（JSON格式）
     *
     * 结构示例：
     * {
     *   "budget_approval": {
     *     "constraint": "^1.2.0",
     *     "locked": "1.2.3",
     *     "compatible": true
     *   },
     *   "tech_review": {
     *     "constraint": "latest",
     *     "locked": null,
     *     "compatible": true
     *   }
     * }
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "dependencies", columnDefinition = "jsonb")
    private Map<String, Object> dependencies;

    /**
     * 最小引擎版本要求
     */
    @Column(name = "min_engine_version", length = 20)
    private String minEngineVersion;
}

