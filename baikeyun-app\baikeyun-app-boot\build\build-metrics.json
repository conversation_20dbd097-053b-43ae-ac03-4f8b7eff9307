{"duration": 5613, "records": [{"duration": 1861, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#getAllExtensions", "started": "21:33:45.681", "dependents": [581, 580, 576, 575, 574], "id": 573, "thread": "build-28"}, {"duration": 1762, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#pregenProxies", "started": "21:33:47.317", "dependents": [594], "id": 593, "thread": "build-100"}, {"duration": 1334, "stepId": "io.quarkus.deployment.steps.ClassTransformingBuildStep#handleClassTransformation", "started": "21:33:45.983", "dependents": [593], "id": 572, "thread": "build-16"}, {"duration": 1110, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#build", "started": "21:33:44.810", "dependents": [594], "id": 566, "thread": "build-73"}, {"duration": 810, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#enhancerDomainObjects", "started": "21:33:45.166", "dependents": [570, 572, 569, 568], "id": 567, "thread": "build-22"}, {"duration": 488, "stepId": "io.quarkus.vertx.http.deployment.webjar.WebJarProcessor#processWebJarDevMode", "started": "21:33:47.543", "dependents": [581, 582, 594], "id": 580, "thread": "build-16"}, {"duration": 487, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateConfigClass", "started": "21:33:43.719", "dependents": [], "id": 341, "thread": "build-26"}, {"duration": 419, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#setupConsole", "started": "21:33:43.717", "dependents": [336, 440, 335, 346, 337], "id": 334, "thread": "build-40"}, {"duration": 409, "stepId": "io.quarkus.deployment.index.ApplicationArchiveBuildStep#build", "started": "21:33:44.069", "dependents": [376, 344, 501, 343, 587, 346, 572, 569, 424, 426, 441, 345], "id": 342, "thread": "build-54"}, {"duration": 381, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#parsePersistenceXmlDescriptors", "started": "21:33:43.726", "dependents": [330, 424, 426], "id": 329, "thread": "build-31"}, {"duration": 350, "stepId": "io.quarkus.deployment.ide.IdeProcessor#detectRunningIdeProcesses", "started": "21:33:43.736", "dependents": [327], "id": 326, "thread": "build-49"}, {"duration": 349, "stepId": "io.quarkus.deployment.steps.ApplicationIndexBuildStep#build", "started": "21:33:43.718", "dependents": [548, 432, 325, 466, 419, 342, 478, 473], "id": 324, "thread": "build-28"}, {"duration": 334, "stepId": "io.quarkus.virtual.threads.deployment.VirtualThreadsProcessor#setup", "started": "21:33:43.712", "dependents": [492, 466, 490, 594, 493, 441], "id": 316, "thread": "build-33"}, {"duration": 328, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#logConsoleCommand", "started": "21:33:43.734", "dependents": [552], "id": 323, "thread": "build-2"}, {"duration": 306, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#registerJsonRpcService", "started": "21:33:43.720", "dependents": [492, 541, 544, 490, 594, 493, 319], "id": 311, "thread": "build-47"}, {"duration": 299, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#buildStatic", "started": "21:33:43.736", "dependents": [594], "id": 314, "thread": "build-46"}, {"duration": 296, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#checkForBuildTimeConfigChange", "started": "21:33:43.719", "dependents": [594], "id": 309, "thread": "build-44"}, {"duration": 293, "stepId": "io.quarkus.arc.deployment.ArcProcessor#generateResources", "started": "21:33:45.346", "dependents": [592, 572, 521], "id": 520, "thread": "build-16"}, {"duration": 283, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#currentContextFactory", "started": "21:33:43.721", "dependents": [594, 521], "id": 299, "thread": "build-50"}, {"duration": 282, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#createDevUILog", "started": "21:33:43.878", "dependents": [540, 594, 586], "id": 339, "thread": "build-71"}, {"duration": 282, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#configureLogging", "started": "21:33:43.701", "dependents": [594], "id": 288, "thread": "build-7"}, {"duration": 282, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#bodyHandler", "started": "21:33:43.878", "dependents": [594, 517, 586], "id": 338, "thread": "build-73"}, {"duration": 281, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#releaseConfigOnShutdown", "started": "21:33:43.702", "dependents": [594], "id": 280, "thread": "build-10"}, {"duration": 278, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#createVertxContextHandlers", "started": "21:33:43.726", "dependents": [315, 594, 312], "id": 302, "thread": "build-11"}, {"duration": 276, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initFormAuth", "started": "21:33:43.708", "dependents": [584, 466, 585, 594, 441], "id": 290, "thread": "build-17"}, {"duration": 275, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerBeans", "started": "21:33:44.867", "dependents": [472, 470, 484, 469, 477, 490, 556, 479, 471, 492, 495, 508, 485, 474, 475, 476, 493, 517, 478, 473], "id": 468, "thread": "build-64"}, {"duration": 275, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#create", "started": "21:33:43.708", "dependents": [594], "id": 289, "thread": "build-8"}, {"duration": 272, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#shutdown", "started": "21:33:43.711", "dependents": [594], "id": 287, "thread": "build-30"}, {"duration": 270, "stepId": "io.quarkus.deployment.steps.ClassPathSystemPropBuildStep#set", "started": "21:33:43.716", "dependents": [594], "id": 291, "thread": "build-27"}, {"duration": 269, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#registerMetrics", "started": "21:33:43.718", "dependents": [439, 594], "id": 293, "thread": "build-13"}, {"duration": 264, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLoggingStaticInit", "started": "21:33:43.718", "dependents": [594], "id": 282, "thread": "build-43"}, {"duration": 253, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#registerAdditionalBeans", "started": "21:33:43.768", "dependents": [492, 466, 510, 498, 448, 490, 594, 493, 441], "id": 310, "thread": "build-64"}, {"duration": 249, "stepId": "io.quarkus.security.deployment.SecurityProcessor#recordBouncyCastleProviders", "started": "21:33:43.726", "dependents": [594], "id": 272, "thread": "build-12"}, {"duration": 249, "stepId": "io.quarkus.netty.deployment.NettyProcessor#eagerlyInitClass", "started": "21:33:43.734", "dependents": [594], "id": 285, "thread": "build-37"}, {"duration": 247, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#ioThreadDetector", "started": "21:33:43.757", "dependents": [308, 594], "id": 303, "thread": "build-72"}, {"duration": 243, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#resetMapper", "started": "21:33:43.739", "dependents": [594], "id": 284, "thread": "build-56"}, {"duration": 242, "stepId": "io.quarkus.arc.deployment.ArcProcessor#buildCompatibleExtensions", "started": "21:33:43.739", "dependents": [466, 441], "id": 276, "thread": "build-25"}, {"duration": 238, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#createVertxThreadFactory", "started": "21:33:43.767", "dependents": [594, 312], "id": 301, "thread": "build-78"}, {"duration": 237, "stepId": "io.quarkus.mutiny.deployment.MutinyProcessor#buildTimeInit", "started": "21:33:43.746", "dependents": [594], "id": 279, "thread": "build-21"}, {"duration": 237, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#shutdownConfigValidator", "started": "21:33:43.746", "dependents": [594], "id": 283, "thread": "build-3"}, {"duration": 234, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#createHttpAuthenticationHandler", "started": "21:33:43.770", "dependents": [317, 594, 524], "id": 307, "thread": "build-54"}, {"duration": 234, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#addRoutingCtxToSecurityEventsForCdiBeans", "started": "21:33:43.770", "dependents": [594, 320], "id": 305, "thread": "build-39"}, {"duration": 228, "stepId": "io.quarkus.elytron.security.common.deployment.QuarkusSecurityCommonProcessor#registerPasswordProvider", "started": "21:33:43.754", "dependents": [594], "id": 281, "thread": "build-18"}, {"duration": 223, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#build", "started": "21:33:49.079", "dependents": [], "id": 594, "thread": "build-114"}, {"duration": 222, "stepId": "io.quarkus.elytron.security.common.deployment.QuarkusSecurityCommonProcessor#registerPasswordProviderForNative", "started": "21:33:43.761", "dependents": [594], "id": 286, "thread": "build-57"}, {"duration": 221, "stepId": "io.quarkus.deployment.dev.io.NioThreadPoolDevModeProcessor#setupTCCL", "started": "21:33:43.761", "dependents": [594], "id": 277, "thread": "build-68"}, {"duration": 218, "stepId": "io.quarkus.deployment.steps.BannerProcessor#recordBanner", "started": "21:33:43.879", "dependents": [439, 594], "id": 328, "thread": "build-60"}, {"duration": 217, "stepId": "io.quarkus.vertx.http.deployment.ManagementInterfaceSecurityProcessor#createManagementAuthMechHandler", "started": "21:33:43.770", "dependents": [525, 594, 296], "id": 292, "thread": "build-53"}, {"duration": 215, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#produceNamedHttpSecurityPolicies", "started": "21:33:43.762", "dependents": [492, 490, 594, 493], "id": 274, "thread": "build-75"}, {"duration": 210, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#replaceDefaultAuthFailureHandler", "started": "21:33:43.769", "dependents": [594, 564, 586], "id": 275, "thread": "build-81"}, {"duration": 208, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#generateDataSourceSupportBean", "started": "21:33:43.795", "dependents": [492, 466, 510, 498, 490, 594, 493, 441], "id": 297, "thread": "build-66"}, {"duration": 206, "stepId": "io.quarkus.deployment.steps.PreloadClassesBuildStep#preInit", "started": "21:33:43.771", "dependents": [594], "id": 273, "thread": "build-79"}, {"duration": 202, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#buildTimeRunTimeConfig", "started": "21:33:43.708", "dependents": [546, 592], "id": 270, "thread": "build-19"}, {"duration": 193, "stepId": "io.quarkus.agroal.deployment.AgroalMetricsProcessor#registerMetrics", "started": "21:33:43.795", "dependents": [594], "id": 294, "thread": "build-48"}, {"duration": 182, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createBuildTimeConstJsTemplate", "started": "21:33:47.595", "dependents": [578, 579], "id": 577, "thread": "build-114"}, {"duration": 174, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#helpCommand", "started": "21:33:43.702", "dependents": [552], "id": 267, "thread": "build-9"}, {"duration": 172, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#quitCommand", "started": "21:33:43.703", "dependents": [552], "id": 268, "thread": "build-14"}, {"duration": 169, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#registerDevUiHandlers", "started": "21:33:48.032", "dependents": [584, 585, 594], "id": 583, "thread": "build-16"}, {"duration": 168, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#generateJpaConfigBean", "started": "21:33:43.879", "dependents": [492, 490, 594, 493], "id": 318, "thread": "build-16"}, {"duration": 160, "stepId": "io.quarkus.deployment.steps.RuntimeConfigSetupBuildStep#setupRuntimeConfig", "started": "21:33:43.717", "dependents": [278, 483, 340, 489, 527, 582, 304, 298, 524, 564, 338, 332, 590, 561, 439, 594, 591, 312, 306, 517, 562, 464, 433, 420, 585, 339, 499, 486, 586, 531, 300, 529, 494, 589, 422, 525, 488, 459, 295, 318, 271, 431, 328], "id": 269, "thread": "build-32"}, {"duration": 158, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#jsonDefault", "started": "21:33:43.707", "dependents": [548], "id": 264, "thread": "build-22"}, {"duration": 150, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#build", "started": "21:33:44.497", "dependents": [466, 494, 592, 421, 422, 439, 594, 441], "id": 420, "thread": "build-28"}, {"duration": 143, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#getSwaggerUiFinalDestination", "started": "21:33:45.660", "dependents": [580], "id": 555, "thread": "build-102"}, {"duration": 142, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setupEndpoints", "started": "21:33:45.650", "dependents": [549, 559, 592, 551, 572, 594, 556, 557], "id": 548, "thread": "build-71"}, {"duration": 131, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#defineJpaEntities", "started": "21:33:44.500", "dependents": [530, 437, 415, 412, 426, 424, 567, 593, 592, 430, 438, 411, 436, 431, 413], "id": 410, "thread": "build-22"}, {"duration": 126, "stepId": "io.quarkus.datasource.deployment.DataSourcesExcludedFromHealthChecksProcessor#produceBean", "started": "21:33:43.878", "dependents": [492, 490, 594, 493], "id": 298, "thread": "build-22"}, {"duration": 126, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#eventLoopCount", "started": "21:33:43.878", "dependents": [594, 591, 486], "id": 306, "thread": "build-65"}, {"duration": 126, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initBasicAuth", "started": "21:33:43.878", "dependents": [492, 490, 463, 594, 493, 464], "id": 304, "thread": "build-14"}, {"duration": 125, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#cors", "started": "21:33:43.878", "dependents": [594, 564, 586], "id": 300, "thread": "build-24"}, {"duration": 111, "stepId": "io.quarkus.vertx.http.deployment.console.ConsoleProcessor#setupConsole", "started": "21:33:43.743", "dependents": [589, 588], "id": 263, "thread": "build-65"}, {"duration": 111, "stepId": "io.quarkus.vertx.deployment.VertxJsonProcessor#registerJacksonSerDeser", "started": "21:33:43.727", "dependents": [404], "id": 259, "thread": "build-24"}, {"duration": 111, "stepId": "io.quarkus.vertx.http.deployment.GeneratedStaticResourcesProcessor#process", "started": "21:33:43.879", "dependents": [584, 585, 587, 594], "id": 295, "thread": "build-15"}, {"duration": 109, "stepId": "io.quarkus.arc.deployment.ArcProcessor#validate", "started": "21:33:45.213", "dependents": [518, 502, 510, 506, 507, 499, 509, 503, 514, 504, 512, 520, 508, 501, 500, 572, 516, 505], "id": 498, "thread": "build-44"}, {"duration": 105, "stepId": "io.quarkus.deployment.steps.ConfigDescriptionBuildStep#createConfigDescriptions", "started": "21:33:43.707", "dependents": [545, 542, 536], "id": 257, "thread": "build-16"}, {"duration": 105, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createIndexHtmlTemplate", "started": "21:33:47.778", "dependents": [579], "id": 578, "thread": "build-113"}, {"duration": 104, "stepId": "io.quarkus.security.deployment.SecurityProcessor#recordRuntimeConfigReady", "started": "21:33:43.878", "dependents": [594], "id": 278, "thread": "build-9"}, {"duration": 97, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#compositeScheduler", "started": "21:33:43.746", "dependents": [482, 261, 466, 262, 507, 441], "id": 260, "thread": "build-15"}, {"duration": 96, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#beanDefiningAnnotations", "started": "21:33:43.769", "dependents": [266, 466, 441], "id": 265, "thread": "build-73"}, {"duration": 96, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#setMtlsCertificateRoleProperties", "started": "21:33:43.879", "dependents": [594], "id": 271, "thread": "build-59"}, {"duration": 91, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#build_2776f39a7cbf851c2510e1959c8b2b421193add9", "started": "21:33:44.028", "dependents": [340, 585, 481, 490, 588, 586, 492, 529, 589, 594, 591, 493, 333], "id": 332, "thread": "build-47"}, {"duration": 84, "stepId": "io.quarkus.caffeine.deployment.devui.CaffeineDevUIProcessor#createCard", "started": "21:33:43.701", "dependents": [573, 543], "id": 239, "thread": "build-6"}, {"duration": 81, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#vertxIntegration", "started": "21:33:43.752", "dependents": [558, 556, 554], "id": 258, "thread": "build-71"}, {"duration": 79, "stepId": "io.quarkus.arc.deployment.devui.ArcDevModeApiProcessor#collectBeanInfo", "started": "21:33:45.322", "dependents": [519], "id": 518, "thread": "build-39"}, {"duration": 78, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#unremovableBeans", "started": "21:33:43.720", "dependents": [510, 498], "id": 252, "thread": "build-45"}, {"duration": 77, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateMappings", "started": "21:33:44.497", "dependents": [502, 472, 474, 510, 592, 465, 408, 462], "id": 405, "thread": "build-2"}, {"duration": 74, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#registerAdditionalBeans", "started": "21:33:43.720", "dependents": [466, 592, 441], "id": 247, "thread": "build-48"}, {"duration": 72, "stepId": "io.quarkus.arc.deployment.BeanArchiveProcessor#build", "started": "21:33:44.672", "dependents": [466, 484, 452, 450, 533, 507, 447, 461, 548, 514, 508, 485, 456, 449, 443, 517, 451, 458, 465, 454, 556, 479, 448, 444, 442], "id": 441, "thread": "build-64"}, {"duration": 68, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLoggingRuntimeInit", "started": "21:33:44.648", "dependents": [440, 590, 592, 594], "id": 439, "thread": "build-2"}, {"duration": 65, "stepId": "io.quarkus.netty.deployment.NettyProcessor#setNettyMachineId", "started": "21:33:43.717", "dependents": [594], "id": 236, "thread": "build-38"}, {"duration": 65, "stepId": "io.quarkus.security.deployment.SecurityProcessor#makeSecurityAnnotationsInherited", "started": "21:33:43.720", "dependents": [466], "id": 240, "thread": "build-35"}, {"duration": 64, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#generateCustomizer", "started": "21:33:44.499", "dependents": [441], "id": 404, "thread": "build-24"}, {"duration": 63, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#unremovable", "started": "21:33:44.498", "dependents": [466, 510, 498, 441], "id": 403, "thread": "build-78"}, {"duration": 54, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#beanValidationAnnotations", "started": "21:33:44.745", "dependents": [465, 517, 462], "id": 461, "thread": "build-22"}, {"duration": 53, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#logging", "started": "21:33:43.750", "dependents": [256], "id": 255, "thread": "build-60"}, {"duration": 52, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#beans", "started": "21:33:43.736", "dependents": [466, 441], "id": 242, "thread": "build-20"}, {"duration": 51, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#testConsoleCommand", "started": "21:33:44.498", "dependents": [552], "id": 402, "thread": "build-64"}, {"duration": 49, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#createJsonRPCService", "started": "21:33:43.746", "dependents": [541, 544, 319], "id": 248, "thread": "build-66"}, {"duration": 49, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateBuilders", "started": "21:33:45.659", "dependents": [592], "id": 546, "thread": "build-51"}, {"duration": 48, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createBuildTimeData", "started": "21:33:47.547", "dependents": [577, 578], "id": 576, "thread": "build-113"}, {"duration": 46, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#registerConfigs", "started": "21:33:45.659", "dependents": [594], "id": 545, "thread": "build-151"}, {"duration": 46, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#exceptionMappers", "started": "21:33:43.744", "dependents": [407], "id": 244, "thread": "build-67"}, {"duration": 45, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#preinitializeRouter", "started": "21:33:44.119", "dependents": [492, 585, 490, 594, 493], "id": 340, "thread": "build-49"}, {"duration": 45, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#handleCustomAnnotatedMethods", "started": "21:33:44.531", "dependents": [466, 409, 407, 441], "id": 406, "thread": "build-31"}, {"duration": 45, "stepId": "io.quarkus.deployment.steps.CompiledJavaVersionBuildStep#compiledJavaVersion", "started": "21:33:43.716", "dependents": [548], "id": 179, "thread": "build-39"}, {"duration": 43, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#createJsonRPCService", "started": "21:33:43.754", "dependents": [541, 544, 319], "id": 251, "thread": "build-41"}, {"duration": 43, "stepId": "io.quarkus.security.deployment.SecurityProcessor#registerSecurityInterceptors", "started": "21:33:44.005", "dependents": [492, 466, 490, 594, 493, 441], "id": 320, "thread": "build-14"}, {"duration": 41, "stepId": "io.quarkus.flyway.deployment.FlywayProcessor#reflection", "started": "21:33:44.499", "dependents": [592, 557], "id": 400, "thread": "build-39"}, {"duration": 41, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#setupAuthenticationMechanisms", "started": "21:33:44.005", "dependents": [466, 463, 594, 441, 464, 564, 586], "id": 317, "thread": "build-65"}, {"duration": 41, "stepId": "io.quarkus.arc.deployment.ConfigStaticInitBuildSteps#transformConfigProducer", "started": "21:33:43.759", "dependents": [466], "id": 254, "thread": "build-59"}, {"duration": 41, "stepId": "io.quarkus.security.deployment.SecurityProcessor#registerAdditionalBeans", "started": "21:33:43.714", "dependents": [466, 441], "id": 161, "thread": "build-36"}, {"duration": 39, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerAutoSecurityFilter", "started": "21:33:44.771", "dependents": [492, 490, 594, 493], "id": 464, "thread": "build-64"}, {"duration": 39, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#addAutoFilters", "started": "21:33:44.771", "dependents": [566], "id": 463, "thread": "build-2"}, {"duration": 36, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#fileHandling", "started": "21:33:43.754", "dependents": [558, 556], "id": 245, "thread": "build-63"}, {"duration": 34, "stepId": "io.quarkus.vertx.deployment.EventBusCodecProcessor#registerCodecs", "started": "21:33:44.745", "dependents": [592, 481], "id": 456, "thread": "build-73"}, {"duration": 33, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#autoAddScope", "started": "21:33:43.701", "dependents": [448], "id": 92, "thread": "build-2"}, {"duration": 32, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#serverSerializers", "started": "21:33:45.801", "dependents": [559, 592, 594], "id": 558, "thread": "build-151"}, {"duration": 32, "stepId": "io.quarkus.deployment.dev.HotDeploymentWatchedFileBuildStep#setupWatchedFileHotDeployment", "started": "21:33:44.659", "dependents": [589, 588], "id": 438, "thread": "build-16"}, {"duration": 32, "stepId": "io.quarkus.deployment.ide.IdeProcessor#detectIdeFiles", "started": "21:33:43.717", "dependents": [327], "id": 143, "thread": "build-4"}, {"duration": 32, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#autoAddScope", "started": "21:33:43.765", "dependents": [448], "id": 253, "thread": "build-52"}, {"duration": 32, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#securityExceptionMappers", "started": "21:33:43.728", "dependents": [407], "id": 174, "thread": "build-57"}, {"duration": 32, "stepId": "io.quarkus.deployment.DockerStatusProcessor#IsDockerWorking", "started": "21:33:43.744", "dependents": [440, 538], "id": 228, "thread": "build-23"}, {"duration": 32, "stepId": "io.quarkus.arc.deployment.ArcProcessor#initialize", "started": "21:33:44.829", "dependents": [518, 485, 468, 467], "id": 466, "thread": "build-64"}, {"duration": 31, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#contextInjection", "started": "21:33:43.757", "dependents": [466, 446, 448, 441], "id": 243, "thread": "build-58"}, {"duration": 31, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanResources", "started": "21:33:44.499", "dependents": [388, 406, 385, 397, 550, 466, 389, 533, 548, 386, 396, 559, 392, 387, 449], "id": 384, "thread": "build-11"}, {"duration": 31, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#implementation", "started": "21:33:43.714", "dependents": [261, 260], "id": 130, "thread": "build-29"}, {"duration": 30, "stepId": "io.quarkus.arc.deployment.ArcProcessor#setupExecutor", "started": "21:33:44.029", "dependents": [594], "id": 322, "thread": "build-54"}, {"duration": 30, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#registerAuthMechanismSelectionInterceptor", "started": "21:33:44.497", "dependents": [382, 458, 594, 478, 380, 379], "id": 378, "thread": "build-47"}, {"duration": 28, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#build", "started": "21:33:44.800", "dependents": [466, 510, 592, 498, 594, 522], "id": 465, "thread": "build-16"}, {"duration": 28, "stepId": "io.quarkus.security.deployment.SecurityProcessor#gatherSecurityChecks", "started": "21:33:44.764", "dependents": [548, 460, 546, 466, 592, 594, 459, 517], "id": 458, "thread": "build-78"}, {"duration": 28, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#autoAddScope", "started": "21:33:43.707", "dependents": [448], "id": 97, "thread": "build-21"}, {"duration": 28, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setupDeployment", "started": "21:33:45.833", "dependents": [560, 563, 584, 585, 592, 561, 594, 562, 564, 586], "id": 559, "thread": "build-16"}, {"duration": 27, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#recordableConstructor", "started": "21:33:43.770", "dependents": [594], "id": 250, "thread": "build-82"}, {"duration": 26, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#exceptionMapper", "started": "21:33:43.762", "dependents": [592, 407], "id": 241, "thread": "build-29"}, {"duration": 26, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#mainClassBuildStep", "started": "21:33:44.499", "dependents": [572], "id": 376, "thread": "build-72"}, {"duration": 26, "stepId": "io.quarkus.scheduler.deployment.devui.SchedulerDevUIProcessor#rpcProvider", "started": "21:33:43.734", "dependents": [544, 319], "id": 175, "thread": "build-61"}, {"duration": 26, "stepId": "io.quarkus.smallrye.jwt.build.deployment.SmallRyeJwtBuildProcessor#addClassesForReflection", "started": "21:33:43.744", "dependents": [592], "id": 215, "thread": "build-5"}, {"duration": 25, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initRuntime", "started": "21:33:45.177", "dependents": [531, 530, 495, 494, 589, 594, 588], "id": 493, "thread": "build-39"}, {"duration": 25, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#smallryeOpenApiIndex", "started": "21:33:44.745", "dependents": [566, 455, 463, 457, 464], "id": 454, "thread": "build-28"}, {"duration": 25, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#buildReactivePersistenceUnit", "started": "21:33:44.633", "dependents": [432, 429, 437, 435, 427, 428, 593, 532, 487, 430, 594, 438, 436, 431], "id": 426, "thread": "build-78"}, {"duration": 25, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#pages", "started": "21:33:45.402", "dependents": [573, 543], "id": 519, "thread": "build-60"}, {"duration": 24, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#filterMultipleVertxInstancesWarning", "started": "21:33:43.710", "dependents": [421, 439], "id": 96, "thread": "build-20"}, {"duration": 24, "stepId": "io.quarkus.devui.deployment.ide.IdeProcessor#createOpenInIDEService", "started": "21:33:44.090", "dependents": [584, 541, 585, 594], "id": 331, "thread": "build-49"}, {"duration": 24, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#addScope", "started": "21:33:43.712", "dependents": [448], "id": 109, "thread": "build-25"}, {"duration": 23, "stepId": "io.quarkus.vertx.http.deployment.devmode.ArcDevProcessor#registerRoutes", "started": "21:33:45.323", "dependents": [584, 520, 585, 587, 594], "id": 516, "thread": "build-60"}, {"duration": 22, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#build", "started": "21:33:43.773", "dependents": [466, 592, 422, 297, 294, 491, 441], "id": 249, "thread": "build-80"}, {"duration": 22, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#overrideContextInternalInterfaceToAddSafeGuards", "started": "21:33:43.736", "dependents": [572], "id": 172, "thread": "build-51"}, {"duration": 22, "stepId": "io.quarkus.vertx.http.deployment.console.ConsoleProcessor#config", "started": "21:33:45.660", "dependents": [552], "id": 542, "thread": "build-44"}, {"duration": 22, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#collectInterceptedStaticMethods", "started": "21:33:45.142", "dependents": [485, 510, 498, 526], "id": 484, "thread": "build-22"}, {"duration": 22, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#registerSecurityBeans", "started": "21:33:43.771", "dependents": [466, 441], "id": 246, "thread": "build-83"}, {"duration": 22, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#registerInterceptors", "started": "21:33:43.748", "dependents": [466, 441], "id": 213, "thread": "build-54"}, {"duration": 22, "stepId": "io.quarkus.deployment.steps.ThreadPoolSetup#createExecutor", "started": "21:33:44.005", "dependents": [332, 315, 321, 313, 594, 322, 586], "id": 312, "thread": "build-11"}, {"duration": 21, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#build", "started": "21:33:44.028", "dependents": [492, 490, 594, 493], "id": 321, "thread": "build-78"}, {"duration": 21, "stepId": "io.quarkus.security.deployment.SecurityProcessor#registerJCAProvidersForReflection", "started": "21:33:43.728", "dependents": [592], "id": 148, "thread": "build-41"}, {"duration": 21, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#build_7a4403d699506d83ac39616f3c11e5e1b448d863", "started": "21:33:44.658", "dependents": [594, 522], "id": 437, "thread": "build-22"}, {"duration": 21, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#transformResourceMethods", "started": "21:33:44.495", "dependents": [466], "id": 369, "thread": "build-49"}, {"duration": 20, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#register", "started": "21:33:44.499", "dependents": [466, 592, 557, 441], "id": 374, "thread": "build-46"}, {"duration": 20, "stepId": "io.quarkus.security.deployment.SecurityProcessor#validateStartUpObserversNotSecured", "started": "21:33:45.323", "dependents": [520], "id": 514, "thread": "build-2"}, {"duration": 20, "stepId": "io.quarkus.devui.deployment.menu.ExtensionsProcessor#createBuildTimeActions", "started": "21:33:43.703", "dependents": [541], "id": 69, "thread": "build-15"}, {"duration": 20, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#additionalBean", "started": "21:33:44.027", "dependents": [466, 441, 345], "id": 319, "thread": "build-64"}, {"duration": 19, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#registerVerticleClasses", "started": "21:33:44.498", "dependents": [592], "id": 373, "thread": "build-16"}, {"duration": 19, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#build", "started": "21:33:45.157", "dependents": [492, 589, 487, 490, 594, 493, 491, 588], "id": 486, "thread": "build-28"}, {"duration": 19, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#handler", "started": "21:33:44.499", "dependents": [439, 594], "id": 372, "thread": "build-31"}, {"duration": 19, "stepId": "io.quarkus.agroal.deployment.devui.AgroalDevUIProcessor#devUI", "started": "21:33:43.707", "dependents": [573, 544, 543, 319], "id": 75, "thread": "build-11"}, {"duration": 19, "stepId": "io.quarkus.devservices.deployment.DevServicesProcessor#config", "started": "21:33:45.660", "dependents": [540, 552, 539], "id": 538, "thread": "build-6"}, {"duration": 18, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#resolveRolesAllowedConfigExpressions", "started": "21:33:44.499", "dependents": [492, 458, 490, 594, 499, 493], "id": 371, "thread": "build-44"}, {"duration": 18, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerAnnotatedUserDefinedRuntimeFilters", "started": "21:33:44.771", "dependents": [492, 592, 490, 594, 493], "id": 457, "thread": "build-16"}, {"duration": 18, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#gatherAuthorizationPolicyInstances", "started": "21:33:44.495", "dependents": [375, 453], "id": 365, "thread": "build-54"}, {"duration": 18, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#additionalBeans", "started": "21:33:43.763", "dependents": [466, 441], "id": 235, "thread": "build-34"}, {"duration": 18, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#registerVerticleClasses", "started": "21:33:44.499", "dependents": [592], "id": 370, "thread": "build-71"}, {"duration": 18, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#addScope", "started": "21:33:43.714", "dependents": [448], "id": 91, "thread": "build-37"}, {"duration": 18, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#jacksonSupport", "started": "21:33:44.496", "dependents": [492, 490, 594, 493], "id": 367, "thread": "build-60"}, {"duration": 17, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#checkMixingStacks", "started": "21:33:43.768", "dependents": [589, 588], "id": 238, "thread": "build-76"}, {"duration": 17, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupStackTraceFormatter", "started": "21:33:44.478", "dependents": [439, 372, 586], "id": 346, "thread": "build-71"}, {"duration": 17, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#validateInterceptedMethods", "started": "21:33:45.323", "dependents": [520], "id": 512, "thread": "build-64"}, {"duration": 17, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#supportMixins", "started": "21:33:44.498", "dependents": [492, 592, 490, 594, 493], "id": 368, "thread": "build-14"}, {"duration": 17, "stepId": "io.quarkus.security.deployment.SecurityProcessor#transformPermissionsAllowedMetaAnnotations", "started": "21:33:44.744", "dependents": [451, 466, 453, 452], "id": 450, "thread": "build-16"}, {"duration": 17, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#unremovableBean", "started": "21:33:43.725", "dependents": [510, 498], "id": 120, "thread": "build-18"}, {"duration": 17, "stepId": "io.quarkus.devservices.postgresql.deployment.PostgresqlDevServicesProcessor#setupPostgres", "started": "21:33:43.723", "dependents": [440], "id": 114, "thread": "build-34"}, {"duration": 16, "stepId": "io.quarkus.deployment.steps.CombinedIndexBuildStep#build", "started": "21:33:44.478", "dependents": [354, 466, 369, 415, 365, 367, 380, 461, 347, 504, 474, 404, 439, 568, 425, 358, 401, 462, 405, 390, 472, 381, 378, 420, 395, 406, 403, 550, 348, 402, 465, 407, 368, 373, 360, 557, 570, 569, 357, 349, 350, 370, 351, 371, 393, 424, 426, 353, 456, 352, 544, 383, 430, 400, 376, 391, 363, 394, 374, 356, 454, 359, 445, 503, 546, 366, 453, 361, 355, 436, 417, 362, 413], "id": 345, "thread": "build-73"}, {"duration": 16, "stepId": "io.quarkus.deployment.steps.ReflectiveHierarchyStep#build", "started": "21:33:45.794", "dependents": [592], "id": 557, "thread": "build-16"}, {"duration": 15, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#findAllJsonRPCMethods", "started": "21:33:45.682", "dependents": [577, 547], "id": 544, "thread": "build-16"}, {"duration": 15, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigRootsAsBeans", "started": "21:33:43.720", "dependents": [492, 490, 493], "id": 100, "thread": "build-46"}, {"duration": 15, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#handleApplication", "started": "21:33:44.514", "dependents": [390, 395, 391, 393, 394, 407, 533, 548, 409, 559, 558, 592, 383, 401], "id": 381, "thread": "build-73"}, {"duration": 15, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#transformEndpoints", "started": "21:33:44.745", "dependents": [466], "id": 449, "thread": "build-78"}, {"duration": 15, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#createJsonRPCService", "started": "21:33:43.721", "dependents": [544, 319], "id": 99, "thread": "build-49"}, {"duration": 15, "stepId": "io.quarkus.flyway.deployment.devui.FlywayDevUIProcessor#registerJsonRpcBackend", "started": "21:33:43.730", "dependents": [544, 319], "id": 135, "thread": "build-59"}, {"duration": 14, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#buildResourceInterceptors", "started": "21:33:44.576", "dependents": [548, 559, 466, 449, 556, 441], "id": 409, "thread": "build-2"}, {"duration": 14, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#config", "started": "21:33:43.729", "dependents": [491], "id": 125, "thread": "build-58"}, {"duration": 14, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#createJsonRpcRouter", "started": "21:33:45.698", "dependents": [594], "id": 547, "thread": "build-34"}, {"duration": 14, "stepId": "io.quarkus.deployment.steps.RegisterForReflectionBuildStep#build", "started": "21:33:44.500", "dependents": [592, 557], "id": 366, "thread": "build-66"}, {"duration": 14, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#additionalBeans", "started": "21:33:43.771", "dependents": [466, 441], "id": 237, "thread": "build-55"}, {"duration": 14, "stepId": "io.quarkus.arc.deployment.CommandLineArgumentsProcessor#commandLineArgs", "started": "21:33:43.759", "dependents": [492, 466, 490, 493, 441], "id": 227, "thread": "build-70"}, {"duration": 13, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#buildExclusions", "started": "21:33:44.514", "dependents": [454], "id": 377, "thread": "build-27"}, {"duration": 13, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#setupConfigOverride", "started": "21:33:43.740", "dependents": [], "id": 153, "thread": "build-63"}, {"duration": 13, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateConfigMappingsInjectionPoints", "started": "21:33:45.322", "dependents": [546, 515], "id": 510, "thread": "build-27"}, {"duration": 13, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#configFiles", "started": "21:33:43.722", "dependents": [438], "id": 98, "thread": "build-51"}, {"duration": 13, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#sharedStateListener", "started": "21:33:43.742", "dependents": [337], "id": 158, "thread": "build-34"}, {"duration": 13, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#addAdditionalRoutes", "started": "21:33:45.334", "dependents": [584, 585, 587, 592, 594, 557, 564, 586], "id": 517, "thread": "build-72"}, {"duration": 13, "stepId": "io.quarkus.panache.hibernate.common.deployment.PanacheHibernateCommonResourceProcessor#findEntityClasses", "started": "21:33:44.634", "dependents": [569, 418], "id": 417, "thread": "build-22"}, {"duration": 13, "stepId": "io.quarkus.smallrye.openapi.deployment.devui.OpenApiDevUIProcessor#pages", "started": "21:33:43.737", "dependents": [573, 543], "id": 145, "thread": "build-52"}, {"duration": 13, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#devDbHandler", "started": "21:33:43.748", "dependents": [440], "id": 178, "thread": "build-64"}, {"duration": 12, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#generateDataSourceBeans", "started": "21:33:44.658", "dependents": [492, 466, 490, 594, 493, 441], "id": 435, "thread": "build-64"}, {"duration": 12, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#registerBeans", "started": "21:33:44.659", "dependents": [466, 510, 498, 441], "id": 436, "thread": "build-73"}, {"duration": 12, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initRegular", "started": "21:33:45.176", "dependents": [495], "id": 492, "thread": "build-24"}, {"duration": 12, "stepId": "io.quarkus.devui.deployment.menu.ReadmeProcessor#createJsonRPCServiceForCache", "started": "21:33:43.725", "dependents": [544, 319], "id": 111, "thread": "build-15"}, {"duration": 12, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForContextResolvers", "started": "21:33:44.530", "dependents": [559, 466, 553, 592, 441], "id": 401, "thread": "build-44"}, {"duration": 12, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#validatePersistenceUnitExtensions", "started": "21:33:45.323", "dependents": [520], "id": 509, "thread": "build-16"}, {"duration": 11, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#unlessBuildProperty", "started": "21:33:44.502", "dependents": [377, 381, 364], "id": 362, "thread": "build-27"}, {"duration": 11, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#vetoMPConfigProperties", "started": "21:33:43.748", "dependents": [466], "id": 170, "thread": "build-68"}, {"duration": 11, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#validateBeanDeployment", "started": "21:33:45.323", "dependents": [520, 517], "id": 508, "thread": "build-46"}, {"duration": 11, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#ifBuildProperty", "started": "21:33:44.502", "dependents": [377, 381, 364], "id": 363, "thread": "build-33"}, {"duration": 11, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#cacheControlSupport", "started": "21:33:43.717", "dependents": [548], "id": 80, "thread": "build-5"}, {"duration": 11, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#validateScheduledBusinessMethods", "started": "21:33:45.323", "dependents": [520], "id": 507, "thread": "build-72"}, {"duration": 11, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#indexAdditionalConstrainedClasses", "started": "21:33:44.575", "dependents": [465, 461], "id": 408, "thread": "build-24"}, {"duration": 11, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#collectScheduledMethods_84ea631eea52cbbcaee3e56019e68e7826861add", "started": "21:33:45.142", "dependents": [482, 507, 480], "id": 479, "thread": "build-28"}, {"duration": 11, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#reflection", "started": "21:33:43.759", "dependents": [592], "id": 218, "thread": "build-4"}, {"duration": 11, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#silenceLogging", "started": "21:33:43.739", "dependents": [256], "id": 147, "thread": "build-53"}, {"duration": 10, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#unremovableBeans", "started": "21:33:43.718", "dependents": [510, 498], "id": 81, "thread": "build-41"}, {"duration": 10, "stepId": "io.quarkus.vertx.http.deployment.StaticResourcesProcessor#collectStaticResources", "started": "21:33:43.767", "dependents": [529], "id": 231, "thread": "build-36"}, {"duration": 10, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#unlessBuildProfile", "started": "21:33:44.502", "dependents": [377, 381, 364], "id": 361, "thread": "build-73"}, {"duration": 10, "stepId": "io.quarkus.arc.deployment.AutoAddScopeProcessor#annotationTransformer", "started": "21:33:44.746", "dependents": [466, 510, 498], "id": 448, "thread": "build-24"}, {"duration": 10, "stepId": "io.quarkus.arc.deployment.ConfigStaticInitBuildSteps#registerBeans", "started": "21:33:43.765", "dependents": [466, 441], "id": 229, "thread": "build-74"}, {"duration": 10, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#ifBuildProfile", "started": "21:33:44.502", "dependents": [377, 381, 364], "id": 360, "thread": "build-26"}, {"duration": 10, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#frameworkRoot", "started": "21:33:43.726", "dependents": [112, 555, 585, 551, 578, 582, 564, 586, 145, 217, 576, 577, 331, 565, 263, 516, 583], "id": 106, "thread": "build-42"}, {"duration": 9, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#unremoveableSkipPredicates", "started": "21:33:43.708", "dependents": [510, 498], "id": 54, "thread": "build-18"}, {"duration": 9, "stepId": "io.quarkus.mutiny.deployment.MutinyProcessor#runtimeInit", "started": "21:33:44.029", "dependents": [594], "id": 315, "thread": "build-44"}, {"duration": 9, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#asyncSupport", "started": "21:33:43.736", "dependents": [548], "id": 134, "thread": "build-60"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerContextPropagation", "started": "21:33:43.726", "dependents": [314], "id": 101, "thread": "build-23"}, {"duration": 9, "stepId": "io.quarkus.deployment.pkg.steps.FileSystemResourcesBuildStep#notNormalMode", "started": "21:33:43.724", "dependents": [], "id": 89, "thread": "build-54"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#generateConfigProperties", "started": "21:33:44.500", "dependents": [502, 472, 474, 510, 592, 465, 408, 462], "id": 359, "thread": "build-50"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateRuntimeConfigProperty", "started": "21:33:45.331", "dependents": [592, 594], "id": 513, "thread": "build-78"}, {"duration": 9, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#detectBasicAuthImplicitlyRequired", "started": "21:33:45.142", "dependents": [594], "id": 478, "thread": "build-26"}, {"duration": 9, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#validateBeans", "started": "21:33:45.323", "dependents": [520], "id": 506, "thread": "build-28"}, {"duration": 9, "stepId": "io.quarkus.deployment.steps.NativeImageConfigBuildStep#build", "started": "21:33:45.177", "dependents": [594], "id": 491, "thread": "build-27"}, {"duration": 9, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#provideSecurityInformation", "started": "21:33:43.723", "dependents": [463, 464], "id": 88, "thread": "build-52"}, {"duration": 8, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#deprioritizeLegacyProviders", "started": "21:33:43.734", "dependents": [558], "id": 121, "thread": "build-3"}, {"duration": 8, "stepId": "io.quarkus.hibernate.orm.deployment.ResteasyReactiveServerIntegrationProcessor#unwrappedExceptions", "started": "21:33:43.750", "dependents": [407], "id": 167, "thread": "build-69"}, {"duration": 8, "stepId": "io.quarkus.arc.deployment.HotDeploymentConfigBuildStep#startup", "started": "21:33:43.734", "dependents": [128], "id": 119, "thread": "build-5"}, {"duration": 8, "stepId": "io.quarkus.deployment.logging.LoggingWithPanacheProcessor#process", "started": "21:33:44.497", "dependents": [572], "id": 358, "thread": "build-40"}, {"duration": 8, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#generateAuthorizationPolicyStorage", "started": "21:33:44.514", "dependents": [458, 441], "id": 375, "thread": "build-26"}, {"duration": 8, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateStaticInitConfigProperty", "started": "21:33:45.331", "dependents": [592, 594], "id": 511, "thread": "build-31"}, {"duration": 8, "stepId": "io.quarkus.deployment.steps.BlockingOperationControlBuildStep#blockingOP", "started": "21:33:44.005", "dependents": [594], "id": 308, "thread": "build-78"}, {"duration": 8, "stepId": "io.quarkus.deployment.steps.ReflectiveHierarchyStep#ignoreJavaClassWarnings", "started": "21:33:43.750", "dependents": [557], "id": 169, "thread": "build-42"}, {"duration": 8, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#gatherMvnpmJars", "started": "21:33:43.713", "dependents": [578, 583], "id": 65, "thread": "build-34"}, {"duration": 8, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initStatic", "started": "21:33:45.177", "dependents": [495, 594], "id": 490, "thread": "build-44"}, {"duration": 8, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigClasses", "started": "21:33:45.336", "dependents": [594], "id": 515, "thread": "build-16"}, {"duration": 8, "stepId": "io.quarkus.flyway.deployment.devui.FlywayDevUIProcessor#create", "started": "21:33:44.661", "dependents": [573, 594, 543], "id": 434, "thread": "build-78"}, {"duration": 8, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#collectEventConsumers", "started": "21:33:45.142", "dependents": [495, 481], "id": 477, "thread": "build-24"}, {"duration": 8, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#httpRoot", "started": "21:33:43.728", "dependents": [566, 587, 565, 551, 263, 583, 586], "id": 105, "thread": "build-56"}, {"duration": 8, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#finalizeRouter", "started": "21:33:48.204", "dependents": [590, 589, 594, 588], "id": 586, "thread": "build-16"}, {"duration": 8, "stepId": "io.quarkus.datasource.deployment.devservices.DevServicesDatasourceProcessor#launchDatabases", "started": "21:33:44.717", "dependents": [535, 538, 539], "id": 440, "thread": "build-16"}, {"duration": 8, "stepId": "io.quarkus.hibernate.reactive.deployment.ResteasyReactiveServerIntegrationProcessor#unwrappedExceptions", "started": "21:33:43.750", "dependents": [407], "id": 168, "thread": "build-4"}, {"duration": 8, "stepId": "io.quarkus.vertx.http.deployment.devmode.NotFoundProcessor#routeNotFound", "started": "21:33:48.204", "dependents": [594], "id": 587, "thread": "build-113"}, {"duration": 8, "stepId": "io.quarkus.flyway.deployment.FlywayProcessor#createBeans", "started": "21:33:44.661", "dependents": [531, 530, 492, 466, 494, 490, 594, 493, 441], "id": 433, "thread": "build-31"}, {"duration": 7, "stepId": "io.quarkus.hibernate.orm.deployment.GraalVMFeatures#registerGeneratorClassesForReflections", "started": "21:33:43.748", "dependents": [592], "id": 159, "thread": "build-62"}, {"duration": 7, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#configValidator", "started": "21:33:44.800", "dependents": [546, 592], "id": 462, "thread": "build-73"}, {"duration": 7, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForParamConverters_dcdfdd2a310a09abe5ee3f0ed2b2bc49f36f3d07", "started": "21:33:44.531", "dependents": [548, 559, 466, 592, 441], "id": 398, "thread": "build-73"}, {"duration": 7, "stepId": "io.quarkus.devui.deployment.menu.ReportIssuesProcessor#registerJsonRpcService", "started": "21:33:43.754", "dependents": [544, 319], "id": 177, "thread": "build-53"}, {"duration": 7, "stepId": "io.quarkus.deployment.CollectionClassProcessor#setupCollectionClasses", "started": "21:33:43.702", "dependents": [592], "id": 18, "thread": "build-12"}, {"duration": 7, "stepId": "io.quarkus.security.deployment.SecurityProcessor#prepareBouncyCastleProviders", "started": "21:33:43.719", "dependents": [592], "id": 74, "thread": "build-42"}, {"duration": 7, "stepId": "io.quarkus.datasource.deployment.devui.DevUIDatasourceProcessor#create", "started": "21:33:43.708", "dependents": [573, 543], "id": 43, "thread": "build-13"}, {"duration": 7, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#additionalProviders", "started": "21:33:45.794", "dependents": [558, 556, 554], "id": 553, "thread": "build-34"}, {"duration": 7, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#setupEndpoints", "started": "21:33:45.649", "dependents": [558, 592, 556, 554], "id": 533, "thread": "build-151"}, {"duration": 7, "stepId": "io.quarkus.security.jpa.deployment.QuarkusSecurityJpaProcessor#configureJpaAuthConfig", "started": "21:33:44.658", "dependents": [441], "id": 432, "thread": "build-28"}, {"duration": 7, "stepId": "io.quarkus.vertx.http.deployment.GeneratedStaticResourcesProcessor#devMode", "started": "21:33:43.750", "dependents": [438, 295, 186], "id": 165, "thread": "build-70"}, {"duration": 7, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesMethodsProcessor#reactiveRoutesMethods", "started": "21:33:43.729", "dependents": [445], "id": 110, "thread": "build-53"}, {"duration": 7, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#responseStatusSupport", "started": "21:33:43.750", "dependents": [548], "id": 166, "thread": "build-59"}, {"duration": 7, "stepId": "io.quarkus.devui.deployment.menu.DependenciesProcessor#createAppDeps", "started": "21:33:43.714", "dependents": [576], "id": 63, "thread": "build-12"}, {"duration": 7, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#configureAgroalConnection", "started": "21:33:43.770", "dependents": [466, 441], "id": 233, "thread": "build-62"}, {"duration": 7, "stepId": "io.quarkus.flyway.deployment.FlywayProcessor#build", "started": "21:33:44.650", "dependents": [433, 592, 527, 594, 438], "id": 425, "thread": "build-28"}, {"duration": 7, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#config", "started": "21:33:43.748", "dependents": [546], "id": 156, "thread": "build-29"}, {"duration": 7, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#startTesting", "started": "21:33:44.137", "dependents": [589, 439, 588], "id": 337, "thread": "build-31"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.UnremovableAnnotationsProcessor#unremovableBeans", "started": "21:33:43.736", "dependents": [510, 498], "id": 124, "thread": "build-21"}, {"duration": 7, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#additionalBean", "started": "21:33:43.746", "dependents": [466, 441], "id": 154, "thread": "build-55"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ReflectiveBeanClassesProcessor#implicitReflectiveBeanClasses", "started": "21:33:45.142", "dependents": [520], "id": 476, "thread": "build-44"}, {"duration": 7, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#watchConfigFiles", "started": "21:33:43.759", "dependents": [438], "id": 199, "thread": "build-42"}, {"duration": 7, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#createSynthBeansForConfiguredInjectionPoints", "started": "21:33:45.142", "dependents": [492, 490, 594, 493], "id": 475, "thread": "build-39"}, {"duration": 7, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#responseHeaderSupport", "started": "21:33:43.761", "dependents": [548], "id": 206, "thread": "build-61"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigMappingsBean", "started": "21:33:45.142", "dependents": [495], "id": 474, "thread": "build-27"}, {"duration": 7, "stepId": "io.quarkus.hibernate.orm.deployment.metrics.HibernateOrmMetricsProcessor#metrics", "started": "21:33:45.652", "dependents": [594], "id": 534, "thread": "build-51"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#configPropertyInjectionPoints", "started": "21:33:45.323", "dependents": [513, 592, 511], "id": 505, "thread": "build-14"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigPropertiesBean", "started": "21:33:45.142", "dependents": [495], "id": 472, "thread": "build-16"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#unremovableBeans", "started": "21:33:43.736", "dependents": [510, 498], "id": 123, "thread": "build-54"}, {"duration": 7, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#defaultUnwrappedExceptions", "started": "21:33:43.761", "dependents": [407], "id": 208, "thread": "build-51"}, {"duration": 6, "stepId": "io.quarkus.security.deployment.SecurityProcessor#createSecurityCheckStorage", "started": "21:33:44.793", "dependents": [548, 492, 466, 490, 594, 493, 517], "id": 460, "thread": "build-16"}, {"duration": 6, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForFeatures", "started": "21:33:44.530", "dependents": [559, 399], "id": 394, "thread": "build-16"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerSyntheticObservers", "started": "21:33:45.202", "dependents": [520, 496, 510, 592, 498, 497], "id": 495, "thread": "build-24"}, {"duration": 6, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#additionalBeans", "started": "21:33:43.746", "dependents": [466, 441], "id": 152, "thread": "build-58"}, {"duration": 6, "stepId": "io.quarkus.deployment.pkg.steps.JarResultBuildStep#outputTarget", "started": "21:33:43.712", "dependents": [566, 98, 346, 89], "id": 60, "thread": "build-24"}, {"duration": 6, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#securityContextOverrideHandler", "started": "21:33:43.762", "dependents": [559], "id": 205, "thread": "build-53"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.AutoProducerMethodsProcessor#annotationTransformer", "started": "21:33:44.745", "dependents": [466], "id": 447, "thread": "build-2"}, {"duration": 6, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#build", "started": "21:33:45.976", "dependents": [572, 571], "id": 570, "thread": "build-73"}, {"duration": 6, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#beans", "started": "21:33:43.844", "dependents": [466, 441], "id": 262, "thread": "build-71"}, {"duration": 6, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#brandingFiles", "started": "21:33:43.739", "dependents": [438], "id": 129, "thread": "build-15"}, {"duration": 6, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#registerBean", "started": "21:33:43.764", "dependents": [466, 441], "id": 214, "thread": "build-77"}, {"duration": 6, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#registerOptionalClaimProducer", "started": "21:33:45.142", "dependents": [495], "id": 469, "thread": "build-78"}, {"duration": 6, "stepId": "io.quarkus.netty.deployment.NettyProcessor#build", "started": "21:33:43.741", "dependents": [592, 491], "id": 137, "thread": "build-64"}, {"duration": 6, "stepId": "io.quarkus.security.deployment.SecurityProcessor#configurePermissionCheckers", "started": "21:33:45.142", "dependents": [492, 490, 594, 493], "id": 471, "thread": "build-31"}, {"duration": 6, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#applicationReflection", "started": "21:33:43.701", "dependents": [592], "id": 11, "thread": "build-8"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.WrongAnnotationUsageProcessor#detect", "started": "21:33:45.142", "dependents": [520], "id": 473, "thread": "build-72"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerCustomConfigBeanTypes", "started": "21:33:45.142", "dependents": [492, 592, 490, 493], "id": 470, "thread": "build-2"}, {"duration": 6, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#doNotRemoveVertxOptionsCustomizers", "started": "21:33:43.709", "dependents": [510, 498], "id": 46, "thread": "build-23"}, {"duration": 6, "stepId": "io.quarkus.devui.deployment.menu.ReadmeProcessor#createReadmePage", "started": "21:33:43.765", "dependents": [576], "id": 223, "thread": "build-69"}, {"duration": 6, "stepId": "io.quarkus.deployment.steps.CapabilityAggregationStep#aggregateCapabilities", "started": "21:33:43.757", "dependents": [466, 369, 365, 435, 192, 193, 217, 231, 559, 591, 517, 460, 378, 420, 437, 403, 200, 455, 465, 407, 557, 275, 238, 375, 224, 422, 310, 488, 225, 246, 560, 307, 371, 424, 426, 507, 298, 233, 548, 292, 216, 297, 249, 566, 317, 305, 440, 335, 366, 453, 423, 436, 318], "id": 191, "thread": "build-55"}, {"duration": 6, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#build_d182d2fe7ae008890806ec353e99fa052582ee2d", "started": "21:33:44.659", "dependents": [570, 594], "id": 431, "thread": "build-27"}, {"duration": 6, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#mapPageBuildTimeData", "started": "21:33:45.681", "dependents": [577], "id": 543, "thread": "build-34"}, {"duration": 6, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDefaultLevels", "started": "21:33:43.803", "dependents": [546, 439], "id": 256, "thread": "build-59"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#integrateEagerSecurity", "started": "21:33:44.762", "dependents": [548], "id": 453, "thread": "build-24"}, {"duration": 5, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#setUpPersistenceProviderAndWaitForVertxPool", "started": "21:33:45.179", "dependents": [530, 594, 534], "id": 489, "thread": "build-28"}, {"duration": 5, "stepId": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheJpaCommonResourceProcessor#lookupNamedQueries_5a86a91ed8ef1aa483288c8239df231983eeb766", "started": "21:33:44.633", "dependents": [416], "id": 415, "thread": "build-2"}, {"duration": 5, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#registerBeans", "started": "21:33:44.659", "dependents": [466, 441], "id": 430, "thread": "build-39"}, {"duration": 5, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#setupExceptionHandler", "started": "21:33:44.137", "dependents": [346], "id": 336, "thread": "build-60"}, {"duration": 5, "stepId": "io.quarkus.hibernate.orm.deployment.GraalVMFeatures#registerJdbcArrayTypesForReflection", "started": "21:33:43.771", "dependents": [592], "id": 230, "thread": "build-84"}, {"duration": 5, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setMinLevelForInitialConfigurator", "started": "21:33:43.708", "dependents": [594], "id": 30, "thread": "build-4"}, {"duration": 5, "stepId": "io.quarkus.scheduler.deployment.SchedulerMethodsProcessor#schedulerMethods", "started": "21:33:43.763", "dependents": [445], "id": 212, "thread": "build-62"}, {"duration": 5, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#build_9d6b7122fb368970c50c3a870d1f672392cd8afb", "started": "21:33:43.772", "dependents": [592, 491], "id": 234, "thread": "build-69"}, {"duration": 5, "stepId": "io.quarkus.arc.deployment.SplitPackageProcessor#splitPackageDetection", "started": "21:33:44.478", "dependents": [520], "id": 344, "thread": "build-26"}, {"duration": 5, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#build", "started": "21:33:45.154", "dependents": [492, 592, 490, 594, 493], "id": 482, "thread": "build-26"}, {"duration": 5, "stepId": "io.quarkus.deployment.recording.substitutions.AdditionalSubstitutionsBuildStep#additionalSubstitutions", "started": "21:33:43.709", "dependents": [594], "id": 42, "thread": "build-26"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForDynamicFeatures", "started": "21:33:44.531", "dependents": [559, 399], "id": 395, "thread": "build-72"}, {"duration": 5, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#handler", "started": "21:33:45.865", "dependents": [587, 565, 594], "id": 564, "thread": "build-151"}, {"duration": 5, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#shouldNotRemoveHttpServerOptionsCustomizers", "started": "21:33:43.710", "dependents": [510, 498], "id": 44, "thread": "build-27"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveVertxWebSocketIntegrationProcessor#scanner", "started": "21:33:43.717", "dependents": [548], "id": 67, "thread": "build-23"}, {"duration": 5, "stepId": "io.quarkus.devservices.postgresql.deployment.PostgresqlDevServicesProcessor#psqlCommand", "started": "21:33:45.660", "dependents": [552], "id": 537, "thread": "build-16"}, {"duration": 5, "stepId": "io.quarkus.vertx.http.deployment.GeneratedStaticResourcesProcessor#produceResources", "started": "21:33:43.757", "dependents": [231], "id": 186, "thread": "build-62"}, {"duration": 5, "stepId": "io.quarkus.tls.CertificatesProcessor#initializeCertificate", "started": "21:33:45.158", "dependents": [492, 490, 594, 493, 586], "id": 483, "thread": "build-24"}, {"duration": 5, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#build", "started": "21:33:45.976", "dependents": [572], "id": 568, "thread": "build-151"}, {"duration": 5, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#transformInjectionPoint", "started": "21:33:43.759", "dependents": [466], "id": 196, "thread": "build-74"}, {"duration": 5, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#registerSafeDuplicatedContextInterceptor", "started": "21:33:43.744", "dependents": [466, 441], "id": 141, "thread": "build-18"}, {"duration": 5, "stepId": "io.quarkus.netty.deployment.NettyProcessor#cleanupMacDNSInLog", "started": "21:33:43.757", "dependents": [421, 439], "id": 181, "thread": "build-29"}, {"duration": 5, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#metrics", "started": "21:33:43.717", "dependents": [466], "id": 68, "thread": "build-18"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.devui.ResteasyReactiveDevUIProcessor#createJsonRPCService", "started": "21:33:43.757", "dependents": [544, 319], "id": 180, "thread": "build-52"}, {"duration": 5, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#build", "started": "21:33:45.151", "dependents": [483, 589, 594, 588, 486], "id": 481, "thread": "build-44"}, {"duration": 5, "stepId": "io.quarkus.deployment.dev.IsolatedDevModeMain$AddApplicationClassPredicateBuildStep$1@7cf3ef7b", "started": "21:33:43.763", "dependents": [548, 466, 517], "id": 209, "thread": "build-39"}, {"duration": 5, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#overrideStandardValidationFactoryResolution", "started": "21:33:43.757", "dependents": [572], "id": 185, "thread": "build-34"}, {"duration": 5, "stepId": "io.quarkus.panache.hibernate.common.deployment.PanacheHibernateCommonResourceProcessor#replaceFieldAccesses", "started": "21:33:45.976", "dependents": [572], "id": 569, "thread": "build-16"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.ArcProcessor#loggerProducer", "started": "21:33:43.714", "dependents": [466, 441], "id": 57, "thread": "build-28"}, {"duration": 4, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#validate", "started": "21:33:45.323", "dependents": [520], "id": 504, "thread": "build-78"}, {"duration": 4, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#transactionContext", "started": "21:33:44.861", "dependents": [468], "id": 467, "thread": "build-16"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanForParameterContainers", "started": "21:33:44.530", "dependents": [392], "id": 390, "thread": "build-27"}, {"duration": 4, "stepId": "io.quarkus.vertx.http.deployment.ManagementInterfaceSecurityProcessor#setupAuthenticationMechanisms", "started": "21:33:43.987", "dependents": [466, 594, 441, 586], "id": 296, "thread": "build-27"}, {"duration": 4, "stepId": "io.quarkus.vertx.deployment.EventConsumerMethodsProcessor#eventConsumerMethods", "started": "21:33:43.759", "dependents": [445], "id": 190, "thread": "build-36"}, {"duration": 4, "stepId": "io.quarkus.security.deployment.SecurityProcessor#supportBlockingExecutionOfPermissionChecks", "started": "21:33:43.723", "dependents": [445], "id": 77, "thread": "build-53"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForInterceptors", "started": "21:33:44.532", "dependents": [409], "id": 393, "thread": "build-46"}, {"duration": 4, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#validate", "started": "21:33:45.323", "dependents": [520], "id": 503, "thread": "build-31"}, {"duration": 4, "stepId": "io.quarkus.netty.deployment.NettyProcessor#registerEventLoopBeans", "started": "21:33:44.119", "dependents": [492, 490, 594, 493], "id": 333, "thread": "build-60"}, {"duration": 4, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#devDbHandler", "started": "21:33:43.702", "dependents": [440], "id": 4, "thread": "build-13"}, {"duration": 4, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#registerServiceBinding", "started": "21:33:43.768", "dependents": [249, 440, 426, 486], "id": 225, "thread": "build-42"}, {"duration": 4, "stepId": "io.quarkus.devui.deployment.menu.EndpointsProcessor#createJsonRPCService", "started": "21:33:43.702", "dependents": [544, 319], "id": 6, "thread": "build-11"}, {"duration": 4, "stepId": "io.quarkus.deployment.ExtensionLoader#config", "started": "21:33:43.701", "dependents": [278, 75, 7, 510, 257, 346, 489, 342, 524, 327, 217, 520, 590, 559, 561, 446, 439, 19, 591, 306, 534, 517, 425, 9, 13, 30, 451, 502, 309, 378, 74, 381, 43, 458, 469, 585, 50, 521, 275, 372, 586, 471, 531, 270, 145, 576, 422, 463, 295, 271, 555, 340, 452, 527, 424, 450, 320, 426, 507, 298, 447, 16, 564, 290, 22, 31, 292, 508, 115, 23, 38, 297, 24, 562, 337, 432, 249, 266, 566, 316, 60, 317, 161, 445, 37, 546, 33, 440, 137, 453, 494, 572, 52, 459, 41, 40, 483, 344, 482, 466, 56, 48, 367, 304, 47, 338, 332, 519, 98, 58, 532, 59, 62, 594, 68, 334, 312, 473, 247, 464, 405, 460, 420, 433, 465, 341, 293, 588, 486, 282, 529, 589, 583, 457, 488, 431, 324, 307, 582, 299, 100, 294, 240, 77, 548, 514, 434, 114, 516, 88, 478, 518, 376, 78, 329, 339, 272, 76, 148, 441, 260, 379, 580, 106, 302, 300, 101, 525, 436, 82, 318, 105, 328], "id": 2, "thread": "build-3"}, {"duration": 4, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#registerReflectivelyAccessedMethods", "started": "21:33:43.763", "dependents": [592], "id": 204, "thread": "build-76"}, {"duration": 4, "stepId": "io.quarkus.datasource.deployment.devui.DevUIDatasourceProcessor#registerJsonRpcBackend", "started": "21:33:43.744", "dependents": [544, 319], "id": 138, "thread": "build-62"}, {"duration": 4, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#createJsonRPCService", "started": "21:33:43.709", "dependents": [544, 319], "id": 35, "thread": "build-12"}, {"duration": 4, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#registerSwaggerUiHandler", "started": "21:33:48.032", "dependents": [584, 585, 594], "id": 582, "thread": "build-113"}, {"duration": 4, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#configurationDescriptorBuilding", "started": "21:33:44.651", "dependents": [432, 429, 437, 435, 427, 428, 593, 532, 487, 430, 594, 438, 436, 431], "id": 424, "thread": "build-24"}, {"duration": 4, "stepId": "io.quarkus.hibernate.orm.deployment.GraalVMFeatures#registerStandardStackElementTypesForReflection", "started": "21:33:43.718", "dependents": [592], "id": 66, "thread": "build-31"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.ArcProcessor#launchMode", "started": "21:33:43.722", "dependents": [466, 441], "id": 72, "thread": "build-12"}, {"duration": 4, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#registerServiceBinding", "started": "21:33:43.768", "dependents": [249, 440, 426, 486], "id": 224, "thread": "build-80"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForExceptionMappers", "started": "21:33:44.576", "dependents": [559, 466, 592, 441], "id": 407, "thread": "build-78"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.AutoInjectFieldProcessor#annotationTransformer", "started": "21:33:44.746", "dependents": [466], "id": 446, "thread": "build-64"}, {"duration": 4, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#jpaEntitiesIndexer", "started": "21:33:44.495", "dependents": [410, 593], "id": 354, "thread": "build-26"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanForIOInterceptors", "started": "21:33:44.530", "dependents": [409], "id": 391, "thread": "build-26"}, {"duration": 3, "stepId": "io.quarkus.devui.deployment.menu.DependenciesProcessor#createBuildTimeActions", "started": "21:33:43.716", "dependents": [541], "id": 61, "thread": "build-35"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#findEnablementStereotypes", "started": "21:33:44.498", "dependents": [363, 361, 360, 362], "id": 357, "thread": "build-65"}, {"duration": 3, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#cleanupVertxWarnings", "started": "21:33:43.701", "dependents": [421, 439], "id": 1, "thread": "build-5"}, {"duration": 3, "stepId": "io.quarkus.netty.deployment.NettyProcessor#registerQualifiers", "started": "21:33:43.709", "dependents": [466, 441], "id": 28, "thread": "build-25"}, {"duration": 3, "stepId": "io.quarkus.credentials.CredentialsProcessor#unremoveable", "started": "21:33:43.733", "dependents": [510, 498], "id": 102, "thread": "build-60"}, {"duration": 3, "stepId": "io.quarkus.devui.deployment.menu.EndpointsProcessor#createEndpointsPage", "started": "21:33:43.767", "dependents": [576], "id": 217, "thread": "build-79"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.common.deployment.JaxrsMethodsProcessor#jaxrsMethods", "started": "21:33:44.745", "dependents": [445], "id": 444, "thread": "build-27"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#compressionSupport", "started": "21:33:43.711", "dependents": [548], "id": 38, "thread": "build-31"}, {"duration": 3, "stepId": "io.quarkus.deployment.steps.DevServicesConfigBuildStep#setup", "started": "21:33:45.656", "dependents": [546, 545, 555, 542, 589, 538, 537, 536, 588], "id": 535, "thread": "build-34"}, {"duration": 3, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#continuousTestingState", "started": "21:33:45.649", "dependents": [594], "id": 528, "thread": "build-16"}, {"duration": 3, "stepId": "io.quarkus.deployment.steps.CapabilityAggregationStep#provideCapabilities", "started": "21:33:43.713", "dependents": [191], "id": 51, "thread": "build-4"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.LookupConditionsProcessor#suppressConditionsGenerators", "started": "21:33:44.745", "dependents": [466], "id": 443, "thread": "build-31"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevServicesProcessor#devServicesAutoGenerateByDefault", "started": "21:33:45.652", "dependents": [535], "id": 532, "thread": "build-102"}, {"duration": 3, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#yamlConfig", "started": "21:33:43.705", "dependents": [546], "id": 12, "thread": "build-17"}, {"duration": 3, "stepId": "io.quarkus.vertx.http.deployment.StaticResourcesProcessor#runtimeInit", "started": "21:33:45.650", "dependents": [594, 586], "id": 529, "thread": "build-6"}, {"duration": 3, "stepId": "io.quarkus.deployment.steps.DevServicesConfigBuildStep#deprecated", "started": "21:33:43.763", "dependents": [535], "id": 201, "thread": "build-64"}, {"duration": 3, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#feature", "started": "21:33:43.701", "dependents": [594], "id": 3, "thread": "build-4"}, {"duration": 3, "stepId": "io.quarkus.devui.deployment.welcome.WelcomeProcessor#createWelcomePages", "started": "21:33:47.543", "dependents": [576], "id": 575, "thread": "build-114"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#convertJpaResourceAnnotationsToQualifier", "started": "21:33:44.659", "dependents": [466], "id": 429, "thread": "build-24"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ArcProcessor#quarkusMain", "started": "21:33:43.752", "dependents": [266, 466, 441], "id": 160, "thread": "build-52"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#registerStartupObservers", "started": "21:33:45.209", "dependents": [498], "id": 497, "thread": "build-39"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateConfigPropertiesInjectionPoints", "started": "21:33:45.322", "dependents": [515], "id": 502, "thread": "build-24"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#providersFromClasspath", "started": "21:33:43.742", "dependents": [558, 556, 554], "id": 127, "thread": "build-66"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ArcProcessor#initializeContainer", "started": "21:33:45.640", "dependents": [594, 522], "id": 521, "thread": "build-51"}, {"duration": 3, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#additionalBean", "started": "21:33:43.761", "dependents": [466, 441], "id": 195, "thread": "build-73"}, {"duration": 3, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#runtimeOverrideConfig", "started": "21:33:43.773", "dependents": [546], "id": 232, "thread": "build-85"}, {"duration": 3, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#mutinyReturnTypes", "started": "21:33:43.769", "dependents": [570, 568], "id": 226, "thread": "build-51"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#registerMonitoringComponents", "started": "21:33:43.866", "dependents": [466, 441], "id": 266, "thread": "build-22"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDevModeProcessor#openCommand", "started": "21:33:45.792", "dependents": [552], "id": 551, "thread": "build-51"}, {"duration": 2, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerOpenApiSchemaClassesForReflection", "started": "21:33:44.771", "dependents": [592, 557], "id": 455, "thread": "build-24"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#additionalBeans", "started": "21:33:44.536", "dependents": [466, 592, 441], "id": 399, "thread": "build-16"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#runtimeConfiguration", "started": "21:33:45.862", "dependents": [594, 562], "id": 561, "thread": "build-71"}, {"duration": 2, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#convertRoutes", "started": "21:33:45.870", "dependents": [584, 585], "id": 565, "thread": "build-16"}, {"duration": 2, "stepId": "io.quarkus.deployment.SecureRandomProcessor#registerReflectiveMethods", "started": "21:33:43.705", "dependents": [592], "id": 10, "thread": "build-18"}, {"duration": 2, "stepId": "io.quarkus.deployment.ide.IdeProcessor#effectiveIde", "started": "21:33:44.087", "dependents": [336, 576, 331, 346], "id": 327, "thread": "build-2"}, {"duration": 2, "stepId": "io.quarkus.flyway.deployment.FlywayProcessor#startActions", "started": "21:33:45.649", "dependents": [531, 530, 589, 532, 594, 534, 588], "id": 527, "thread": "build-28"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.init.InitializationTaskProcessor#startApplicationInitializer", "started": "21:33:45.652", "dependents": [594], "id": 531, "thread": "build-34"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#unremovableBeans", "started": "21:33:44.535", "dependents": [510, 498], "id": 396, "thread": "build-26"}, {"duration": 2, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDarkeningDefault", "started": "21:33:43.739", "dependents": [546], "id": 117, "thread": "build-55"}, {"duration": 2, "stepId": "io.quarkus.deployment.steps.DevModeBuildStep#watchChanges", "started": "21:33:43.716", "dependents": [438], "id": 58, "thread": "build-13"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#handleInitialSql", "started": "21:33:44.658", "dependents": [433, 434], "id": 428, "thread": "build-44"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#generateCustomProducer", "started": "21:33:44.535", "dependents": [466, 441], "id": 397, "thread": "build-27"}, {"duration": 2, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#loadAllBuildTimeTemplates", "started": "21:33:47.883", "dependents": [583], "id": 579, "thread": "build-114"}, {"duration": 2, "stepId": "io.quarkus.netty.deployment.NettyProcessor#cleanupUnsafeLog", "started": "21:33:43.726", "dependents": [421, 439], "id": 79, "thread": "build-55"}, {"duration": 2, "stepId": "io.quarkus.deployment.recording.AnnotationProxyBuildStep#build", "started": "21:33:44.067", "dependents": [482, 481], "id": 325, "thread": "build-2"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#registerHibernateOrmMetadataForCoreDialects", "started": "21:33:43.748", "dependents": [424, 426], "id": 144, "thread": "build-42"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#unremovableBeans", "started": "21:33:43.715", "dependents": [510, 498], "id": 55, "thread": "build-31"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ArcProcessor#notifyBeanContainerListeners", "started": "21:33:45.643", "dependents": [594, 523], "id": 522, "thread": "build-16"}, {"duration": 2, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#feature", "started": "21:33:43.716", "dependents": [594], "id": 59, "thread": "build-26"}, {"duration": 2, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#initializeRouter", "started": "21:33:48.201", "dependents": [587, 594, 586], "id": 585, "thread": "build-114"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#startPersistenceUnits", "started": "21:33:45.652", "dependents": [589, 594, 588], "id": 530, "thread": "build-44"}, {"duration": 2, "stepId": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsProcessor#devuiJsonRpcServices", "started": "21:33:43.705", "dependents": [445], "id": 8, "thread": "build-19"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#handleClassLevelExceptionMappers", "started": "21:33:44.531", "dependents": [548, 592], "id": 389, "thread": "build-49"}, {"duration": 2, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#registerHttpAuthMechanismAnnotations", "started": "21:33:43.754", "dependents": [378], "id": 163, "thread": "build-55"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ObservabilityProcessor#preAuthFailureFilter", "started": "21:33:45.862", "dependents": [563, 594, 564, 586], "id": 560, "thread": "build-151"}, {"duration": 2, "stepId": "io.quarkus.security.deployment.SecurityProcessor#produceJcaSecurityProviders", "started": "21:33:43.711", "dependents": [74, 272, 148], "id": 31, "thread": "build-29"}, {"duration": 2, "stepId": "io.quarkus.deployment.dev.ConfigureDisableInstrumentationBuildStep#configure", "started": "21:33:43.743", "dependents": [589, 588], "id": 128, "thread": "build-42"}, {"duration": 2, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#waitForVertxPool", "started": "21:33:45.177", "dependents": [489, 488], "id": 487, "thread": "build-26"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#customExceptionMappers", "started": "21:33:43.704", "dependents": [406], "id": 5, "thread": "build-16"}, {"duration": 2, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#lookupNamedQueries_5a86a91ed8ef1aa483288c8239df231983eeb766", "started": "21:33:44.634", "dependents": [414], "id": 413, "thread": "build-64"}, {"duration": 2, "stepId": "io.quarkus.security.deployment.SecurityProcessor#authorizationController", "started": "21:33:43.707", "dependents": [466, 441], "id": 19, "thread": "build-5"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#additionalReflection", "started": "21:33:45.801", "dependents": [592], "id": 556, "thread": "build-71"}, {"duration": 2, "stepId": "io.quarkus.security.deployment.SecurityProcessor#createPermissionSecurityChecksBuilder", "started": "21:33:44.762", "dependents": [458, 471], "id": 452, "thread": "build-2"}, {"duration": 2, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#openSocket", "started": "21:33:48.214", "dependents": [592, 594], "id": 591, "thread": "build-9"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ArcProcessor#signalBeanContainerReady", "started": "21:33:45.646", "dependents": [528, 530, 527, 533, 524, 586, 548, 559, 529, 558, 587, 589, 525, 594, 547, 526], "id": 523, "thread": "build-51"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#registerCustomExceptionMappers", "started": "21:33:43.706", "dependents": [406], "id": 15, "thread": "build-20"}, {"duration": 1, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#handleJsonAnnotations", "started": "21:33:45.792", "dependents": [553, 592, 594], "id": 550, "thread": "build-151"}, {"duration": 1, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#addDefaultAuthFailureHandler", "started": "21:33:45.864", "dependents": [594, 564, 586], "id": 563, "thread": "build-16"}, {"duration": 1, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#processFooterLogs", "started": "21:33:45.679", "dependents": [541, 573, 543], "id": 540, "thread": "build-16"}, {"duration": 1, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#produceEagerSecurityInterceptorStorage", "started": "21:33:44.528", "dependents": [492, 490, 594, 493], "id": 382, "thread": "build-47"}, {"duration": 1, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#installCliCommands", "started": "21:33:45.794", "dependents": [589, 588], "id": 552, "thread": "build-151"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.ProfileBuildStep#defaultProfile", "started": "21:33:43.714", "dependents": [546], "id": 49, "thread": "build-32"}, {"duration": 1, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#registerBean", "started": "21:33:43.709", "dependents": [466, 441], "id": 21, "thread": "build-20"}, {"duration": 1, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createKnownInternalImportMap", "started": "21:33:43.737", "dependents": [578], "id": 112, "thread": "build-56"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#registerShutdownObservers", "started": "21:33:45.209", "dependents": [498], "id": 496, "thread": "build-44"}, {"duration": 1, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#applicationSpecificUnwrappedExceptions", "started": "21:33:44.499", "dependents": [407], "id": 356, "thread": "build-33"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#multitenancy", "started": "21:33:44.658", "dependents": [492, 510, 498, 490, 594, 493], "id": 427, "thread": "build-31"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheJpaCommonResourceProcessor#buildNamedQueryMap", "started": "21:33:44.638", "dependents": [594], "id": 416, "thread": "build-24"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.LifecycleEventsBuildStep#startupEvent", "started": "21:33:48.213", "dependents": [594, 591], "id": 589, "thread": "build-28"}, {"duration": 1, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#startRecoveryService", "started": "21:33:45.202", "dependents": [594], "id": 494, "thread": "build-44"}, {"duration": 1, "stepId": "io.quarkus.security.deployment.SecurityProcessor#makePermissionCheckerClassBeansUnremovable", "started": "21:33:43.713", "dependents": [510, 498], "id": 37, "thread": "build-35"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#setupPersistenceProvider", "started": "21:33:45.182", "dependents": [530, 594, 534], "id": 488, "thread": "build-72"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#setupVersionField", "started": "21:33:43.767", "dependents": [592], "id": 207, "thread": "build-73"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#recordEntityToPersistenceUnit", "started": "21:33:45.983", "dependents": [594], "id": 571, "thread": "build-151"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#callInitializer", "started": "21:33:45.650", "dependents": [594], "id": 526, "thread": "build-34"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ArcProcessor#exposeCustomScopeNames", "started": "21:33:43.711", "dependents": [266, 109, 466, 448, 447, 473, 441, 91], "id": 29, "thread": "build-28"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.ShutdownListenerBuildStep#setupShutdown", "started": "21:33:48.213", "dependents": [594], "id": 590, "thread": "build-114"}, {"duration": 1, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#generateDataSourceBeans", "started": "21:33:44.648", "dependents": [530, 492, 433, 494, 423, 435, 490, 527, 424, 594, 493, 425], "id": 422, "thread": "build-24"}, {"duration": 1, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#reinitializeClassesForNetty", "started": "21:33:43.744", "dependents": [491], "id": 126, "thread": "build-55"}, {"duration": 1, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#additionalAsyncTypeMethodScanners", "started": "21:33:43.711", "dependents": [548], "id": 34, "thread": "build-32"}, {"duration": 1, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#registerScope", "started": "21:33:43.709", "dependents": [29], "id": 26, "thread": "build-24"}, {"duration": 1, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#build", "started": "21:33:44.633", "dependents": [594], "id": 412, "thread": "build-24"}, {"duration": 1, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#registerRowSetSupport", "started": "21:33:43.758", "dependents": [592], "id": 173, "thread": "build-73"}, {"duration": 1, "stepId": "io.quarkus.reactive.datasource.deployment.ReactiveDataSourceProcessor#addQualifierAsBean", "started": "21:33:43.732", "dependents": [466, 441], "id": 90, "thread": "build-5"}, {"duration": 0, "stepId": "io.quarkus.deployment.JniProcessor#setupJni", "started": "21:33:43.727", "dependents": [491], "id": 78, "thread": "build-56"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.CurateOutcomeBuildStep#removeResources", "started": "21:33:43.716", "dependents": [572], "id": 50, "thread": "build-32"}, {"duration": 0, "stepId": "io.quarkus.security.jpa.common.deployment.QuarkusSecurityJpaCommonProcessor#provideJpaSecurityDefinition", "started": "21:33:44.648", "dependents": [432], "id": 419, "thread": "build-22"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#ensureBeanLookupAvailable", "started": "21:33:43.761", "dependents": [510, 498], "id": 176, "thread": "build-69"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#mapDeploymentMethods", "started": "21:33:45.681", "dependents": [544, 547], "id": 541, "thread": "build-6"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.AdditionalClassLoaderResourcesBuildStep#appendAdditionalClassloaderResources", "started": "21:33:43.762", "dependents": [345], "id": 184, "thread": "build-39"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#candidatesForFieldAccess", "started": "21:33:44.634", "dependents": [417], "id": 411, "thread": "build-31"}, {"duration": 0, "stepId": "io.quarkus.deployment.index.ApplicationArchiveBuildStep#addConfiguredIndexedDependencies", "started": "21:33:43.717", "dependents": [342], "id": 52, "thread": "build-40"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#quarkusApplication", "started": "21:33:44.497", "dependents": [466, 441], "id": 347, "thread": "build-47"}, {"duration": 0, "stepId": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsProcessor#check", "started": "21:33:44.749", "dependents": [], "id": 445, "thread": "build-31"}, {"duration": 0, "stepId": "io.quarkus.security.jpa.deployment.QuarkusSecurityJpaProcessor#panacheEntityPredicate", "started": "21:33:44.648", "dependents": [432, 419], "id": 418, "thread": "build-24"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#marker", "started": "21:33:43.752", "dependents": [342], "id": 150, "thread": "build-18"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#produceModel", "started": "21:33:43.750", "dependents": [567, 593, 354], "id": 146, "thread": "build-18"}, {"duration": 0, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setProperty", "started": "21:33:43.729", "dependents": [594], "id": 83, "thread": "build-5"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#perClassExceptionMapperSupport", "started": "21:33:44.531", "dependents": [466], "id": 385, "thread": "build-71"}, {"duration": 0, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#enableSslInNative", "started": "21:33:43.708", "dependents": [491], "id": 13, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.AutoInjectFieldProcessor#autoInjectQualifiers", "started": "21:33:44.746", "dependents": [446, 448], "id": 442, "thread": "build-39"}, {"duration": 0, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#registerRSASigProvider", "started": "21:33:43.728", "dependents": [148], "id": 82, "thread": "build-58"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#contributeQuarkusConfigToJpaModel", "started": "21:33:43.710", "dependents": [410], "id": 23, "thread": "build-5"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#addAllWriteableMarker", "started": "21:33:45.802", "dependents": [572], "id": 554, "thread": "build-51"}, {"duration": 0, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#missingDevUIMessageHandler", "started": "21:33:44.137", "dependents": [589, 588], "id": 335, "thread": "build-47"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.devui.ResteasyReactiveDevUIProcessor#createPages", "started": "21:33:43.755", "dependents": [573, 543], "id": 157, "thread": "build-72"}, {"duration": 0, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#feature", "started": "21:33:43.748", "dependents": [594], "id": 142, "thread": "build-69"}, {"duration": 0, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#preventLoggerContention", "started": "21:33:43.769", "dependents": [256], "id": 211, "thread": "build-53"}, {"duration": 0, "stepId": "io.quarkus.deployment.ForkJoinPoolProcessor#setProperty", "started": "21:33:43.765", "dependents": [594], "id": 197, "thread": "build-73"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.StaticResourcesProcessor#indexHtmlFile", "started": "21:33:43.768", "dependents": [438], "id": 203, "thread": "build-80"}, {"duration": 0, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#watchYamlConfig", "started": "21:33:43.752", "dependents": [438], "id": 151, "thread": "build-41"}, {"duration": 0, "stepId": "io.quarkus.scheduler.deployment.devui.SchedulerDevUIProcessor#page", "started": "21:33:45.154", "dependents": [573, 543], "id": 480, "thread": "build-24"}, {"duration": 0, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#produceCoroutineScope", "started": "21:33:43.711", "dependents": [466, 441], "id": 25, "thread": "build-28"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#initializeRolesAllowedConfigExp", "started": "21:33:45.323", "dependents": [594], "id": 499, "thread": "build-26"}, {"duration": 0, "stepId": "io.quarkus.flyway.deployment.FlywayAlwaysEnabledProcessor#indexFlyway", "started": "21:33:43.725", "dependents": [342], "id": 73, "thread": "build-31"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#featureBuildItem", "started": "21:33:43.729", "dependents": [594], "id": 85, "thread": "build-55"}, {"duration": 0, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#featureAndCapability", "started": "21:33:43.757", "dependents": [191, 594], "id": 164, "thread": "build-62"}, {"duration": 0, "stepId": "io.quarkus.security.jpa.deployment.QuarkusSecurityJpaProcessor#feature", "started": "21:33:43.725", "dependents": [594], "id": 70, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initMtlsClientAuth", "started": "21:33:43.708", "dependents": [466, 441], "id": 16, "thread": "build-25"}, {"duration": 0, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#logCleanupFilters", "started": "21:33:43.748", "dependents": [421, 439], "id": 139, "thread": "build-60"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#feature", "started": "21:33:43.742", "dependents": [594], "id": 118, "thread": "build-62"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#unremovableAsyncObserverExceptionHandlers", "started": "21:33:43.737", "dependents": [510, 498], "id": 107, "thread": "build-55"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#enrollBeanValidationTypeSafeActivatorForReflection", "started": "21:33:43.767", "dependents": [592], "id": 200, "thread": "build-73"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#aggregateParameterContainers", "started": "21:33:44.534", "dependents": [548, 396, 397, 449], "id": 392, "thread": "build-49"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ExecutorServiceProcessor#executorServiceBean", "started": "21:33:44.028", "dependents": [492, 490, 493], "id": 313, "thread": "build-54"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#buildSetup", "started": "21:33:43.752", "dependents": [594], "id": 149, "thread": "build-53"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#includeArchivesHostingEntityPackagesInIndex", "started": "21:33:43.720", "dependents": [342], "id": 62, "thread": "build-46"}, {"duration": 0, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#logCleanup", "started": "21:33:43.736", "dependents": [421, 439], "id": 104, "thread": "build-52"}, {"duration": 0, "stepId": "io.quarkus.deployment.ConstructorPropertiesProcessor#build", "started": "21:33:44.500", "dependents": [592], "id": 355, "thread": "build-27"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#featureBuildItem", "started": "21:33:43.722", "dependents": [594], "id": 64, "thread": "build-52"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#pathInterfaceImpls", "started": "21:33:44.531", "dependents": [466, 441], "id": 386, "thread": "build-14"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.ReportIssuesProcessor#createReportIssuePage", "started": "21:33:43.762", "dependents": [576], "id": 183, "thread": "build-64"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#configureHandlers", "started": "21:33:45.864", "dependents": [594], "id": 562, "thread": "build-151"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.BuildMetricsProcessor#createBuildMetricsPages", "started": "21:33:43.734", "dependents": [576], "id": 95, "thread": "build-55"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#createConfigurationPages", "started": "21:33:45.660", "dependents": [576], "id": 536, "thread": "build-28"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#feature", "started": "21:33:43.726", "dependents": [594], "id": 76, "thread": "build-24"}, {"duration": 0, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#reduceLogging", "started": "21:33:43.762", "dependents": [256], "id": 188, "thread": "build-76"}, {"duration": 0, "stepId": "io.quarkus.deployment.ExtensionLoader#booleanSupplierFactory", "started": "21:33:43.708", "dependents": [51], "id": 14, "thread": "build-24"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#createContinuousTestingPages", "started": "21:33:43.748", "dependents": [576], "id": 140, "thread": "build-59"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateLogFilterBuildStep#setupLogFilters", "started": "21:33:43.725", "dependents": [421, 439], "id": 71, "thread": "build-24"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#feature", "started": "21:33:43.732", "dependents": [594], "id": 87, "thread": "build-55"}, {"duration": 0, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#logCleanup", "started": "21:33:43.744", "dependents": [421, 439], "id": 122, "thread": "build-3"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.devmode.NotFoundProcessor#resourceNotFoundDataAvailable", "started": "21:33:43.771", "dependents": [466, 441], "id": 220, "thread": "build-61"}, {"duration": 0, "stepId": "io.quarkus.jdbc.postgresql.deployment.PostgreSQLJDBCReflections#build", "started": "21:33:43.713", "dependents": [592], "id": 32, "thread": "build-35"}, {"duration": 0, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#transformSchedulerBeans", "started": "21:33:43.844", "dependents": [466], "id": 261, "thread": "build-24"}, {"duration": 0, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#configFile", "started": "21:33:43.709", "dependents": [438], "id": 20, "thread": "build-5"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateUserTypeProcessor#build", "started": "21:33:44.499", "dependents": [592], "id": 349, "thread": "build-33"}, {"duration": 0, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#feature", "started": "21:33:43.711", "dependents": [594], "id": 27, "thread": "build-24"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#suppressNonRuntimeConfigChanged", "started": "21:33:43.709", "dependents": [309], "id": 17, "thread": "build-24"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createRelocationMap", "started": "21:33:43.763", "dependents": [578], "id": 194, "thread": "build-69"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.ExtensionsProcessor#createExtensionsPages", "started": "21:33:47.544", "dependents": [576], "id": 574, "thread": "build-113"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#transformSecurityAnnotations", "started": "21:33:44.528", "dependents": [466], "id": 379, "thread": "build-72"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#searchForProviders", "started": "21:33:43.764", "dependents": [342], "id": 193, "thread": "build-52"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#defineTypeOfImpliedPU", "started": "21:33:44.650", "dependents": [429, 435, 424], "id": 423, "thread": "build-22"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.TestsAsBeansProcessor#testAnnotations", "started": "21:33:43.734", "dependents": [266, 466, 441], "id": 94, "thread": "build-52"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#resourceIndex", "started": "21:33:44.499", "dependents": [384, 549, 441], "id": 351, "thread": "build-33"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#produceModel", "started": "21:33:43.762", "dependents": [567, 593, 354], "id": 182, "thread": "build-69"}, {"duration": 0, "stepId": "io.quarkus.netty.deployment.NettyProcessor#limitArenaSize", "started": "21:33:43.741", "dependents": [594], "id": 115, "thread": "build-34"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#validateAsyncObserverExceptionHandlers", "started": "21:33:45.323", "dependents": [520], "id": 500, "thread": "build-47"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.ManagementInterfaceSecurityProcessor#initializeAuthMechanismHandler", "started": "21:33:45.650", "dependents": [594], "id": 525, "thread": "build-44"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setMinimalNettyMaxOrderSize", "started": "21:33:43.736", "dependents": [137, 115], "id": 103, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.TestsAsBeansProcessor#testClassBeans", "started": "21:33:43.739", "dependents": [466, 441], "id": 113, "thread": "build-62"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.PreloadClassesBuildStep#registerPreInitClasses", "started": "21:33:43.771", "dependents": [], "id": 219, "thread": "build-4"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#collectEntityClasses", "started": "21:33:44.499", "dependents": [568], "id": 353, "thread": "build-46"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#createAllRoutes", "started": "21:33:48.032", "dependents": [583], "id": 581, "thread": "build-114"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#contributePersistenceXmlToJpaModel", "started": "21:33:44.108", "dependents": [410], "id": 330, "thread": "build-60"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.BannerProcessor#watchBannerChanges", "started": "21:33:43.707", "dependents": [438], "id": 9, "thread": "build-13"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#resolveConfigExpressionRoles", "started": "21:33:44.793", "dependents": [594], "id": 459, "thread": "build-73"}, {"duration": 0, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#feature", "started": "21:33:43.718", "dependents": [594], "id": 56, "thread": "build-43"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#produceLoggingCategories", "started": "21:33:43.715", "dependents": [256], "id": 40, "thread": "build-35"}, {"duration": 0, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#contributeClassesToIndex", "started": "21:33:43.762", "dependents": [345], "id": 187, "thread": "build-52"}, {"duration": 0, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#handle", "started": "21:33:43.757", "dependents": [421, 439], "id": 162, "thread": "build-36"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#setUpDenyAllJaxRs", "started": "21:33:43.713", "dependents": [460], "id": 33, "thread": "build-29"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#conditionTransformer", "started": "21:33:44.514", "dependents": [466], "id": 364, "thread": "build-26"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.CurateOutcomeBuildStep#curateOutcome", "started": "21:33:43.711", "dependents": [65, 324, 51, 575, 346, 573, 426, 342, 179, 543, 63, 434, 577, 541, 544, 249, 74, 50, 191, 374, 61, 291, 238, 486, 580, 440, 576, 572], "id": 24, "thread": "build-5"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveLogFilter#setupLogFilters", "started": "21:33:43.717", "dependents": [421, 439], "id": 53, "thread": "build-40"}, {"duration": 0, "stepId": "io.quarkus.elytron.security.common.deployment.QuarkusSecurityCommonProcessor#services", "started": "21:33:43.714", "dependents": [592], "id": 36, "thread": "build-12"}, {"duration": 0, "stepId": "io.quarkus.deployment.pkg.steps.NativeImageBuildStep#ignoreBuildPropertyChanges", "started": "21:33:43.715", "dependents": [309], "id": 39, "thread": "build-38"}, {"duration": 0, "stepId": "io.quarkus.flyway.deployment.FlywayAlwaysEnabledProcessor#build", "started": "21:33:43.746", "dependents": [594], "id": 133, "thread": "build-54"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ObserverValidationProcessor#validateApplicationObserver", "started": "21:33:45.323", "dependents": [520], "id": 501, "thread": "build-49"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.LoggingBeanSupportProcessor#discoveredComponents", "started": "21:33:43.769", "dependents": [266, 466, 441], "id": 210, "thread": "build-61"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#create", "started": "21:33:43.746", "dependents": [573, 543], "id": 132, "thread": "build-42"}, {"duration": 0, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#agroal", "started": "21:33:43.771", "dependents": [594], "id": 222, "thread": "build-77"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#unremovableContextMethodParams", "started": "21:33:44.531", "dependents": [510, 498], "id": 387, "thread": "build-60"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForParamConverters_59e3169e3a646b7fcf3083416f558434b73816c5", "started": "21:33:44.530", "dependents": [398], "id": 383, "thread": "build-31"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#registerAnnotations", "started": "21:33:43.754", "dependents": [266, 466, 441], "id": 155, "thread": "build-58"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.HotDeploymentConfigBuildStep#configFile", "started": "21:33:43.746", "dependents": [438], "id": 136, "thread": "build-68"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#unknownConfigFiles", "started": "21:33:44.478", "dependents": [594], "id": 343, "thread": "build-49"}, {"duration": 0, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLogFilters", "started": "21:33:43.732", "dependents": [421, 439], "id": 86, "thread": "build-3"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#additionalBeans", "started": "21:33:43.742", "dependents": [466, 441], "id": 116, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#buildNamedQueryMap", "started": "21:33:44.636", "dependents": [594], "id": 414, "thread": "build-24"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#notFoundRoutes", "started": "21:33:48.201", "dependents": [587], "id": 584, "thread": "build-113"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#subResourcesAsBeans", "started": "21:33:44.532", "dependents": [466, 510, 498, 441], "id": 388, "thread": "build-47"}, {"duration": 0, "stepId": "io.quarkus.deployment.SslProcessor#setupNativeSsl", "started": "21:33:43.715", "dependents": [422, 297, 45, 491], "id": 41, "thread": "build-31"}, {"duration": 0, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#registerDriver", "started": "21:33:43.715", "dependents": [249], "id": 45, "thread": "build-35"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.DevServicesProcessor#createDevServicesPages", "started": "21:33:45.680", "dependents": [576], "id": 539, "thread": "build-28"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#handleFieldSecurity", "started": "21:33:45.792", "dependents": [550], "id": 549, "thread": "build-34"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#checkTransactionsSupport", "started": "21:33:43.770", "dependents": [520], "id": 216, "thread": "build-61"}, {"duration": 0, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#unremoveableBeans", "started": "21:33:43.729", "dependents": [510, 498], "id": 84, "thread": "build-3"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initializeAuthenticationHandler", "started": "21:33:45.650", "dependents": [594], "id": 524, "thread": "build-102"}, {"duration": 0, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#autoRegisterModules", "started": "21:33:44.499", "dependents": [404], "id": 352, "thread": "build-33"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#collectEntityClasses", "started": "21:33:44.498", "dependents": [570], "id": 348, "thread": "build-14"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#ensureBeanLookupAvailable", "started": "21:33:43.737", "dependents": [510, 498], "id": 108, "thread": "build-62"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveAlwaysEnabledProcessor#feature", "started": "21:33:43.734", "dependents": [594], "id": 93, "thread": "build-54"}, {"duration": 0, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDefaultLogCleanupFilters", "started": "21:33:44.648", "dependents": [546], "id": 421, "thread": "build-22"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#gatherClassSecurityChecks", "started": "21:33:44.762", "dependents": [458], "id": 451, "thread": "build-78"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#createJsonRPCService", "started": "21:33:43.767", "dependents": [544, 319], "id": 202, "thread": "build-42"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#addPersistenceUnitAnnotationToIndex", "started": "21:33:43.763", "dependents": [345], "id": 189, "thread": "build-52"}, {"duration": 0, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#filterNettyHostsFileParsingWarn", "started": "21:33:43.771", "dependents": [421, 439], "id": 221, "thread": "build-5"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#processInterceptedStaticMethods", "started": "21:33:45.164", "dependents": [567, 592, 570, 572, 569, 568], "id": 485, "thread": "build-24"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmAlwaysEnabledProcessor#featureBuildItem", "started": "21:33:43.759", "dependents": [594], "id": 171, "thread": "build-69"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ObservabilityProcessor#methodScanner", "started": "21:33:43.764", "dependents": [548], "id": 192, "thread": "build-36"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.ApplicationInfoBuildStep#create", "started": "21:33:43.707", "dependents": [594], "id": 7, "thread": "build-4"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#transformAdditionalSecuredClassesToMethods", "started": "21:33:43.716", "dependents": [458, 379], "id": 47, "thread": "build-27"}, {"duration": 0, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#unremovableBeans", "started": "21:33:43.765", "dependents": [510, 498], "id": 198, "thread": "build-36"}, {"duration": 0, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#registerHttpAuthMechanismAnnotation", "started": "21:33:43.709", "dependents": [378], "id": 22, "thread": "build-27"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#collectInterceptedMethods", "started": "21:33:44.528", "dependents": [382, 453], "id": 380, "thread": "build-27"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#warnOfSchemaProblems", "started": "21:33:48.213", "dependents": [594], "id": 588, "thread": "build-9"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#reflections", "started": "21:33:43.746", "dependents": [592], "id": 131, "thread": "build-29"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#hotDeploymentWatchedFiles", "started": "21:33:43.716", "dependents": [438], "id": 48, "thread": "build-35"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.ReflectionDiagnosticProcessor#writeReflectionData", "started": "21:33:48.216", "dependents": [], "id": 592, "thread": "build-114"}, {"duration": 0, "stepId": "io.quarkus.caffeine.deployment.CaffeineProcessor#cacheLoaders", "started": "21:33:44.499", "dependents": [592], "id": 350, "thread": "build-71"}], "started": "2025-10-08T21:33:43.7", "items": [{"count": 1314, "class": "io.quarkus.deployment.builditem.ConfigDescriptionBuildItem"}, {"count": 877, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveClassBuildItem"}, {"count": 640, "class": "io.quarkus.deployment.builditem.GeneratedClassBuildItem"}, {"count": 569, "class": "io.quarkus.deployment.builditem.BytecodeTransformerBuildItem"}, {"count": 178, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveMethodBuildItem"}, {"count": 138, "class": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheNamedQueryEntityClassBuildStep"}, {"count": 138, "class": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheNamedQueryEntityClassBuildStep"}, {"count": 95, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveFieldBuildItem"}, {"count": 93, "class": "io.quarkus.hibernate.validator.spi.AdditionalConstrainedClassBuildItem"}, {"count": 80, "class": "io.quarkus.deployment.builditem.MainBytecodeRecorderBuildItem"}, {"count": 79, "class": "io.quarkus.arc.deployment.AdditionalBeanBuildItem"}, {"count": 55, "class": "io.quarkus.deployment.builditem.StaticBytecodeRecorderBuildItem"}, {"count": 45, "class": "io.quarkus.vertx.http.deployment.RouteBuildItem"}, {"count": 36, "class": "io.quarkus.deployment.builditem.nativeimage.RuntimeInitializedClassBuildItem"}, {"count": 36, "class": "io.quarkus.arc.deployment.SyntheticBeanBuildItem"}, {"count": 35, "class": "io.quarkus.deployment.builditem.HotDeploymentWatchedFileBuildItem"}, {"count": 34, "class": "io.quarkus.hibernate.reactive.panache.deployment.PanacheEntityClassBuildItem"}, {"count": 34, "class": "io.quarkus.deployment.builditem.ConfigClassBuildItem"}, {"count": 31, "class": "io.quarkus.arc.deployment.ConfigPropertyBuildItem"}, {"count": 28, "class": "io.quarkus.deployment.builditem.RunTimeConfigurationDefaultBuildItem"}, {"count": 26, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveHierarchyBuildItem"}, {"count": 26, "class": "io.quarkus.arc.deployment.UnremovableBeanBuildItem"}, {"count": 23, "class": "io.quarkus.deployment.builditem.CapabilityBuildItem"}, {"count": 23, "class": "io.quarkus.deployment.builditem.FeatureBuildItem"}, {"count": 20, "class": "io.quarkus.deployment.logging.LogCleanupFilterBuildItem"}, {"count": 16, "class": "io.quarkus.deployment.builditem.AdditionalIndexedClassesBuildItem"}, {"count": 14, "class": "io.quarkus.devui.spi.JsonRPCProvidersBuildItem"}, {"count": 12, "class": "io.quarkus.vertx.http.deployment.webjar.WebJarBuildItem"}, {"count": 11, "class": "io.quarkus.devui.deployment.DevUIWebJarBuildItem"}, {"count": 11, "class": "io.quarkus.devui.deployment.DevUIRoutesBuildItem"}, {"count": 11, "class": "io.quarkus.arc.deployment.AnnotationsTransformerBuildItem"}, {"count": 10, "class": "io.quarkus.deployment.builditem.SuppressNonRuntimeConfigChangedWarningBuildItem"}, {"count": 10, "class": "io.quarkus.deployment.builditem.AdditionalApplicationArchiveMarkerBuildItem"}, {"count": 9, "class": "io.quarkus.devui.spi.page.CardPageBuildItem"}, {"count": 9, "class": "io.quarkus.devui.deployment.InternalPageBuildItem"}, {"count": 8, "class": "io.quarkus.deployment.builditem.ConsoleCommandBuildItem"}, {"count": 8, "class": "io.quarkus.hibernate.orm.deployment.spi.DatabaseKindDialectBuildItem"}, {"count": 8, "class": "io.quarkus.resteasy.reactive.spi.ExceptionMapperBuildItem"}, {"count": 8, "class": "io.quarkus.deployment.builditem.nativeimage.RuntimeReinitializedClassBuildItem"}, {"count": 7, "class": "io.quarkus.resteasy.reactive.server.spi.MethodScannerBuildItem"}, {"count": 7, "class": "io.quarkus.devui.spi.buildtime.BuildTimeActionBuildItem"}, {"count": 7, "class": "io.quarkus.vertx.http.deployment.devmode.NotFoundPageDisplayableEndpointBuildItem"}, {"count": 7, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyWriterBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.builditem.SystemPropertyBuildItem"}, {"count": 6, "class": "io.quarkus.arc.deployment.BeanDefiningAnnotationBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.builditem.ServiceStartBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageSystemPropertyBuildItem"}, {"count": 6, "class": "io.quarkus.arc.deployment.AutoAddScopeBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsAllowedBuildItem"}, {"count": 5, "class": "io.quarkus.vertx.http.deployment.FilterBuildItem"}, {"count": 5, "class": "io.quarkus.arc.deployment.GeneratedBeanBuildItem"}, {"count": 5, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageResourceBuildItem"}, {"count": 5, "class": "io.quarkus.devui.deployment.BuildTimeConstBuildItem"}, {"count": 4, "class": "io.quarkus.vertx.http.deployment.HttpAuthMechanismAnnotationBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.RunTimeConfigBuilderBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageConfigBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyWriterOverrideBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyReaderBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.LogCategoryBuildItem"}, {"count": 4, "class": "io.quarkus.arc.deployment.BeanRegistrationPhaseBuildItem$BeanConfiguratorBuildItem"}, {"count": 4, "class": "io.quarkus.vertx.http.deployment.spi.RouteBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.server.spi.UnwrappedExceptionBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyReaderOverrideBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.StaticInitConfigBuilderBuildItem"}, {"count": 3, "class": "io.quarkus.jackson.spi.ClassPathJacksonModuleBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.ApplicationClassPredicateBuildItem"}, {"count": 3, "class": "io.quarkus.resteasy.reactive.spi.CustomExceptionMapperBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.ConfigMappingBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.GeneratedResourceBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.ShutdownListenerBuildItem"}, {"count": 2, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceInterceptorsContributorBuildItem"}, {"count": 2, "class": "io.quarkus.hibernate.orm.panache.deployment.PanacheEntityClassBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.ObjectSubstitutionBuildItem"}, {"count": 2, "class": "io.quarkus.devui.spi.buildtime.QuteTemplateBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.RecordableConstructorBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.ExtensionSslNativeSupportBuildItem"}, {"count": 2, "class": "io.quarkus.hibernate.orm.deployment.spi.AdditionalJpaModelBuildItem"}, {"count": 2, "class": "io.quarkus.smallrye.openapi.deployment.spi.AddToOpenAPIDefinitionBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.BytecodeRecorderObjectLoaderBuildItem"}, {"count": 2, "class": "io.quarkus.devui.spi.buildtime.StaticContentBuildItem"}, {"count": 2, "class": "io.quarkus.arc.deployment.BeanContainerListenerBuildItem"}, {"count": 2, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceConfigurationHandlerBuildItem"}, {"count": 2, "class": "io.quarkus.datasource.deployment.spi.DefaultDataSourceDbKindBuildItem"}, {"count": 2, "class": "io.quarkus.devui.spi.page.FooterPageBuildItem"}, {"count": 2, "class": "io.quarkus.hibernate.orm.deployment.PersistenceProviderSetUpBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.dev.testing.TestListenerBuildItem"}, {"count": 2, "class": "io.quarkus.devui.deployment.InternalImportMapBuildItem"}, {"count": 2, "class": "io.quarkus.arc.deployment.AutoInjectAnnotationBuildItem"}, {"count": 2, "class": "io.quarkus.hibernate.orm.panache.deployment.EntityToPersistenceUnitBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.MvnpmBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.AnnotationProxyBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.BytecodeRecorderConstantDefinitionBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.console.ConsoleInstalledBuildItem"}, {"count": 1, "class": "io.quarkus.panache.hibernate.common.deployment.HibernateModelClassCandidatesForFieldAccessBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.SynthesisFinishedBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageResourceBundleBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.core.deployment.EventLoopCountBuildItem"}, {"count": 1, "class": "io.quarkus.security.jpa.common.deployment.PanacheEntityPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.core.deployment.CoreVertxBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ContextResolversBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.DockerStatusBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveHierarchyIgnoreWarningBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.deployment.LocalCodecSelectorTypesBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.InitialRouterBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.openapi.deployment.spi.OpenApiDocumentBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.dev.ExceptionNotificationBuildItem"}, {"count": 1, "class": "io.quarkus.swaggerui.deployment.SwaggerUiBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.CompiledJavaVersionBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ValidationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.EventLoopSupplierBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.BooleanSupplierFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.validator.spi.BeanValidationAnnotationsBuildItem"}, {"count": 1, "class": "io.quarkus.tls.TlsRegistryBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ParamConverterProvidersBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.ImpliedBlockingPersistenceUnitTypeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.spi.HandlerConfigurationProviderBuildItem"}, {"count": 1, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceProviderBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.DevServicesLauncherConfigResultBuildItem"}, {"count": 1, "class": "io.quarkus.security.spi.AdditionalSecurityConstrainerEventPropsBuildItem"}, {"count": 1, "class": "io.quarkus.panache.common.deployment.PanacheEntityClassesBuildItem"}, {"count": 1, "class": "io.quarkus.panache.hibernate.common.deployment.HibernateEnhancersRegisteredBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationIndexBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ThreadFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.spi.JdbcDriverBuildItem"}, {"count": 1, "class": "io.quarkus.security.spi.PermissionsAllowedMetaAnnotationBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.logging.LoggingSetupBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InterceptorBindingRegistrarBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.spi.JdbcInitialSQLGeneratorBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ArcContainerBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.JsonRPCRuntimeMethodsBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.context.deployment.spi.ThreadContextProviderBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationClassNameBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.InitTaskCompletedBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.SecurityInformationBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.StreamingLogHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.dev.DisableInstrumentationForIndexPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.logging.LoggingDecorateBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.spi.GlobalHandlerCustomizerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CurrentContextFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ParameterContainersBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ConfigurationBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ApplicationResultBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.BodyHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildExclusionsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.LogCategoryMinLevelDefaultsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.IOThreadDetectorBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InvokerFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.SslNativeConfigBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CustomScopeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ServerDefaultProducesHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.integration.HibernateOrmIntegrationRuntimeConfiguredBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.IdeRunningProcessBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.TransformedClassesBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.EventLoopGroupBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelIndexBuildItem"}, {"count": 1, "class": "io.quarkus.security.deployment.SecurityProcessor$MethodSecurityChecks"}, {"count": 1, "class": "io.quarkus.arc.deployment.devui.ArcBeanInfoBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanDiscoveryFinishedBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.RunTimeConfigurationProxyBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceInterceptorsBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.VertxDevUILogBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildCompatibleExtensionsBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.ThemeVarsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelPersistenceUnitMappingBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.context.deployment.ContextPropagationInitializedBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ExceptionMappersBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InterceptorResolverBuildItem"}, {"count": 1, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceResultBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.IndexDependencyBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor$AdditionalConstrainedClassesIndexBuildItem"}, {"count": 1, "class": "io.quarkus.security.deployment.PermissionSecurityChecksBuilderBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanArchiveIndexBuildItem"}, {"count": 1, "class": "io.quarkus.jackson.spi.JacksonModuleBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ConsoleFormatterBannerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.SuppressConditionGeneratorBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildTimeEnabledStereotypesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationArchivesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ContextHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.TransformedAnnotationsBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveResourceMethodEntriesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.GeneratedFileSystemResourceHandledBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.OutputTargetBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.AuthorizationPolicyInstancesBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.PreBeanContainerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InjectionPointTransformerBuildItem"}, {"count": 1, "class": "io.quarkus.panache.hibernate.common.deployment.HibernateMetamodelForFieldAccessBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.webjar.WebJarResultsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelPersistenceUnitContributionBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.MinNettyAllocatorMaxOrderBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.openapi.deployment.OpenApiFilteredIndexViewBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.NonApplicationRootPathBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.VertxWebRouterBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.CombinedIndexBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.Capabilities"}, {"count": 1, "class": "io.quarkus.devui.deployment.ExtensionsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ExecutorBuildItem"}, {"count": 1, "class": "io.quarkus.security.deployment.JCAProviderBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.SetupEndpointsResultBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDeploymentInfoBuildItem"}, {"count": 1, "class": "io.quarkus.reactive.pg.client.deployment.PgPoolBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ObserverRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceScanningResultBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ServerSerialisersBuildItem"}, {"count": 1, "class": "io.quarkus.scheduler.deployment.DiscoveredImplementationsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.deployment.VertxBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDeploymentBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanContainerBuildItem"}, {"count": 1, "class": "io.quarkus.devui.spi.buildtime.FooterLogBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.EffectiveIdeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.JaxRsResourceIndexBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.CurateOutcomeBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.HttpRootPathBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.DeploymentMethodBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.steps.CapabilityAggregationStep$CapabilitiesConfiguredInDescriptorsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationStartBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.RelocationImportMapBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor$HttpAuthenticationHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.reactive.datasource.deployment.VertxPoolBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.AggregatedParameterContainersBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ContextRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.scheduler.deployment.SchedulerImplementationBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CustomScopeAnnotationsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.PersistenceUnitDescriptorBuildItem"}, {"count": 1, "class": "io.quarkus.flyway.deployment.FlywayProcessor$MigrationStateBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ContextRegistrationPhaseBuildItem$ContextConfiguratorBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.spi.JdbcDataSourceSchemaReadyBuildItem"}, {"count": 1, "class": "io.quarkus.panache.common.deployment.PanacheMethodCustomizerBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.BuiltInReaderOverrideBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CompletedApplicationClassPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationInfoBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.IdeFileBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.MainClassBuildItem"}], "itemsCount": 5125, "buildTarget": "quarkus-application"}