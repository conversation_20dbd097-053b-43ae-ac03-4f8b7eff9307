{"duration": 5463, "records": [{"duration": 1841, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#getAllExtensions", "started": "21:38:07.424", "dependents": [575, 581, 574, 576, 580], "id": 573, "thread": "build-87"}, {"duration": 1715, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#pregenProxies", "started": "21:38:09.020", "dependents": [594], "id": 593, "thread": "build-143"}, {"duration": 1260, "stepId": "io.quarkus.deployment.steps.ClassTransformingBuildStep#handleClassTransformation", "started": "21:38:07.760", "dependents": [593], "id": 572, "thread": "build-82"}, {"duration": 1052, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#build", "started": "21:38:06.616", "dependents": [594], "id": 566, "thread": "build-82"}, {"duration": 862, "stepId": "io.quarkus.vertx.http.deployment.webjar.WebJarProcessor#processWebJarDevMode", "started": "21:38:09.265", "dependents": [581, 594, 582], "id": 580, "thread": "build-35"}, {"duration": 805, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#enhancerDomainObjects", "started": "21:38:06.949", "dependents": [568, 570, 572, 569], "id": 567, "thread": "build-23"}, {"duration": 468, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateConfigClass", "started": "21:38:05.594", "dependents": [], "id": 341, "thread": "build-48"}, {"duration": 447, "stepId": "io.quarkus.deployment.ide.IdeProcessor#detectRunningIdeProcesses", "started": "21:38:05.582", "dependents": [337], "id": 333, "thread": "build-15"}, {"duration": 405, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#logConsoleCommand", "started": "21:38:05.554", "dependents": [553], "id": 325, "thread": "build-6"}, {"duration": 400, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#setupConsole", "started": "21:38:05.622", "dependents": [345, 440, 334, 332, 338], "id": 331, "thread": "build-24"}, {"duration": 392, "stepId": "io.quarkus.deployment.index.ApplicationArchiveBuildStep#build", "started": "21:38:05.964", "dependents": [426, 345, 344, 500, 441, 424, 586, 346, 572, 569, 343, 376], "id": 342, "thread": "build-6"}, {"duration": 371, "stepId": "io.quarkus.arc.deployment.ArcProcessor#buildCompatibleExtensions", "started": "21:38:05.523", "dependents": [466, 441], "id": 310, "thread": "build-17"}, {"duration": 348, "stepId": "io.quarkus.deployment.steps.ConfigDescriptionBuildStep#createConfigDescriptions", "started": "21:38:05.605", "dependents": [547, 543, 535], "id": 324, "thread": "build-47"}, {"duration": 341, "stepId": "io.quarkus.deployment.steps.ApplicationIndexBuildStep#build", "started": "21:38:05.623", "dependents": [466, 475, 342, 421, 431, 327, 549, 476], "id": 326, "thread": "build-66"}, {"duration": 336, "stepId": "io.quarkus.netty.deployment.NettyProcessor#eagerlyInitClass", "started": "21:38:05.524", "dependents": [594], "id": 286, "thread": "build-3"}, {"duration": 336, "stepId": "io.quarkus.elytron.security.common.deployment.QuarkusSecurityCommonProcessor#registerPasswordProviderForNative", "started": "21:38:05.523", "dependents": [594], "id": 279, "thread": "build-16"}, {"duration": 330, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#produceNamedHttpSecurityPolicies", "started": "21:38:05.525", "dependents": [493, 594, 492, 491], "id": 273, "thread": "build-14"}, {"duration": 328, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#registerJsonRpcService", "started": "21:38:05.571", "dependents": [493, 594, 317, 544, 492, 491, 541], "id": 312, "thread": "build-39"}, {"duration": 322, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#createVertxThreadFactory", "started": "21:38:05.554", "dependents": [313, 594], "id": 304, "thread": "build-23"}, {"duration": 320, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#shutdownConfigValidator", "started": "21:38:05.539", "dependents": [594], "id": 283, "thread": "build-13"}, {"duration": 308, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#create", "started": "21:38:05.551", "dependents": [594], "id": 285, "thread": "build-5"}, {"duration": 302, "stepId": "io.quarkus.virtual.threads.deployment.VirtualThreadsProcessor#setup", "started": "21:38:05.624", "dependents": [466, 493, 594, 441, 492, 491], "id": 318, "thread": "build-67"}, {"duration": 288, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#resetMapper", "started": "21:38:05.571", "dependents": [594], "id": 282, "thread": "build-28"}, {"duration": 285, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#currentContextFactory", "started": "21:38:05.590", "dependents": [594, 521], "id": 306, "thread": "build-19"}, {"duration": 281, "stepId": "io.quarkus.mutiny.deployment.MutinyProcessor#buildTimeInit", "started": "21:38:05.578", "dependents": [594], "id": 287, "thread": "build-42"}, {"duration": 281, "stepId": "io.quarkus.elytron.security.common.deployment.QuarkusSecurityCommonProcessor#registerPasswordProvider", "started": "21:38:05.578", "dependents": [594], "id": 284, "thread": "build-31"}, {"duration": 278, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#createVertxContextHandlers", "started": "21:38:05.597", "dependents": [313, 594, 315], "id": 299, "thread": "build-54"}, {"duration": 269, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#registerMetrics", "started": "21:38:05.595", "dependents": [594, 439], "id": 294, "thread": "build-41"}, {"duration": 268, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#ioThreadDetector", "started": "21:38:05.607", "dependents": [594, 308], "id": 300, "thread": "build-59"}, {"duration": 262, "stepId": "io.quarkus.arc.deployment.ArcProcessor#generateResources", "started": "21:38:07.121", "dependents": [592, 572, 521], "id": 520, "thread": "build-46"}, {"duration": 259, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#registerDevUiHandlers", "started": "21:38:10.128", "dependents": [584, 594, 585], "id": 583, "thread": "build-35"}, {"duration": 258, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#createDevUILog", "started": "21:38:05.772", "dependents": [587, 594, 540], "id": 336, "thread": "build-46"}, {"duration": 258, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#bodyHandler", "started": "21:38:05.772", "dependents": [587, 594, 517], "id": 335, "thread": "build-36"}, {"duration": 255, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#buildStatic", "started": "21:38:05.659", "dependents": [594], "id": 316, "thread": "build-78"}, {"duration": 254, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLoggingStaticInit", "started": "21:38:05.605", "dependents": [594], "id": 278, "thread": "build-9"}, {"duration": 248, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerBeans", "started": "21:38:06.677", "dependents": [473, 483, 471, 492, 478, 479, 484, 476, 493, 495, 475, 507, 474, 472, 477, 557, 470, 491, 517, 469], "id": 468, "thread": "build-17"}, {"duration": 247, "stepId": "io.quarkus.deployment.steps.ClassPathSystemPropBuildStep#set", "started": "21:38:05.613", "dependents": [594], "id": 288, "thread": "build-51"}, {"duration": 246, "stepId": "io.quarkus.security.deployment.SecurityProcessor#recordBouncyCastleProviders", "started": "21:38:05.614", "dependents": [594], "id": 289, "thread": "build-58"}, {"duration": 244, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initFormAuth", "started": "21:38:05.611", "dependents": [584, 466, 594, 441, 585], "id": 274, "thread": "build-49"}, {"duration": 241, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#helpCommand", "started": "21:38:05.535", "dependents": [553], "id": 264, "thread": "build-22"}, {"duration": 236, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#jsonDefault", "started": "21:38:05.550", "dependents": [549], "id": 266, "thread": "build-35"}, {"duration": 235, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#quitCommand", "started": "21:38:05.542", "dependents": [553], "id": 265, "thread": "build-29"}, {"duration": 232, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#checkForBuildTimeConfigChange", "started": "21:38:05.660", "dependents": [594], "id": 309, "thread": "build-63"}, {"duration": 232, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#releaseConfigOnShutdown", "started": "21:38:05.627", "dependents": [594], "id": 276, "thread": "build-44"}, {"duration": 231, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#configureLogging", "started": "21:38:05.628", "dependents": [594], "id": 280, "thread": "build-69"}, {"duration": 229, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#build", "started": "21:38:10.735", "dependents": [], "id": 594, "thread": "build-87"}, {"duration": 225, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#registerAdditionalBeans", "started": "21:38:05.672", "dependents": [510, 466, 493, 594, 441, 498, 492, 491, 448], "id": 311, "thread": "build-82"}, {"duration": 219, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#shutdown", "started": "21:38:05.640", "dependents": [594], "id": 281, "thread": "build-40"}, {"duration": 216, "stepId": "io.quarkus.deployment.dev.io.NioThreadPoolDevModeProcessor#setupTCCL", "started": "21:38:05.644", "dependents": [594], "id": 277, "thread": "build-73"}, {"duration": 212, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#createHttpAuthenticationHandler", "started": "21:38:05.663", "dependents": [524, 594, 320], "id": 305, "thread": "build-8"}, {"duration": 212, "stepId": "io.quarkus.deployment.steps.BannerProcessor#recordBanner", "started": "21:38:05.772", "dependents": [594, 439], "id": 329, "thread": "build-20"}, {"duration": 206, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#addRoutingCtxToSecurityEventsForCdiBeans", "started": "21:38:05.670", "dependents": [319, 594], "id": 303, "thread": "build-75"}, {"duration": 203, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#buildTimeRunTimeConfig", "started": "21:38:05.594", "dependents": [546, 592], "id": 271, "thread": "build-45"}, {"duration": 200, "stepId": "io.quarkus.deployment.steps.RuntimeConfigSetupBuildStep#setupRuntimeConfig", "started": "21:38:05.571", "dependents": [417, 528, 301, 486, 432, 561, 564, 485, 585, 321, 291, 328, 587, 524, 335, 489, 339, 517, 422, 336, 459, 582, 525, 594, 461, 297, 531, 589, 529, 302, 494, 591, 295, 313, 499, 562, 298, 433, 490, 290, 439, 590, 329], "id": 263, "thread": "build-4"}, {"duration": 200, "stepId": "io.quarkus.deployment.steps.PreloadClassesBuildStep#preInit", "started": "21:38:05.654", "dependents": [594], "id": 272, "thread": "build-50"}, {"duration": 199, "stepId": "io.quarkus.vertx.http.deployment.ManagementInterfaceSecurityProcessor#createManagementAuthMechHandler", "started": "21:38:05.664", "dependents": [594, 296, 525], "id": 293, "thread": "build-7"}, {"duration": 194, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#replaceDefaultAuthFailureHandler", "started": "21:38:05.663", "dependents": [587, 594, 564], "id": 275, "thread": "build-61"}, {"duration": 185, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#generateDataSourceSupportBean", "started": "21:38:05.695", "dependents": [510, 466, 493, 594, 441, 498, 492, 491], "id": 307, "thread": "build-37"}, {"duration": 180, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#parsePersistenceXmlDescriptors", "started": "21:38:05.611", "dependents": [426, 424, 270], "id": 269, "thread": "build-60"}, {"duration": 173, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setupEndpoints", "started": "21:38:07.392", "dependents": [554, 594, 551, 550, 559, 592, 572, 557], "id": 549, "thread": "build-152"}, {"duration": 170, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createBuildTimeConstJsTemplate", "started": "21:38:09.318", "dependents": [578, 579], "id": 577, "thread": "build-82"}, {"duration": 167, "stepId": "io.quarkus.agroal.deployment.AgroalMetricsProcessor#registerMetrics", "started": "21:38:05.695", "dependents": [594], "id": 292, "thread": "build-43"}, {"duration": 165, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#vertxIntegration", "started": "21:38:05.536", "dependents": [556, 557, 558], "id": 254, "thread": "build-26"}, {"duration": 160, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#getSwaggerUiFinalDestination", "started": "21:38:07.403", "dependents": [580], "id": 548, "thread": "build-93"}, {"duration": 158, "stepId": "io.quarkus.vertx.deployment.VertxJsonProcessor#registerJacksonSerDeser", "started": "21:38:05.582", "dependents": [404], "id": 261, "thread": "build-33"}, {"duration": 156, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#generateJpaConfigBean", "started": "21:38:05.772", "dependents": [493, 594, 492, 491], "id": 321, "thread": "build-33"}, {"duration": 142, "stepId": "io.quarkus.vertx.http.deployment.console.ConsoleProcessor#setupConsole", "started": "21:38:05.610", "dependents": [588, 589], "id": 262, "thread": "build-53"}, {"duration": 134, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#logging", "started": "21:38:05.541", "dependents": [239], "id": 237, "thread": "build-11"}, {"duration": 133, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#unremovableBeans", "started": "21:38:05.523", "dependents": [510, 498], "id": 207, "thread": "build-18"}, {"duration": 132, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#beanDefiningAnnotations", "started": "21:38:05.654", "dependents": [466, 441, 268], "id": 267, "thread": "build-77"}, {"duration": 132, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#autoAddScope", "started": "21:38:05.524", "dependents": [448], "id": 209, "thread": "build-7"}, {"duration": 126, "stepId": "io.quarkus.caffeine.deployment.devui.CaffeineDevUIProcessor#createCard", "started": "21:38:05.529", "dependents": [542, 573], "id": 210, "thread": "build-8"}, {"duration": 124, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#build", "started": "21:38:06.377", "dependents": [466, 594, 441, 419, 592, 439, 494, 422], "id": 417, "thread": "build-17"}, {"duration": 113, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#defineJpaEntities", "started": "21:38:06.382", "dependents": [426, 438, 436, 424, 432, 412, 427, 567, 413, 593, 437, 592, 411, 530, 414], "id": 410, "thread": "build-82"}, {"duration": 111, "stepId": "io.quarkus.netty.deployment.NettyProcessor#setNettyMachineId", "started": "21:38:05.577", "dependents": [594], "id": 248, "thread": "build-46"}, {"duration": 110, "stepId": "io.quarkus.arc.deployment.ConfigStaticInitBuildSteps#transformConfigProducer", "started": "21:38:05.576", "dependents": [466], "id": 245, "thread": "build-2"}, {"duration": 106, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createIndexHtmlTemplate", "started": "21:38:09.488", "dependents": [579], "id": 578, "thread": "build-110"}, {"duration": 105, "stepId": "io.quarkus.deployment.steps.CompiledJavaVersionBuildStep#compiledJavaVersion", "started": "21:38:05.625", "dependents": [549], "id": 257, "thread": "build-68"}, {"duration": 104, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initBasicAuth", "started": "21:38:05.772", "dependents": [493, 594, 462, 461, 492, 491], "id": 301, "thread": "build-53"}, {"duration": 103, "stepId": "io.quarkus.datasource.deployment.DataSourcesExcludedFromHealthChecksProcessor#produceBean", "started": "21:38:05.772", "dependents": [493, 594, 492, 491], "id": 297, "thread": "build-72"}, {"duration": 103, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#cors", "started": "21:38:05.772", "dependents": [587, 594, 564], "id": 298, "thread": "build-52"}, {"duration": 103, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#eventLoopCount", "started": "21:38:05.772", "dependents": [594, 486, 591], "id": 302, "thread": "build-26"}, {"duration": 103, "stepId": "io.quarkus.arc.deployment.ArcProcessor#validate", "started": "21:38:06.999", "dependents": [510, 511, 502, 506, 503, 520, 509, 508, 505, 499, 500, 507, 518, 504, 516, 572, 515, 501], "id": 498, "thread": "build-15"}, {"duration": 98, "stepId": "io.quarkus.security.deployment.SecurityProcessor#makeSecurityAnnotationsInherited", "started": "21:38:05.592", "dependents": [466], "id": 250, "thread": "build-43"}, {"duration": 92, "stepId": "io.quarkus.vertx.http.deployment.GeneratedStaticResourcesProcessor#process", "started": "21:38:05.772", "dependents": [584, 594, 586, 585], "id": 295, "thread": "build-74"}, {"duration": 90, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#exceptionMappers", "started": "21:38:05.558", "dependents": [408], "id": 193, "thread": "build-37"}, {"duration": 89, "stepId": "io.quarkus.security.deployment.SecurityProcessor#recordRuntimeConfigReady", "started": "21:38:05.772", "dependents": [594], "id": 291, "thread": "build-68"}, {"duration": 88, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#setMtlsCertificateRoleProperties", "started": "21:38:05.772", "dependents": [594], "id": 290, "thread": "build-65"}, {"duration": 82, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateMappings", "started": "21:38:06.376", "dependents": [510, 474, 464, 502, 592, 409, 465, 469], "id": 407, "thread": "build-15"}, {"duration": 81, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#setupConfigOverride", "started": "21:38:05.539", "dependents": [], "id": 158, "thread": "build-24"}, {"duration": 81, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#beans", "started": "21:38:05.554", "dependents": [466, 441], "id": 174, "thread": "build-27"}, {"duration": 80, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#createJsonRPCService", "started": "21:38:05.521", "dependents": [317, 544, 541], "id": 127, "thread": "build-12"}, {"duration": 80, "stepId": "io.quarkus.arc.deployment.devui.ArcDevModeApiProcessor#collectBeanInfo", "started": "21:38:07.103", "dependents": [519], "id": 518, "thread": "build-53"}, {"duration": 76, "stepId": "io.quarkus.deployment.CollectionClassProcessor#setupCollectionClasses", "started": "21:38:05.592", "dependents": [592], "id": 230, "thread": "build-25"}, {"duration": 76, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#build_2776f39a7cbf851c2510e1959c8b2b421193add9", "started": "21:38:05.902", "dependents": [528, 594, 330, 589, 481, 492, 585, 591, 587, 493, 588, 491, 339], "id": 328, "thread": "build-39"}, {"duration": 76, "stepId": "io.quarkus.security.deployment.SecurityProcessor#registerAdditionalBeans", "started": "21:38:05.587", "dependents": [466, 441], "id": 217, "thread": "build-34"}, {"duration": 74, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#registerAdditionalBeans", "started": "21:38:05.633", "dependents": [466, 441, 592], "id": 256, "thread": "build-36"}, {"duration": 65, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#contextInjection", "started": "21:38:05.539", "dependents": [466, 441, 446, 448], "id": 138, "thread": "build-10"}, {"duration": 63, "stepId": "io.quarkus.agroal.deployment.devui.AgroalDevUIProcessor#devUI", "started": "21:38:05.611", "dependents": [317, 544, 542, 573], "id": 236, "thread": "build-56"}, {"duration": 58, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#preinitializeRouter", "started": "21:38:05.981", "dependents": [493, 594, 492, 585, 491], "id": 339, "thread": "build-66"}, {"duration": 58, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#registerConfigs", "started": "21:38:07.403", "dependents": [594], "id": 547, "thread": "build-55"}, {"duration": 57, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#addScope", "started": "21:38:05.544", "dependents": [448], "id": 128, "thread": "build-30"}, {"duration": 57, "stepId": "io.quarkus.deployment.ide.IdeProcessor#detectIdeFiles", "started": "21:38:05.576", "dependents": [337], "id": 171, "thread": "build-36"}, {"duration": 57, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateBuilders", "started": "21:38:07.403", "dependents": [592], "id": 546, "thread": "build-29"}, {"duration": 57, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#addScope", "started": "21:38:05.547", "dependents": [448], "id": 137, "thread": "build-32"}, {"duration": 54, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#fileHandling", "started": "21:38:05.537", "dependents": [557, 558], "id": 118, "thread": "build-25"}, {"duration": 52, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#generateCustomizer", "started": "21:38:06.377", "dependents": [441], "id": 404, "thread": "build-6"}, {"duration": 52, "stepId": "io.quarkus.arc.deployment.BeanArchiveProcessor#build", "started": "21:38:06.518", "dependents": [483, 443, 466, 507, 532, 450, 458, 452, 463, 442, 515, 557, 517, 445, 455, 454, 449, 479, 549, 484, 448, 508, 447, 451, 465], "id": 441, "thread": "build-63"}, {"duration": 52, "stepId": "io.quarkus.deployment.DockerStatusProcessor#IsDockerWorking", "started": "21:38:05.563", "dependents": [538, 440], "id": 156, "thread": "build-40"}, {"duration": 51, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#unremovable", "started": "21:38:06.377", "dependents": [510, 466, 441, 498], "id": 403, "thread": "build-23"}, {"duration": 50, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#setupAuthenticationMechanisms", "started": "21:38:05.877", "dependents": [587, 466, 594, 462, 461, 441, 564], "id": 320, "thread": "build-23"}, {"duration": 50, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#testConsoleCommand", "started": "21:38:06.376", "dependents": [553], "id": 402, "thread": "build-20"}, {"duration": 50, "stepId": "io.quarkus.security.deployment.SecurityProcessor#registerSecurityInterceptors", "started": "21:38:05.876", "dependents": [466, 493, 594, 441, 492, 491], "id": 319, "thread": "build-26"}, {"duration": 50, "stepId": "io.quarkus.smallrye.jwt.build.deployment.SmallRyeJwtBuildProcessor#addClassesForReflection", "started": "21:38:05.626", "dependents": [592], "id": 242, "thread": "build-21"}, {"duration": 48, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#createJsonRPCService", "started": "21:38:05.649", "dependents": [317, 544, 541], "id": 253, "thread": "build-74"}, {"duration": 48, "stepId": "io.quarkus.security.deployment.SecurityProcessor#registerJCAProvidersForReflection", "started": "21:38:05.614", "dependents": [592], "id": 215, "thread": "build-52"}, {"duration": 48, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#compositeScheduler", "started": "21:38:05.691", "dependents": [466, 482, 441, 260, 259, 508], "id": 258, "thread": "build-46"}, {"duration": 47, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createBuildTimeData", "started": "21:38:09.270", "dependents": [578, 577], "id": 576, "thread": "build-110"}, {"duration": 45, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#beanValidationAnnotations", "started": "21:38:06.570", "dependents": [464, 465, 517], "id": 463, "thread": "build-23"}, {"duration": 45, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLoggingRuntimeInit", "started": "21:38:06.501", "dependents": [594, 592, 440, 590], "id": 439, "thread": "build-23"}, {"duration": 44, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#recordableConstructor", "started": "21:38:05.527", "dependents": [594], "id": 90, "thread": "build-20"}, {"duration": 41, "stepId": "io.quarkus.arc.deployment.LoggingBeanSupportProcessor#discoveredComponents", "started": "21:38:05.549", "dependents": [466, 441, 268], "id": 117, "thread": "build-21"}, {"duration": 41, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#implementation", "started": "21:38:05.649", "dependents": [258, 259], "id": 249, "thread": "build-65"}, {"duration": 40, "stepId": "io.quarkus.vertx.http.deployment.StaticResourcesProcessor#collectStaticResources", "started": "21:38:05.664", "dependents": [528], "id": 255, "thread": "build-72"}, {"duration": 40, "stepId": "io.quarkus.scheduler.deployment.devui.SchedulerDevUIProcessor#rpcProvider", "started": "21:38:05.581", "dependents": [317, 544], "id": 160, "thread": "build-44"}, {"duration": 39, "stepId": "io.quarkus.flyway.deployment.FlywayProcessor#reflection", "started": "21:38:06.376", "dependents": [554, 592], "id": 400, "thread": "build-39"}, {"duration": 38, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#serverSerializers", "started": "21:38:07.581", "dependents": [594, 559, 592], "id": 558, "thread": "build-152"}, {"duration": 38, "stepId": "io.quarkus.arc.deployment.ArcProcessor#setupExecutor", "started": "21:38:05.903", "dependents": [594], "id": 323, "thread": "build-37"}, {"duration": 38, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#registerInterceptors", "started": "21:38:05.644", "dependents": [466, 441], "id": 243, "thread": "build-12"}, {"duration": 36, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#securityExceptionMappers", "started": "21:38:05.568", "dependents": [408], "id": 135, "thread": "build-9"}, {"duration": 34, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#handleCustomAnnotatedMethods", "started": "21:38:06.409", "dependents": [466, 441, 406, 408], "id": 405, "thread": "build-67"}, {"duration": 34, "stepId": "io.quarkus.arc.deployment.ArcProcessor#initialize", "started": "21:38:06.639", "dependents": [518, 468, 467, 484], "id": 466, "thread": "build-17"}, {"duration": 33, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#exceptionMapper", "started": "21:38:05.529", "dependents": [592, 408], "id": 75, "thread": "build-2"}, {"duration": 33, "stepId": "io.quarkus.devservices.postgresql.deployment.PostgresqlDevServicesProcessor#setupPostgres", "started": "21:38:05.629", "dependents": [440], "id": 219, "thread": "build-61"}, {"duration": 32, "stepId": "io.quarkus.devui.deployment.menu.ExtensionsProcessor#createBuildTimeActions", "started": "21:38:05.654", "dependents": [541], "id": 247, "thread": "build-20"}, {"duration": 32, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setupDeployment", "started": "21:38:07.619", "dependents": [587, 584, 594, 562, 563, 561, 592, 564, 585, 560], "id": 559, "thread": "build-29"}, {"duration": 31, "stepId": "io.quarkus.deployment.steps.CapabilityAggregationStep#provideCapabilities", "started": "21:38:05.617", "dependents": [220], "id": 195, "thread": "build-63"}, {"duration": 30, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#build", "started": "21:38:05.902", "dependents": [493, 594, 492, 491], "id": 322, "thread": "build-54"}, {"duration": 30, "stepId": "io.quarkus.deployment.recording.substitutions.AdditionalSubstitutionsBuildStep#additionalSubstitutions", "started": "21:38:05.603", "dependents": [594], "id": 169, "thread": "build-30"}, {"duration": 30, "stepId": "io.quarkus.vertx.http.deployment.console.ConsoleProcessor#config", "started": "21:38:07.403", "dependents": [553], "id": 543, "thread": "build-91"}, {"duration": 30, "stepId": "io.quarkus.arc.deployment.ConfigStaticInitBuildSteps#registerBeans", "started": "21:38:05.626", "dependents": [466, 441], "id": 208, "thread": "build-57"}, {"duration": 30, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#devDbHandler", "started": "21:38:05.602", "dependents": [440], "id": 170, "thread": "build-55"}, {"duration": 28, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#configFiles", "started": "21:38:05.639", "dependents": [438], "id": 229, "thread": "build-27"}, {"duration": 28, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigRootsAsBeans", "started": "21:38:05.616", "dependents": [493, 492, 491], "id": 191, "thread": "build-62"}, {"duration": 28, "stepId": "io.quarkus.smallrye.openapi.deployment.devui.OpenApiDevUIProcessor#pages", "started": "21:38:05.633", "dependents": [542, 573], "id": 214, "thread": "build-30"}, {"duration": 28, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#autoAddScope", "started": "21:38:05.572", "dependents": [448], "id": 129, "thread": "build-20"}, {"duration": 28, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#registerAuthMechanismSelectionInterceptor", "started": "21:38:06.376", "dependents": [379, 594, 380, 458, 383, 476], "id": 377, "thread": "build-24"}, {"duration": 28, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#overrideStandardValidationFactoryResolution", "started": "21:38:05.549", "dependents": [572], "id": 99, "thread": "build-31"}, {"duration": 27, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#build_9d6b7122fb368970c50c3a870d1f672392cd8afb", "started": "21:38:05.553", "dependents": [592, 488], "id": 103, "thread": "build-33"}, {"duration": 27, "stepId": "io.quarkus.flyway.deployment.devui.FlywayDevUIProcessor#registerJsonRpcBackend", "started": "21:38:05.627", "dependents": [317, 544], "id": 203, "thread": "build-50"}, {"duration": 26, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#additionalBeans", "started": "21:38:05.555", "dependents": [466, 441], "id": 106, "thread": "build-15"}, {"duration": 26, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#mainClassBuildStep", "started": "21:38:06.376", "dependents": [572], "id": 376, "thread": "build-67"}, {"duration": 26, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initRuntime", "started": "21:38:06.959", "dependents": [594, 495, 588, 531, 589, 494, 530], "id": 493, "thread": "build-15"}, {"duration": 26, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#addAutoFilters", "started": "21:38:06.589", "dependents": [566], "id": 462, "thread": "build-17"}, {"duration": 26, "stepId": "io.quarkus.deployment.steps.ThreadPoolSetup#createExecutor", "started": "21:38:05.876", "dependents": [328, 587, 594, 315, 314, 322, 323], "id": 313, "thread": "build-75"}, {"duration": 25, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerAutoSecurityFilter", "started": "21:38:06.589", "dependents": [493, 594, 492, 491], "id": 461, "thread": "build-82"}, {"duration": 25, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanResources", "started": "21:38:06.384", "dependents": [394, 386, 552, 405, 559, 549, 466, 387, 388, 532, 390, 451, 398, 395, 396], "id": 385, "thread": "build-19"}, {"duration": 25, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#registerSecurityBeans", "started": "21:38:05.665", "dependents": [466, 441], "id": 251, "thread": "build-37"}, {"duration": 25, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#additionalBean", "started": "21:38:05.900", "dependents": [466, 441, 346], "id": 317, "thread": "build-82"}, {"duration": 25, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#defaultUnwrappedExceptions", "started": "21:38:05.627", "dependents": [408], "id": 200, "thread": "build-20"}, {"duration": 24, "stepId": "io.quarkus.deployment.dev.HotDeploymentWatchedFileBuildStep#setupWatchedFileHotDeployment", "started": "21:38:06.509", "dependents": [588, 589], "id": 438, "thread": "build-20"}, {"duration": 23, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#build", "started": "21:38:06.935", "dependents": [493, 594, 588, 589, 492, 491, 487, 488], "id": 486, "thread": "build-67"}, {"duration": 23, "stepId": "io.quarkus.security.deployment.SecurityProcessor#gatherSecurityChecks", "started": "21:38:06.584", "dependents": [466, 594, 460, 546, 592, 517, 549, 459], "id": 458, "thread": "build-8"}, {"duration": 23, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#autoAddScope", "started": "21:38:05.580", "dependents": [448], "id": 133, "thread": "build-47"}, {"duration": 23, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#gatherMvnpmJars", "started": "21:38:05.605", "dependents": [578, 583], "id": 166, "thread": "build-12"}, {"duration": 22, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#build", "started": "21:38:06.616", "dependents": [510, 466, 594, 592, 498, 522], "id": 465, "thread": "build-39"}, {"duration": 22, "stepId": "io.quarkus.deployment.pkg.steps.JarResultBuildStep#outputTarget", "started": "21:38:05.616", "dependents": [345, 213, 566, 229], "id": 178, "thread": "build-40"}, {"duration": 22, "stepId": "io.quarkus.deployment.pkg.steps.FileSystemResourcesBuildStep#notNormalMode", "started": "21:38:05.639", "dependents": [], "id": 213, "thread": "build-38"}, {"duration": 22, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#filterMultipleVertxInstancesWarning", "started": "21:38:05.541", "dependents": [419, 439], "id": 74, "thread": "build-4"}, {"duration": 22, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#pages", "started": "21:38:07.187", "dependents": [542, 573], "id": 519, "thread": "build-103"}, {"duration": 21, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#checkMixingStacks", "started": "21:38:05.664", "dependents": [588, 589], "id": 246, "thread": "build-55"}, {"duration": 21, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#collectInterceptedStaticMethods", "started": "21:38:06.926", "dependents": [510, 526, 498, 484], "id": 483, "thread": "build-23"}, {"duration": 21, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#additionalBeans", "started": "21:38:05.564", "dependents": [466, 441], "id": 111, "thread": "build-41"}, {"duration": 20, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#build", "started": "21:38:05.674", "dependents": [307, 466, 441, 592, 422, 292, 488], "id": 252, "thread": "build-52"}, {"duration": 20, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerContextPropagation", "started": "21:38:05.639", "dependents": [316], "id": 212, "thread": "build-32"}, {"duration": 19, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#gatherAuthorizationPolicyInstances", "started": "21:38:06.376", "dependents": [453, 375], "id": 373, "thread": "build-33"}, {"duration": 19, "stepId": "io.quarkus.deployment.steps.DevModeBuildStep#watchChanges", "started": "21:38:05.602", "dependents": [438], "id": 161, "thread": "build-20"}, {"duration": 19, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#config", "started": "21:38:05.644", "dependents": [546], "id": 216, "thread": "build-64"}, {"duration": 19, "stepId": "io.quarkus.devui.deployment.menu.ReadmeProcessor#createReadmePage", "started": "21:38:05.649", "dependents": [576], "id": 231, "thread": "build-76"}, {"duration": 19, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#transformResourceMethods", "started": "21:38:06.376", "dependents": [466], "id": 374, "thread": "build-26"}, {"duration": 19, "stepId": "io.quarkus.arc.deployment.CommandLineArgumentsProcessor#commandLineArgs", "started": "21:38:05.662", "dependents": [466, 493, 441, 492, 491], "id": 244, "thread": "build-30"}, {"duration": 18, "stepId": "io.quarkus.vertx.http.deployment.devmode.ArcDevProcessor#registerRoutes", "started": "21:38:07.103", "dependents": [584, 594, 586, 585, 520], "id": 516, "thread": "build-78"}, {"duration": 18, "stepId": "io.quarkus.devui.deployment.menu.DependenciesProcessor#createAppDeps", "started": "21:38:05.605", "dependents": [576], "id": 163, "thread": "build-21"}, {"duration": 18, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#registerBean", "started": "21:38:05.649", "dependents": [466, 441], "id": 228, "thread": "build-71"}, {"duration": 18, "stepId": "io.quarkus.deployment.ExtensionLoader#config", "started": "21:38:05.566", "dependents": [134, 112, 115, 217, 432, 113, 502, 564, 367, 121, 587, 342, 475, 114, 149, 440, 458, 452, 407, 377, 335, 517, 582, 336, 215, 337, 473, 250, 306, 319, 299, 462, 297, 531, 436, 589, 309, 425, 376, 520, 583, 476, 591, 252, 548, 313, 341, 345, 326, 562, 434, 298, 439, 446, 150, 289, 344, 441, 561, 271, 485, 321, 305, 144, 294, 124, 328, 466, 507, 139, 450, 154, 489, 422, 161, 461, 130, 131, 147, 295, 140, 278, 447, 433, 490, 141, 465, 566, 329, 324, 486, 528, 482, 227, 143, 159, 142, 291, 155, 275, 580, 516, 152, 334, 269, 572, 274, 236, 157, 459, 292, 426, 457, 594, 380, 533, 381, 529, 494, 178, 536, 191, 307, 588, 460, 576, 453, 318, 590, 519, 320, 417, 301, 559, 173, 585, 331, 172, 293, 521, 524, 444, 219, 184, 515, 470, 229, 339, 525, 510, 368, 180, 221, 424, 256, 302, 188, 449, 214, 549, 189, 508, 194, 518, 258, 431, 546, 290, 179, 240, 212, 190, 268], "id": 107, "thread": "build-38"}, {"duration": 18, "stepId": "io.quarkus.devservices.deployment.DevServicesProcessor#config", "started": "21:38:07.403", "dependents": [540, 539, 553], "id": 538, "thread": "build-87"}, {"duration": 17, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#registerVerticleClasses", "started": "21:38:06.377", "dependents": [592], "id": 371, "thread": "build-63"}, {"duration": 17, "stepId": "io.quarkus.deployment.steps.CombinedIndexBuildStep#build", "started": "21:38:06.357", "dependents": [568, 369, 347, 464, 402, 384, 474, 351, 407, 377, 569, 400, 359, 363, 389, 426, 373, 391, 348, 381, 436, 455, 364, 454, 427, 425, 365, 376, 392, 361, 374, 349, 439, 453, 354, 414, 417, 382, 552, 371, 360, 357, 408, 503, 404, 413, 466, 356, 444, 544, 570, 504, 463, 469, 379, 368, 350, 358, 405, 424, 393, 403, 418, 370, 554, 352, 546, 401, 353, 465, 372, 362, 355], "id": 346, "thread": "build-66"}, {"duration": 17, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#handler", "started": "21:38:06.374", "dependents": [594, 439], "id": 367, "thread": "build-46"}, {"duration": 17, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#register", "started": "21:38:06.375", "dependents": [554, 466, 441, 592], "id": 369, "thread": "build-36"}, {"duration": 17, "stepId": "io.quarkus.security.deployment.SecurityProcessor#validateStartUpObserversNotSecured", "started": "21:38:07.103", "dependents": [520], "id": 515, "thread": "build-46"}, {"duration": 17, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#smallryeOpenApiIndex", "started": "21:38:06.571", "dependents": [457, 462, 461, 566, 456], "id": 455, "thread": "build-15"}, {"duration": 17, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#watchConfigFiles", "started": "21:38:05.624", "dependents": [438], "id": 185, "thread": "build-64"}, {"duration": 17, "stepId": "io.quarkus.resteasy.reactive.server.deployment.devui.ResteasyReactiveDevUIProcessor#createJsonRPCService", "started": "21:38:05.516", "dependents": [317, 544], "id": 23, "thread": "build-5"}, {"duration": 16, "stepId": "io.quarkus.devui.deployment.menu.DependenciesProcessor#createBuildTimeActions", "started": "21:38:05.613", "dependents": [541], "id": 167, "thread": "build-61"}, {"duration": 16, "stepId": "io.quarkus.arc.deployment.HotDeploymentConfigBuildStep#startup", "started": "21:38:05.608", "dependents": [165], "id": 164, "thread": "build-32"}, {"duration": 16, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#sharedStateListener", "started": "21:38:05.538", "dependents": [334], "id": 67, "thread": "build-28"}, {"duration": 16, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupStackTraceFormatter", "started": "21:38:06.357", "dependents": [587, 367, 439], "id": 345, "thread": "build-48"}, {"duration": 16, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#httpRoot", "started": "21:38:05.593", "dependents": [587, 262, 551, 586, 566, 583, 565], "id": 149, "thread": "build-52"}, {"duration": 16, "stepId": "io.quarkus.devui.deployment.menu.EndpointsProcessor#createJsonRPCService", "started": "21:38:05.534", "dependents": [317, 544], "id": 55, "thread": "build-6"}, {"duration": 16, "stepId": "io.quarkus.vertx.deployment.EventBusCodecProcessor#registerCodecs", "started": "21:38:06.572", "dependents": [592, 481], "id": 454, "thread": "build-39"}, {"duration": 15, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#resolveRolesAllowedConfigExpressions", "started": "21:38:06.380", "dependents": [493, 594, 499, 458, 492, 491], "id": 372, "thread": "build-78"}, {"duration": 15, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#compressionSupport", "started": "21:38:05.605", "dependents": [549], "id": 159, "thread": "build-10"}, {"duration": 15, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#registerVerticleClasses", "started": "21:38:06.379", "dependents": [592], "id": 370, "thread": "build-8"}, {"duration": 15, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#handleApplication", "started": "21:38:06.391", "dependents": [391, 382, 393, 559, 384, 408, 392, 549, 558, 532, 406, 401, 592, 389], "id": 381, "thread": "build-54"}, {"duration": 15, "stepId": "io.quarkus.deployment.steps.ReflectiveHierarchyStep#ignoreJavaClassWarnings", "started": "21:38:05.607", "dependents": [554], "id": 162, "thread": "build-57"}, {"duration": 15, "stepId": "io.quarkus.netty.deployment.NettyProcessor#build", "started": "21:38:05.649", "dependents": [592, 488], "id": 221, "thread": "build-70"}, {"duration": 15, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#build_7a4403d699506d83ac39616f3c11e5e1b448d863", "started": "21:38:06.509", "dependents": [594, 522], "id": 437, "thread": "build-33"}, {"duration": 15, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#unremovableBean", "started": "21:38:05.535", "dependents": [510, 498], "id": 57, "thread": "build-23"}, {"duration": 15, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#frameworkRoot", "started": "21:38:05.594", "dependents": [564, 214, 585, 153, 583, 548, 587, 262, 578, 551, 576, 516, 340, 577, 240, 582, 565], "id": 150, "thread": "build-51"}, {"duration": 14, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#vetoMPConfigProperties", "started": "21:38:05.558", "dependents": [466], "id": 91, "thread": "build-34"}, {"duration": 14, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#build", "started": "21:38:06.932", "dependents": [493, 594, 592, 492, 491], "id": 482, "thread": "build-8"}, {"duration": 14, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#supportMixins", "started": "21:38:06.376", "dependents": [493, 594, 592, 492, 491], "id": 363, "thread": "build-54"}, {"duration": 14, "stepId": "io.quarkus.vertx.http.deployment.GeneratedStaticResourcesProcessor#produceResources", "started": "21:38:05.538", "dependents": [255], "id": 61, "thread": "build-27"}, {"duration": 14, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#buildReactivePersistenceUnit", "started": "21:38:06.494", "dependents": [429, 438, 594, 533, 436, 432, 428, 435, 430, 427, 593, 437, 431, 487], "id": 426, "thread": "build-6"}, {"duration": 14, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#provideSecurityInformation", "started": "21:38:05.590", "dependents": [462, 461], "id": 134, "thread": "build-50"}, {"duration": 14, "stepId": "io.quarkus.hibernate.orm.deployment.GraalVMFeatures#registerStandardStackElementTypesForReflection", "started": "21:38:05.649", "dependents": [592], "id": 218, "thread": "build-62"}, {"duration": 14, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForExceptionMappers", "started": "21:38:06.444", "dependents": [466, 441, 559, 592], "id": 408, "thread": "build-23"}, {"duration": 14, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#unremovableBeans", "started": "21:38:05.654", "dependents": [510, 498], "id": 232, "thread": "build-75"}, {"duration": 13, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#buildExclusions", "started": "21:38:06.391", "dependents": [455], "id": 378, "thread": "build-52"}, {"duration": 13, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#unremovableBeans", "started": "21:38:05.529", "dependents": [510, 498], "id": 49, "thread": "build-9"}, {"duration": 13, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#findAllJsonRPCMethods", "started": "21:38:07.425", "dependents": [577, 545], "id": 544, "thread": "build-46"}, {"duration": 13, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#feature", "started": "21:38:05.622", "dependents": [594], "id": 173, "thread": "build-10"}, {"duration": 13, "stepId": "io.quarkus.tls.CertificatesProcessor#initializeCertificate", "started": "21:38:06.935", "dependents": [587, 493, 594, 492, 491], "id": 485, "thread": "build-15"}, {"duration": 13, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#jacksonSupport", "started": "21:38:06.377", "dependents": [493, 594, 492, 491], "id": 368, "thread": "build-48"}, {"duration": 13, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#devDbHandler", "started": "21:38:05.537", "dependents": [440], "id": 58, "thread": "build-5"}, {"duration": 12, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initRegular", "started": "21:38:06.959", "dependents": [495], "id": 492, "thread": "build-8"}, {"duration": 12, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#configureAgroalConnection", "started": "21:38:05.663", "dependents": [466, 441], "id": 241, "thread": "build-62"}, {"duration": 12, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#validateInterceptedMethods", "started": "21:38:07.103", "dependents": [520], "id": 511, "thread": "build-8"}, {"duration": 12, "stepId": "io.quarkus.devui.deployment.ide.IdeProcessor#createOpenInIDEService", "started": "21:38:06.031", "dependents": [584, 594, 585, 541], "id": 340, "thread": "build-46"}, {"duration": 12, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#transformEndpoints", "started": "21:38:06.572", "dependents": [466], "id": 451, "thread": "build-8"}, {"duration": 12, "stepId": "io.quarkus.deployment.steps.ReflectiveHierarchyStep#build", "started": "21:38:07.566", "dependents": [592], "id": 554, "thread": "build-93"}, {"duration": 12, "stepId": "io.quarkus.hibernate.reactive.deployment.ResteasyReactiveServerIntegrationProcessor#unwrappedExceptions", "started": "21:38:05.640", "dependents": [408], "id": 201, "thread": "build-72"}, {"duration": 12, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#silenceLogging", "started": "21:38:05.515", "dependents": [239], "id": 10, "thread": "build-2"}, {"duration": 12, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#mutinyReturnTypes", "started": "21:38:05.620", "dependents": [568, 570], "id": 168, "thread": "build-65"}, {"duration": 11, "stepId": "io.quarkus.hibernate.orm.deployment.GraalVMFeatures#registerGeneratorClassesForReflections", "started": "21:38:05.597", "dependents": [592], "id": 148, "thread": "build-53"}, {"duration": 11, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#unremoveableSkipPredicates", "started": "21:38:05.583", "dependents": [510, 498], "id": 122, "thread": "build-48"}, {"duration": 11, "stepId": "io.quarkus.security.deployment.SecurityProcessor#prepareBouncyCastleProviders", "started": "21:38:05.633", "dependents": [592], "id": 189, "thread": "build-65"}, {"duration": 11, "stepId": "io.quarkus.vertx.deployment.EventConsumerMethodsProcessor#eventConsumerMethods", "started": "21:38:05.597", "dependents": [444], "id": 146, "thread": "build-49"}, {"duration": 11, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#ifBuildProperty", "started": "21:38:06.380", "dependents": [366, 381, 378], "id": 365, "thread": "build-72"}, {"duration": 11, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#addAdditionalRoutes", "started": "21:38:07.112", "dependents": [587, 554, 584, 594, 586, 592, 564, 585], "id": 517, "thread": "build-39"}, {"duration": 11, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#createJsonRpcRouter", "started": "21:38:07.438", "dependents": [594], "id": 545, "thread": "build-91"}, {"duration": 11, "stepId": "io.quarkus.security.deployment.SecurityProcessor#transformPermissionsAllowedMetaAnnotations", "started": "21:38:06.572", "dependents": [466, 450, 452, 453], "id": 449, "thread": "build-67"}, {"duration": 11, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#createJsonRPCService", "started": "21:38:05.522", "dependents": [317, 544], "id": 24, "thread": "build-13"}, {"duration": 11, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#reflection", "started": "21:38:05.521", "dependents": [592], "id": 19, "thread": "build-10"}, {"duration": 10, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#unlessBuildProperty", "started": "21:38:06.380", "dependents": [366, 381, 378], "id": 364, "thread": "build-53"}, {"duration": 10, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerAnnotatedUserDefinedRuntimeFilters", "started": "21:38:06.589", "dependents": [493, 594, 592, 492, 491], "id": 457, "thread": "build-39"}, {"duration": 10, "stepId": "io.quarkus.devui.deployment.menu.ReportIssuesProcessor#registerJsonRpcService", "started": "21:38:05.662", "dependents": [317, 544], "id": 235, "thread": "build-52"}, {"duration": 10, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateConfigMappingsInjectionPoints", "started": "21:38:07.102", "dependents": [514, 546], "id": 510, "thread": "build-6"}, {"duration": 10, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setMinLevelForInitialConfigurator", "started": "21:38:05.633", "dependents": [594], "id": 188, "thread": "build-55"}, {"duration": 10, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#additionalProviders", "started": "21:38:07.570", "dependents": [556, 557, 558], "id": 555, "thread": "build-55"}, {"duration": 10, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#buildResourceInterceptors", "started": "21:38:06.444", "dependents": [466, 441, 559, 451, 557, 549], "id": 406, "thread": "build-6"}, {"duration": 10, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#responseHeaderSupport", "started": "21:38:05.551", "dependents": [549], "id": 72, "thread": "build-9"}, {"duration": 10, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#reinitializeClassesForNetty", "started": "21:38:05.571", "dependents": [488], "id": 104, "thread": "build-43"}, {"duration": 10, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#metrics", "started": "21:38:05.629", "dependents": [466], "id": 184, "thread": "build-12"}, {"duration": 10, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#registerCustomExceptionMappers", "started": "21:38:05.575", "dependents": [405], "id": 108, "thread": "build-45"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerSyntheticObservers", "started": "21:38:06.986", "dependents": [510, 497, 592, 498, 496, 520], "id": 495, "thread": "build-8"}, {"duration": 9, "stepId": "io.quarkus.deployment.dev.IsolatedDevModeMain$AddApplicationClassPredicateBuildStep$1@16ac0be4", "started": "21:38:05.559", "dependents": [466, 517, 549], "id": 85, "thread": "build-39"}, {"duration": 9, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#validateScheduledBusinessMethods", "started": "21:38:07.103", "dependents": [520], "id": 508, "thread": "build-20"}, {"duration": 9, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#registerServiceBinding", "started": "21:38:05.663", "dependents": [426, 486, 440, 252], "id": 233, "thread": "build-32"}, {"duration": 9, "stepId": "io.quarkus.devui.deployment.menu.ReadmeProcessor#createJsonRPCServiceForCache", "started": "21:38:05.533", "dependents": [317, 544], "id": 47, "thread": "build-15"}, {"duration": 9, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#indexAdditionalConstrainedClasses", "started": "21:38:06.457", "dependents": [463, 465], "id": 409, "thread": "build-6"}, {"duration": 9, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#transformInjectionPoint", "started": "21:38:05.545", "dependents": [466], "id": 65, "thread": "build-15"}, {"duration": 9, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#registerReflectivelyAccessedMethods", "started": "21:38:05.626", "dependents": [592], "id": 176, "thread": "build-32"}, {"duration": 9, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#finalizeRouter", "started": "21:38:10.391", "dependents": [594, 588, 589, 590], "id": 587, "thread": "build-110"}, {"duration": 9, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForContextResolvers", "started": "21:38:06.407", "dependents": [466, 441, 559, 592, 555], "id": 401, "thread": "build-63"}, {"duration": 9, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#registerServiceBinding", "started": "21:38:05.664", "dependents": [426, 486, 440, 252], "id": 234, "thread": "build-57"}, {"duration": 9, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#validatePersistenceUnitExtensions", "started": "21:38:07.103", "dependents": [520], "id": 509, "thread": "build-33"}, {"duration": 9, "stepId": "io.quarkus.vertx.http.deployment.devmode.NotFoundProcessor#routeNotFound", "started": "21:38:10.391", "dependents": [594], "id": 586, "thread": "build-35"}, {"duration": 8, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#deprioritizeLegacyProviders", "started": "21:38:05.666", "dependents": [558], "id": 238, "thread": "build-81"}, {"duration": 8, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#generateDataSourceBeans", "started": "21:38:06.509", "dependents": [466, 493, 594, 441, 492, 491], "id": 435, "thread": "build-63"}, {"duration": 8, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#registerBeans", "started": "21:38:06.509", "dependents": [510, 466, 441, 498], "id": 436, "thread": "build-17"}, {"duration": 8, "stepId": "io.quarkus.scheduler.deployment.SchedulerMethodsProcessor#schedulerMethods", "started": "21:38:05.571", "dependents": [444], "id": 102, "thread": "build-44"}, {"duration": 8, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#validateBeanDeployment", "started": "21:38:07.103", "dependents": [520, 517], "id": 507, "thread": "build-17"}, {"duration": 8, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesMethodsProcessor#reactiveRoutesMethods", "started": "21:38:05.566", "dependents": [444], "id": 95, "thread": "build-2"}, {"duration": 8, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#unlessBuildProfile", "started": "21:38:06.380", "dependents": [366, 381, 378], "id": 362, "thread": "build-52"}, {"duration": 8, "stepId": "io.quarkus.arc.deployment.AutoAddScopeProcessor#annotationTransformer", "started": "21:38:06.572", "dependents": [510, 466, 498], "id": 448, "thread": "build-54"}, {"duration": 8, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#unremovableBeans", "started": "21:38:05.560", "dependents": [510, 498], "id": 84, "thread": "build-28"}, {"duration": 8, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#cacheControlSupport", "started": "21:38:05.524", "dependents": [549], "id": 20, "thread": "build-6"}, {"duration": 8, "stepId": "io.quarkus.deployment.steps.BlockingOperationControlBuildStep#blockingOP", "started": "21:38:05.876", "dependents": [594], "id": 308, "thread": "build-54"}, {"duration": 8, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#additionalBean", "started": "21:38:05.568", "dependents": [466, 441], "id": 98, "thread": "build-19"}, {"duration": 8, "stepId": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsProcessor#devuiJsonRpcServices", "started": "21:38:05.582", "dependents": [444], "id": 116, "thread": "build-43"}, {"duration": 8, "stepId": "io.quarkus.deployment.steps.RegisterForReflectionBuildStep#build", "started": "21:38:06.376", "dependents": [554, 592], "id": 359, "thread": "build-37"}, {"duration": 8, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#ifBuildProfile", "started": "21:38:06.380", "dependents": [366, 381, 378], "id": 361, "thread": "build-59"}, {"duration": 8, "stepId": "io.quarkus.hibernate.orm.deployment.metrics.HibernateOrmMetricsProcessor#metrics", "started": "21:38:07.397", "dependents": [594], "id": 536, "thread": "build-72"}, {"duration": 8, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveLogFilter#setupLogFilters", "started": "21:38:05.525", "dependents": [419, 439], "id": 26, "thread": "build-4"}, {"duration": 7, "stepId": "io.quarkus.deployment.SecureRandomProcessor#registerReflectiveMethods", "started": "21:38:05.541", "dependents": [592], "id": 54, "thread": "build-21"}, {"duration": 7, "stepId": "io.quarkus.netty.deployment.NettyProcessor#registerQualifiers", "started": "21:38:05.563", "dependents": [466, 441], "id": 88, "thread": "build-4"}, {"duration": 7, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#ensureBeanLookupAvailable", "started": "21:38:05.524", "dependents": [510, 498], "id": 18, "thread": "build-11"}, {"duration": 7, "stepId": "io.quarkus.hibernate.orm.deployment.GraalVMFeatures#registerJdbcArrayTypesForReflection", "started": "21:38:05.578", "dependents": [592], "id": 110, "thread": "build-19"}, {"duration": 7, "stepId": "io.quarkus.credentials.CredentialsProcessor#unremoveable", "started": "21:38:05.588", "dependents": [510, 498], "id": 126, "thread": "build-49"}, {"duration": 7, "stepId": "io.quarkus.vertx.http.deployment.ManagementInterfaceSecurityProcessor#setupAuthenticationMechanisms", "started": "21:38:05.863", "dependents": [587, 466, 594, 441], "id": 296, "thread": "build-43"}, {"duration": 7, "stepId": "io.quarkus.security.deployment.SecurityProcessor#supportBlockingExecutionOfPermissionChecks", "started": "21:38:05.606", "dependents": [444], "id": 154, "thread": "build-58"}, {"duration": 7, "stepId": "io.quarkus.mutiny.deployment.MutinyProcessor#runtimeInit", "started": "21:38:05.903", "dependents": [594], "id": 315, "thread": "build-17"}, {"duration": 7, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#responseStatusSupport", "started": "21:38:05.547", "dependents": [549], "id": 66, "thread": "build-19"}, {"duration": 7, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#securityContextOverrideHandler", "started": "21:38:05.533", "dependents": [559], "id": 45, "thread": "build-11"}, {"duration": 7, "stepId": "io.quarkus.netty.deployment.NettyProcessor#cleanupUnsafeLog", "started": "21:38:05.649", "dependents": [419, 439], "id": 206, "thread": "build-55"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#generateConfigProperties", "started": "21:38:06.377", "dependents": [510, 474, 464, 502, 592, 409, 465, 469], "id": 360, "thread": "build-75"}, {"duration": 7, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#startTesting", "started": "21:38:06.022", "dependents": [588, 589, 439], "id": 334, "thread": "build-47"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateRuntimeConfigProperty", "started": "21:38:07.109", "dependents": [594, 592], "id": 513, "thread": "build-63"}, {"duration": 7, "stepId": "io.quarkus.panache.hibernate.common.deployment.PanacheHibernateCommonResourceProcessor#findEntityClasses", "started": "21:38:06.495", "dependents": [420, 569], "id": 418, "thread": "build-82"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initStatic", "started": "21:38:06.959", "dependents": [594, 495], "id": 491, "thread": "build-6"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.UnremovableAnnotationsProcessor#unremovableBeans", "started": "21:38:05.555", "dependents": [510, 498], "id": 76, "thread": "build-19"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.TestsAsBeansProcessor#testAnnotations", "started": "21:38:05.558", "dependents": [466, 441, 268], "id": 78, "thread": "build-38"}, {"duration": 7, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#validateBeans", "started": "21:38:07.103", "dependents": [520], "id": 506, "thread": "build-39"}, {"duration": 7, "stepId": "io.quarkus.datasource.deployment.devui.DevUIDatasourceProcessor#registerJsonRpcBackend", "started": "21:38:05.635", "dependents": [317, 544], "id": 186, "thread": "build-10"}, {"duration": 7, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#createJsonRPCService", "started": "21:38:05.547", "dependents": [317, 544], "id": 68, "thread": "build-34"}, {"duration": 7, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#mapPageBuildTimeData", "started": "21:38:07.424", "dependents": [577], "id": 542, "thread": "build-72"}, {"duration": 7, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#registerRowSetSupport", "started": "21:38:05.558", "dependents": [592], "id": 79, "thread": "build-36"}, {"duration": 6, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#setupExceptionHandler", "started": "21:38:06.031", "dependents": [345], "id": 338, "thread": "build-36"}, {"duration": 6, "stepId": "io.quarkus.devui.deployment.menu.EndpointsProcessor#createEndpointsPage", "started": "21:38:05.670", "dependents": [576], "id": 240, "thread": "build-76"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.SplitPackageProcessor#splitPackageDetection", "started": "21:38:06.357", "dependents": [520], "id": 344, "thread": "build-46"}, {"duration": 6, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#asyncSupport", "started": "21:38:05.526", "dependents": [549], "id": 21, "thread": "build-19"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.AutoProducerMethodsProcessor#annotationTransformer", "started": "21:38:06.572", "dependents": [466], "id": 447, "thread": "build-82"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateStaticInitConfigProperty", "started": "21:38:07.109", "dependents": [594, 592], "id": 512, "thread": "build-24"}, {"duration": 6, "stepId": "io.quarkus.netty.deployment.NettyProcessor#registerEventLoopBeans", "started": "21:38:05.980", "dependents": [493, 594, 492, 491], "id": 330, "thread": "build-47"}, {"duration": 6, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#handler", "started": "21:38:07.657", "dependents": [594, 586, 565], "id": 564, "thread": "build-93"}, {"duration": 6, "stepId": "io.quarkus.security.deployment.SecurityProcessor#makePermissionCheckerClassBeansUnremovable", "started": "21:38:05.588", "dependents": [510, 498], "id": 121, "thread": "build-45"}, {"duration": 6, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#registerSwaggerUiHandler", "started": "21:38:10.127", "dependents": [584, 594, 585], "id": 582, "thread": "build-110"}, {"duration": 6, "stepId": "io.quarkus.security.deployment.SecurityProcessor#authorizationController", "started": "21:38:05.639", "dependents": [466, 441], "id": 190, "thread": "build-71"}, {"duration": 6, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#additionalBean", "started": "21:38:05.578", "dependents": [466, 441], "id": 109, "thread": "build-34"}, {"duration": 5, "stepId": "io.quarkus.arc.deployment.ArcProcessor#loggerProducer", "started": "21:38:05.519", "dependents": [466, 441], "id": 8, "thread": "build-8"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForParamConverters_dcdfdd2a310a09abe5ee3f0ed2b2bc49f36f3d07", "started": "21:38:06.407", "dependents": [466, 441, 559, 592, 549], "id": 397, "thread": "build-54"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#setupEndpoints", "started": "21:38:07.392", "dependents": [592, 556, 557, 558], "id": 532, "thread": "build-163"}, {"duration": 5, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#build", "started": "21:38:07.754", "dependents": [571, 572], "id": 570, "thread": "build-29"}, {"duration": 5, "stepId": "io.quarkus.flyway.deployment.FlywayProcessor#build", "started": "21:38:06.503", "dependents": [594, 438, 433, 592, 529], "id": 425, "thread": "build-82"}, {"duration": 5, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#overrideContextInternalInterfaceToAddSafeGuards", "started": "21:38:05.589", "dependents": [572], "id": 120, "thread": "build-41"}, {"duration": 5, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#yamlConfig", "started": "21:38:05.597", "dependents": [546], "id": 132, "thread": "build-21"}, {"duration": 5, "stepId": "io.quarkus.jdbc.postgresql.deployment.PostgreSQLJDBCReflections#build", "started": "21:38:05.516", "dependents": [592], "id": 5, "thread": "build-4"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveVertxWebSocketIntegrationProcessor#scanner", "started": "21:38:05.524", "dependents": [549], "id": 15, "thread": "build-9"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#resourceIndex", "started": "21:38:06.377", "dependents": [385, 441, 550], "id": 358, "thread": "build-47"}, {"duration": 5, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#collectEventConsumers", "started": "21:38:06.926", "dependents": [495, 481], "id": 478, "thread": "build-8"}, {"duration": 5, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#registerSafeDuplicatedContextInterceptor", "started": "21:38:05.568", "dependents": [466, 441], "id": 94, "thread": "build-36"}, {"duration": 5, "stepId": "io.quarkus.datasource.deployment.devservices.DevServicesDatasourceProcessor#launchDatabases", "started": "21:38:06.547", "dependents": [538, 534, 539], "id": 440, "thread": "build-20"}, {"duration": 5, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#doNotRemoveVertxOptionsCustomizers", "started": "21:38:05.528", "dependents": [510, 498], "id": 25, "thread": "build-15"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#customExceptionMappers", "started": "21:38:05.545", "dependents": [405], "id": 56, "thread": "build-9"}, {"duration": 5, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#collectScheduledMethods_84ea631eea52cbbcaee3e56019e68e7826861add", "started": "21:38:06.927", "dependents": [482, 480, 508], "id": 479, "thread": "build-15"}, {"duration": 5, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#configPropertyInjectionPoints", "started": "21:38:07.103", "dependents": [513, 592, 512], "id": 505, "thread": "build-54"}, {"duration": 5, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#runtimeOverrideConfig", "started": "21:38:05.534", "dependents": [546], "id": 44, "thread": "build-19"}, {"duration": 4, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerOpenApiSchemaClassesForReflection", "started": "21:38:06.589", "dependents": [554, 592], "id": 456, "thread": "build-54"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#runtimeConfiguration", "started": "21:38:07.652", "dependents": [594, 562], "id": 561, "thread": "build-152"}, {"duration": 4, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#registerBean", "started": "21:38:05.549", "dependents": [466, 441], "id": 63, "thread": "build-33"}, {"duration": 4, "stepId": "io.quarkus.flyway.deployment.FlywayProcessor#startActions", "started": "21:38:07.392", "dependents": [594, 588, 531, 533, 589, 536, 530], "id": 529, "thread": "build-87"}, {"duration": 4, "stepId": "io.quarkus.security.deployment.SecurityProcessor#createSecurityCheckStorage", "started": "21:38:06.609", "dependents": [466, 493, 594, 492, 491, 517, 549], "id": 460, "thread": "build-39"}, {"duration": 4, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDarkeningDefault", "started": "21:38:05.635", "dependents": [546], "id": 181, "thread": "build-70"}, {"duration": 4, "stepId": "io.quarkus.security.jpa.deployment.QuarkusSecurityJpaProcessor#configureJpaAuthConfig", "started": "21:38:06.509", "dependents": [441], "id": 431, "thread": "build-54"}, {"duration": 4, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#watchYamlConfig", "started": "21:38:05.525", "dependents": [438], "id": 13, "thread": "build-8"}, {"duration": 4, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#featureBuildItem", "started": "21:38:05.515", "dependents": [594], "id": 3, "thread": "build-3"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForDynamicFeatures", "started": "21:38:06.407", "dependents": [399, 559], "id": 391, "thread": "build-78"}, {"duration": 4, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#generateAuthorizationPolicyStorage", "started": "21:38:06.396", "dependents": [441, 458], "id": 375, "thread": "build-78"}, {"duration": 4, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#brandingFiles", "started": "21:38:05.555", "dependents": [438], "id": 71, "thread": "build-28"}, {"duration": 4, "stepId": "io.quarkus.devui.deployment.menu.ReportIssuesProcessor#createReportIssuePage", "started": "21:38:05.523", "dependents": [576], "id": 11, "thread": "build-15"}, {"duration": 4, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#detectBasicAuthImplicitlyRequired", "started": "21:38:06.927", "dependents": [594], "id": 476, "thread": "build-67"}, {"duration": 4, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#registerOptionalClaimProducer", "started": "21:38:06.926", "dependents": [495], "id": 473, "thread": "build-39"}, {"duration": 4, "stepId": "io.quarkus.vertx.http.deployment.StaticResourcesProcessor#runtimeInit", "started": "21:38:07.392", "dependents": [587, 594], "id": 528, "thread": "build-46"}, {"duration": 4, "stepId": "io.quarkus.devui.deployment.welcome.WelcomeProcessor#createWelcomePages", "started": "21:38:09.265", "dependents": [576], "id": 575, "thread": "build-82"}, {"duration": 4, "stepId": "io.quarkus.devservices.postgresql.deployment.PostgresqlDevServicesProcessor#psqlCommand", "started": "21:38:07.403", "dependents": [553], "id": 537, "thread": "build-46"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigClasses", "started": "21:38:07.113", "dependents": [594], "id": 514, "thread": "build-33"}, {"duration": 4, "stepId": "io.quarkus.flyway.deployment.devui.FlywayDevUIProcessor#create", "started": "21:38:06.512", "dependents": [594, 542, 573], "id": 434, "thread": "build-15"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.ReflectiveBeanClassesProcessor#implicitReflectiveBeanClasses", "started": "21:38:06.927", "dependents": [520], "id": 477, "thread": "build-6"}, {"duration": 4, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#build", "started": "21:38:07.754", "dependents": [572], "id": 568, "thread": "build-82"}, {"duration": 4, "stepId": "io.quarkus.deployment.steps.NativeImageConfigBuildStep#build", "started": "21:38:06.960", "dependents": [594], "id": 488, "thread": "build-63"}, {"duration": 4, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#build_d182d2fe7ae008890806ec353e99fa052582ee2d", "started": "21:38:06.511", "dependents": [594, 570], "id": 432, "thread": "build-67"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#providersFromClasspath", "started": "21:38:05.518", "dependents": [556, 557, 558], "id": 6, "thread": "build-7"}, {"duration": 4, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#createJsonRPCService", "started": "21:38:05.532", "dependents": [317, 544], "id": 37, "thread": "build-10"}, {"duration": 4, "stepId": "io.quarkus.flyway.deployment.FlywayProcessor#createBeans", "started": "21:38:06.512", "dependents": [466, 493, 594, 531, 441, 492, 494, 491, 530], "id": 433, "thread": "build-39"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.AutoInjectFieldProcessor#annotationTransformer", "started": "21:38:06.571", "dependents": [466], "id": 446, "thread": "build-17"}, {"duration": 4, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#jpaEntitiesIndexer", "started": "21:38:06.377", "dependents": [593, 410], "id": 357, "thread": "build-19"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#registerMonitoringComponents", "started": "21:38:05.787", "dependents": [466, 441], "id": 268, "thread": "build-35"}, {"duration": 4, "stepId": "io.quarkus.panache.hibernate.common.deployment.PanacheHibernateCommonResourceProcessor#replaceFieldAccesses", "started": "21:38:07.754", "dependents": [572], "id": 569, "thread": "build-93"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDevModeProcessor#openCommand", "started": "21:38:07.566", "dependents": [553], "id": 551, "thread": "build-55"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#handleJsonAnnotations", "started": "21:38:07.566", "dependents": [594, 592, 555], "id": 552, "thread": "build-152"}, {"duration": 3, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#produceEagerSecurityInterceptorStorage", "started": "21:38:06.405", "dependents": [493, 594, 492, 491], "id": 383, "thread": "build-52"}, {"duration": 3, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#transactionContext", "started": "21:38:06.674", "dependents": [468], "id": 467, "thread": "build-39"}, {"duration": 3, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#enableSslInNative", "started": "21:38:05.586", "dependents": [488], "id": 115, "thread": "build-19"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForFeatures", "started": "21:38:06.407", "dependents": [399, 559], "id": 392, "thread": "build-33"}, {"duration": 3, "stepId": "io.quarkus.deployment.logging.LoggingWithPanacheProcessor#process", "started": "21:38:06.377", "dependents": [572], "id": 356, "thread": "build-82"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#generateCustomProducer", "started": "21:38:06.410", "dependents": [466, 441], "id": 398, "thread": "build-8"}, {"duration": 3, "stepId": "io.quarkus.deployment.steps.DevServicesConfigBuildStep#setup", "started": "21:38:07.399", "dependents": [548, 537, 588, 538, 546, 589, 547, 543, 535], "id": 534, "thread": "build-163"}, {"duration": 3, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#continuousTestingState", "started": "21:38:07.392", "dependents": [594], "id": 527, "thread": "build-93"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.LookupConditionsProcessor#suppressConditionsGenerators", "started": "21:38:06.571", "dependents": [466], "id": 445, "thread": "build-33"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForInterceptors", "started": "21:38:06.407", "dependents": [406], "id": 389, "thread": "build-8"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#validate", "started": "21:38:07.103", "dependents": [520], "id": 503, "thread": "build-63"}, {"duration": 3, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#startRecoveryService", "started": "21:38:06.986", "dependents": [594], "id": 494, "thread": "build-6"}, {"duration": 3, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#registerHttpAuthMechanismAnnotations", "started": "21:38:05.533", "dependents": [377], "id": 38, "thread": "build-21"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ObservabilityProcessor#preAuthFailureFilter", "started": "21:38:07.652", "dependents": [587, 594, 563, 564], "id": 560, "thread": "build-93"}, {"duration": 3, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#createSynthBeansForConfiguredInjectionPoints", "started": "21:38:06.926", "dependents": [493, 594, 492, 491], "id": 471, "thread": "build-54"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.WrongAnnotationUsageProcessor#detect", "started": "21:38:06.927", "dependents": [520], "id": 475, "thread": "build-33"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerCustomConfigBeanTypes", "started": "21:38:06.927", "dependents": [493, 592, 492, 491], "id": 472, "thread": "build-20"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigMappingsBean", "started": "21:38:06.927", "dependents": [495], "id": 474, "thread": "build-63"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#configurationDescriptorBuilding", "started": "21:38:06.503", "dependents": [429, 438, 594, 533, 436, 432, 428, 435, 430, 427, 593, 437, 431, 487], "id": 424, "thread": "build-20"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#registerStartupObservers", "started": "21:38:06.995", "dependents": [498], "id": 497, "thread": "build-6"}, {"duration": 3, "stepId": "io.quarkus.deployment.steps.CapabilityAggregationStep#aggregateCapabilities", "started": "21:38:05.660", "dependents": [435, 375, 275, 437, 440, 377, 359, 517, 456, 426, 373, 297, 436, 241, 226, 591, 252, 222, 374, 307, 460, 453, 320, 223, 233, 417, 559, 305, 408, 560, 321, 293, 466, 234, 224, 332, 489, 422, 246, 424, 251, 255, 403, 311, 423, 549, 303, 508, 554, 465, 372, 566, 240], "id": 220, "thread": "build-79"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ArcProcessor#initializeContainer", "started": "21:38:07.384", "dependents": [594, 522], "id": 521, "thread": "build-91"}, {"duration": 3, "stepId": "io.quarkus.netty.deployment.NettyProcessor#cleanupMacDNSInLog", "started": "21:38:05.531", "dependents": [419, 439], "id": 30, "thread": "build-22"}, {"duration": 3, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#build", "started": "21:38:06.932", "dependents": [594, 486, 588, 589, 485], "id": 481, "thread": "build-6"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanForIOInterceptors", "started": "21:38:06.408", "dependents": [406], "id": 393, "thread": "build-24"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#integrateEagerSecurity", "started": "21:38:06.583", "dependents": [549], "id": 453, "thread": "build-17"}, {"duration": 2, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#setUpPersistenceProviderAndWaitForVertxPool", "started": "21:38:06.962", "dependents": [594, 536, 530], "id": 490, "thread": "build-67"}, {"duration": 2, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#configValidator", "started": "21:38:06.616", "dependents": [546, 592], "id": 464, "thread": "build-17"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#findEnablementStereotypes", "started": "21:38:06.376", "dependents": [361, 364, 365, 362], "id": 354, "thread": "build-78"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#additionalBeans", "started": "21:38:06.410", "dependents": [466, 441, 592], "id": 399, "thread": "build-33"}, {"duration": 2, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#processFooterLogs", "started": "21:38:07.422", "dependents": [542, 573, 541], "id": 540, "thread": "build-46"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheJpaCommonResourceProcessor#lookupNamedQueries_5a86a91ed8ef1aa483288c8239df231983eeb766", "started": "21:38:06.494", "dependents": [416], "id": 413, "thread": "build-15"}, {"duration": 2, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#convertRoutes", "started": "21:38:07.665", "dependents": [584, 585], "id": 565, "thread": "build-29"}, {"duration": 2, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#validate", "started": "21:38:07.103", "dependents": [520], "id": 504, "thread": "build-24"}, {"duration": 2, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createKnownInternalImportMap", "started": "21:38:05.610", "dependents": [578], "id": 153, "thread": "build-52"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.common.deployment.JaxrsMethodsProcessor#jaxrsMethods", "started": "21:38:06.570", "dependents": [444], "id": 443, "thread": "build-20"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#unremovableBeans", "started": "21:38:06.410", "dependents": [510, 498], "id": 396, "thread": "build-48"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#handleInitialSql", "started": "21:38:06.509", "dependents": [434, 433], "id": 429, "thread": "build-82"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanForParameterContainers", "started": "21:38:06.407", "dependents": [390], "id": 384, "thread": "build-26"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.devui.ResteasyReactiveDevUIProcessor#createPages", "started": "21:38:05.567", "dependents": [542, 573], "id": 86, "thread": "build-42"}, {"duration": 2, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#loadAllBuildTimeTemplates", "started": "21:38:09.594", "dependents": [583], "id": 579, "thread": "build-82"}, {"duration": 2, "stepId": "io.quarkus.deployment.recording.AnnotationProxyBuildStep#build", "started": "21:38:05.964", "dependents": [482, 481], "id": 327, "thread": "build-47"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevServicesProcessor#devServicesAutoGenerateByDefault", "started": "21:38:07.396", "dependents": [534], "id": 533, "thread": "build-93"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ArcProcessor#notifyBeanContainerListeners", "started": "21:38:07.388", "dependents": [594, 523], "id": 522, "thread": "build-46"}, {"duration": 2, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#initializeRouter", "started": "21:38:10.387", "dependents": [587, 594, 586], "id": 585, "thread": "build-82"}, {"duration": 2, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#shouldNotRemoveHttpServerOptionsCustomizers", "started": "21:38:05.531", "dependents": [510, 498], "id": 27, "thread": "build-23"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#handleClassLevelExceptionMappers", "started": "21:38:06.409", "dependents": [592, 549], "id": 395, "thread": "build-53"}, {"duration": 2, "stepId": "io.quarkus.security.deployment.SecurityProcessor#configurePermissionCheckers", "started": "21:38:06.927", "dependents": [493, 594, 492, 491], "id": 470, "thread": "build-48"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#convertJpaResourceAnnotationsToQualifier", "started": "21:38:06.509", "dependents": [466], "id": 430, "thread": "build-39"}, {"duration": 2, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#waitForVertxPool", "started": "21:38:06.959", "dependents": [490, 489], "id": 487, "thread": "build-33"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ArcProcessor#launchMode", "started": "21:38:05.522", "dependents": [466, 441], "id": 9, "thread": "build-14"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateConfigPropertiesInjectionPoints", "started": "21:38:07.103", "dependents": [514], "id": 502, "thread": "build-67"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#additionalAsyncTypeMethodScanners", "started": "21:38:05.563", "dependents": [549], "id": 80, "thread": "build-2"}, {"duration": 2, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#registerScope", "started": "21:38:05.533", "dependents": [46], "id": 36, "thread": "build-4"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#subResourcesAsBeans", "started": "21:38:06.409", "dependents": [510, 466, 441, 498], "id": 394, "thread": "build-26"}, {"duration": 2, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#registerDriver", "started": "21:38:05.649", "dependents": [252], "id": 199, "thread": "build-37"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#additionalReflection", "started": "21:38:07.581", "dependents": [592], "id": 557, "thread": "build-29"}, {"duration": 2, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#openSocket", "started": "21:38:10.402", "dependents": [594, 592], "id": 591, "thread": "build-35"}, {"duration": 2, "stepId": "io.quarkus.deployment.steps.ApplicationInfoBuildStep#create", "started": "21:38:05.637", "dependents": [594], "id": 180, "thread": "build-72"}, {"duration": 2, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#registerHttpAuthMechanismAnnotation", "started": "21:38:05.631", "dependents": [377], "id": 172, "thread": "build-70"}, {"duration": 1, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#addDefaultAuthFailureHandler", "started": "21:38:07.655", "dependents": [587, 594, 564], "id": 563, "thread": "build-29"}, {"duration": 1, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#installCliCommands", "started": "21:38:07.569", "dependents": [588, 589], "id": 553, "thread": "build-29"}, {"duration": 1, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#registerBeans", "started": "21:38:06.509", "dependents": [466, 441], "id": 427, "thread": "build-8"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.ProfileBuildStep#defaultProfile", "started": "21:38:05.552", "dependents": [546], "id": 62, "thread": "build-36"}, {"duration": 1, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#mapDeploymentMethods", "started": "21:38:07.424", "dependents": [544, 545], "id": 541, "thread": "build-49"}, {"duration": 1, "stepId": "io.quarkus.deployment.ide.IdeProcessor#effectiveIde", "started": "21:38:06.030", "dependents": [345, 576, 340, 338], "id": 337, "thread": "build-20"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#registerShutdownObservers", "started": "21:38:06.995", "dependents": [498], "id": 496, "thread": "build-15"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.init.InitializationTaskProcessor#startApplicationInitializer", "started": "21:38:07.396", "dependents": [594], "id": 531, "thread": "build-46"}, {"duration": 1, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#applicationSpecificUnwrappedExceptions", "started": "21:38:06.376", "dependents": [408], "id": 351, "thread": "build-47"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#multitenancy", "started": "21:38:06.511", "dependents": [510, 493, 594, 498, 492, 491], "id": 428, "thread": "build-15"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#contributeQuarkusConfigToJpaModel", "started": "21:38:05.603", "dependents": [410], "id": 139, "thread": "build-56"}, {"duration": 1, "stepId": "io.quarkus.vertx.http.deployment.StaticResourcesProcessor#indexHtmlFile", "started": "21:38:05.536", "dependents": [438], "id": 41, "thread": "build-27"}, {"duration": 1, "stepId": "io.quarkus.scheduler.deployment.devui.SchedulerDevUIProcessor#page", "started": "21:38:06.933", "dependents": [542, 573], "id": 480, "thread": "build-67"}, {"duration": 1, "stepId": "io.quarkus.flyway.deployment.FlywayAlwaysEnabledProcessor#indexFlyway", "started": "21:38:05.556", "dependents": [342], "id": 69, "thread": "build-36"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheJpaCommonResourceProcessor#buildNamedQueryMap", "started": "21:38:06.496", "dependents": [594], "id": 416, "thread": "build-67"}, {"duration": 1, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#beans", "started": "21:38:05.739", "dependents": [466, 441], "id": 260, "thread": "build-68"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.LifecycleEventsBuildStep#startupEvent", "started": "21:38:10.400", "dependents": [594, 591], "id": 589, "thread": "build-82"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#registerHibernateOrmMetadataForCoreDialects", "started": "21:38:05.537", "dependents": [426, 424], "id": 42, "thread": "build-10"}, {"duration": 1, "stepId": "io.quarkus.vertx.http.deployment.devmode.NotFoundProcessor#resourceNotFoundDataAvailable", "started": "21:38:05.651", "dependents": [466, 441], "id": 202, "thread": "build-77"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.DevServicesConfigBuildStep#deprecated", "started": "21:38:05.519", "dependents": [534], "id": 4, "thread": "build-9"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#suppressNonRuntimeConfigChanged", "started": "21:38:05.542", "dependents": [309], "id": 48, "thread": "build-30"}, {"duration": 1, "stepId": "io.quarkus.devui.deployment.menu.ExtensionsProcessor#createExtensionsPages", "started": "21:38:09.265", "dependents": [576], "id": 574, "thread": "build-110"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#setupPersistenceProvider", "started": "21:38:06.962", "dependents": [594, 536, 530], "id": 489, "thread": "build-39"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#setupVersionField", "started": "21:38:05.517", "dependents": [592], "id": 1, "thread": "build-6"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#recordEntityToPersistenceUnit", "started": "21:38:07.760", "dependents": [594], "id": 571, "thread": "build-93"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#callInitializer", "started": "21:38:07.392", "dependents": [594], "id": 526, "thread": "build-72"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ArcProcessor#quarkusMain", "started": "21:38:05.528", "dependents": [466, 441, 268], "id": 14, "thread": "build-21"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ArcProcessor#exposeCustomScopeNames", "started": "21:38:05.539", "dependents": [137, 466, 475, 441, 447, 448, 268, 128], "id": 46, "thread": "build-19"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.ShutdownListenerBuildStep#setupShutdown", "started": "21:38:10.400", "dependents": [594], "id": 590, "thread": "build-87"}, {"duration": 1, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#generateDataSourceBeans", "started": "21:38:06.501", "dependents": [493, 594, 424, 435, 433, 529, 492, 494, 425, 491, 423, 530], "id": 422, "thread": "build-20"}, {"duration": 1, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#lookupNamedQueries_5a86a91ed8ef1aa483288c8239df231983eeb766", "started": "21:38:06.495", "dependents": [415], "id": 414, "thread": "build-20"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#unknownConfigFiles", "started": "21:38:06.357", "dependents": [594], "id": 343, "thread": "build-36"}, {"duration": 1, "stepId": "io.quarkus.deployment.SslProcessor#setupNativeSsl", "started": "21:38:05.647", "dependents": [307, 199, 422, 488], "id": 194, "thread": "build-74"}, {"duration": 1, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#build", "started": "21:38:06.494", "dependents": [594], "id": 412, "thread": "build-23"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#ensureBeanLookupAvailable", "started": "21:38:05.593", "dependents": [510, 498], "id": 123, "thread": "build-51"}, {"duration": 1, "stepId": "io.quarkus.security.deployment.SecurityProcessor#gatherClassSecurityChecks", "started": "21:38:06.583", "dependents": [458], "id": 450, "thread": "build-54"}, {"duration": 1, "stepId": "io.quarkus.security.deployment.SecurityProcessor#createPermissionSecurityChecksBuilder", "started": "21:38:06.583", "dependents": [458, 470], "id": 452, "thread": "build-82"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#processInterceptedStaticMethods", "started": "21:38:06.947", "dependents": [568, 570, 592, 572, 569, 567], "id": 484, "thread": "build-8"}, {"duration": 1, "stepId": "io.quarkus.reactive.datasource.deployment.ReactiveDataSourceProcessor#addQualifierAsBean", "started": "21:38:05.603", "dependents": [466, 441], "id": 136, "thread": "build-12"}, {"duration": 1, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDefaultLevels", "started": "21:38:05.675", "dependents": [546, 439], "id": 239, "thread": "build-56"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ArcProcessor#signalBeanContainerReady", "started": "21:38:07.390", "dependents": [528, 594, 589, 559, 529, 549, 558, 587, 524, 527, 526, 532, 586, 545, 530, 525], "id": 523, "thread": "build-91"}, {"duration": 1, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#reflections", "started": "21:38:05.535", "dependents": [592], "id": 35, "thread": "build-25"}, {"duration": 0, "stepId": "io.quarkus.deployment.JniProcessor#setupJni", "started": "21:38:05.602", "dependents": [488], "id": 130, "thread": "build-12"}, {"duration": 0, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#cleanupVertxWarnings", "started": "21:38:05.592", "dependents": [419, 439], "id": 119, "thread": "build-21"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.ResteasyReactiveServerIntegrationProcessor#unwrappedExceptions", "started": "21:38:05.654", "dependents": [408], "id": 204, "thread": "build-72"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.CurateOutcomeBuildStep#removeResources", "started": "21:38:05.605", "dependents": [572], "id": 144, "thread": "build-56"}, {"duration": 0, "stepId": "io.quarkus.security.jpa.common.deployment.QuarkusSecurityJpaCommonProcessor#provideJpaSecurityDefinition", "started": "21:38:06.503", "dependents": [431], "id": 421, "thread": "build-82"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.AdditionalClassLoaderResourcesBuildStep#appendAdditionalClassloaderResources", "started": "21:38:05.611", "dependents": [346], "id": 151, "thread": "build-61"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#candidatesForFieldAccess", "started": "21:38:06.494", "dependents": [418], "id": 411, "thread": "build-67"}, {"duration": 0, "stepId": "io.quarkus.deployment.index.ApplicationArchiveBuildStep#addConfiguredIndexedDependencies", "started": "21:38:05.608", "dependents": [342], "id": 147, "thread": "build-60"}, {"duration": 0, "stepId": "io.quarkus.datasource.deployment.devui.DevUIDatasourceProcessor#create", "started": "21:38:05.605", "dependents": [542, 573], "id": 143, "thread": "build-32"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#quarkusApplication", "started": "21:38:06.379", "dependents": [466, 441], "id": 353, "thread": "build-59"}, {"duration": 0, "stepId": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsProcessor#check", "started": "21:38:06.573", "dependents": [], "id": 444, "thread": "build-63"}, {"duration": 0, "stepId": "io.quarkus.security.jpa.deployment.QuarkusSecurityJpaProcessor#panacheEntityPredicate", "started": "21:38:06.502", "dependents": [421, 431], "id": 420, "thread": "build-17"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#marker", "started": "21:38:05.640", "dependents": [342], "id": 182, "thread": "build-70"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#produceModel", "started": "21:38:05.566", "dependents": [593, 357, 567], "id": 81, "thread": "build-36"}, {"duration": 0, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setProperty", "started": "21:38:05.572", "dependents": [594], "id": 92, "thread": "build-34"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#perClassExceptionMapperSupport", "started": "21:38:06.409", "dependents": [466], "id": 388, "thread": "build-48"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.AutoInjectFieldProcessor#autoInjectQualifiers", "started": "21:38:06.570", "dependents": [446, 448], "id": 442, "thread": "build-33"}, {"duration": 0, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#registerRSASigProvider", "started": "21:38:05.605", "dependents": [215], "id": 142, "thread": "build-21"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#addAllWriteableMarker", "started": "21:38:07.580", "dependents": [572], "id": 556, "thread": "build-93"}, {"duration": 0, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#missingDevUIMessageHandler", "started": "21:38:06.022", "dependents": [588, 589], "id": 332, "thread": "build-20"}, {"duration": 0, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#feature", "started": "21:38:05.524", "dependents": [594], "id": 7, "thread": "build-4"}, {"duration": 0, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#preventLoggerContention", "started": "21:38:05.521", "dependents": [239], "id": 2, "thread": "build-11"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#additionalBeans", "started": "21:38:05.581", "dependents": [466, 441], "id": 105, "thread": "build-44"}, {"duration": 0, "stepId": "io.quarkus.deployment.ForkJoinPoolProcessor#setProperty", "started": "21:38:05.533", "dependents": [594], "id": 28, "thread": "build-5"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.GeneratedStaticResourcesProcessor#devMode", "started": "21:38:05.535", "dependents": [295, 438, 61], "id": 33, "thread": "build-13"}, {"duration": 0, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#produceCoroutineScope", "started": "21:38:05.539", "dependents": [466, 441], "id": 43, "thread": "build-21"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#initializeRolesAllowedConfigExp", "started": "21:38:07.103", "dependents": [594], "id": 499, "thread": "build-48"}, {"duration": 0, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#config", "started": "21:38:05.636", "dependents": [488], "id": 175, "thread": "build-71"}, {"duration": 0, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#featureAndCapability", "started": "21:38:05.535", "dependents": [594, 220], "id": 32, "thread": "build-24"}, {"duration": 0, "stepId": "io.quarkus.security.jpa.deployment.QuarkusSecurityJpaProcessor#feature", "started": "21:38:05.646", "dependents": [594], "id": 192, "thread": "build-70"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initMtlsClientAuth", "started": "21:38:05.588", "dependents": [466, 441], "id": 114, "thread": "build-41"}, {"duration": 0, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#logCleanupFilters", "started": "21:38:05.571", "dependents": [419, 439], "id": 89, "thread": "build-42"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#feature", "started": "21:38:05.545", "dependents": [594], "id": 51, "thread": "build-31"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#unremovableAsyncObserverExceptionHandlers", "started": "21:38:05.547", "dependents": [510, 498], "id": 52, "thread": "build-31"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#enrollBeanValidationTypeSafeActivatorForReflection", "started": "21:38:05.663", "dependents": [592], "id": 223, "thread": "build-10"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#aggregateParameterContainers", "started": "21:38:06.409", "dependents": [451, 398, 549, 396], "id": 390, "thread": "build-46"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ExecutorServiceProcessor#executorServiceBean", "started": "21:38:05.902", "dependents": [493, 492, 491], "id": 314, "thread": "build-63"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#buildSetup", "started": "21:38:05.558", "dependents": [594], "id": 70, "thread": "build-38"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#includeArchivesHostingEntityPackagesInIndex", "started": "21:38:05.611", "dependents": [342], "id": 152, "thread": "build-51"}, {"duration": 0, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#logCleanup", "started": "21:38:05.654", "dependents": [419, 439], "id": 205, "thread": "build-37"}, {"duration": 0, "stepId": "io.quarkus.deployment.ConstructorPropertiesProcessor#build", "started": "21:38:06.375", "dependents": [592], "id": 347, "thread": "build-6"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#featureBuildItem", "started": "21:38:05.566", "dependents": [594], "id": 82, "thread": "build-9"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#pathInterfaceImpls", "started": "21:38:06.409", "dependents": [466, 441], "id": 387, "thread": "build-36"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#configureHandlers", "started": "21:38:07.655", "dependents": [594], "id": 562, "thread": "build-93"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.BuildMetricsProcessor#createBuildMetricsPages", "started": "21:38:05.529", "dependents": [576], "id": 17, "thread": "build-21"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#createConfigurationPages", "started": "21:38:07.403", "dependents": [576], "id": 535, "thread": "build-49"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#feature", "started": "21:38:05.619", "dependents": [594], "id": 157, "thread": "build-65"}, {"duration": 0, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#reduceLogging", "started": "21:38:05.534", "dependents": [239], "id": 29, "thread": "build-13"}, {"duration": 0, "stepId": "io.quarkus.deployment.ExtensionLoader#booleanSupplierFactory", "started": "21:38:05.576", "dependents": [195], "id": 96, "thread": "build-42"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#createContinuousTestingPages", "started": "21:38:05.551", "dependents": [576], "id": 60, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateLogFilterBuildStep#setupLogFilters", "started": "21:38:05.570", "dependents": [419, 439], "id": 87, "thread": "build-43"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#feature", "started": "21:38:05.534", "dependents": [594], "id": 31, "thread": "build-24"}, {"duration": 0, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#logCleanup", "started": "21:38:05.649", "dependents": [419, 439], "id": 196, "thread": "build-70"}, {"duration": 0, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#feature", "started": "21:38:05.551", "dependents": [594], "id": 59, "thread": "build-6"}, {"duration": 0, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#transformSchedulerBeans", "started": "21:38:05.739", "dependents": [466], "id": 259, "thread": "build-36"}, {"duration": 0, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#configFile", "started": "21:38:05.547", "dependents": [438], "id": 53, "thread": "build-33"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateUserTypeProcessor#build", "started": "21:38:06.380", "dependents": [592], "id": 355, "thread": "build-43"}, {"duration": 0, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#feature", "started": "21:38:05.535", "dependents": [594], "id": 34, "thread": "build-5"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createRelocationMap", "started": "21:38:05.532", "dependents": [578], "id": 22, "thread": "build-21"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#transformSecurityAnnotations", "started": "21:38:06.405", "dependents": [466], "id": 380, "thread": "build-78"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#searchForProviders", "started": "21:38:05.664", "dependents": [342], "id": 224, "thread": "build-18"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#startPersistenceUnits", "started": "21:38:07.396", "dependents": [594, 588, 589], "id": 530, "thread": "build-29"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#defineTypeOfImpliedPU", "started": "21:38:06.503", "dependents": [424, 435, 430], "id": 423, "thread": "build-17"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#applicationReflection", "started": "21:38:05.640", "dependents": [592], "id": 183, "thread": "build-73"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#produceModel", "started": "21:38:05.578", "dependents": [593, 357, 567], "id": 100, "thread": "build-19"}, {"duration": 0, "stepId": "io.quarkus.netty.deployment.NettyProcessor#limitArenaSize", "started": "21:38:05.666", "dependents": [594], "id": 227, "thread": "build-80"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#validateAsyncObserverExceptionHandlers", "started": "21:38:07.103", "dependents": [520], "id": 501, "thread": "build-36"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.ManagementInterfaceSecurityProcessor#initializeAuthMechanismHandler", "started": "21:38:07.392", "dependents": [594], "id": 525, "thread": "build-29"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setMinimalNettyMaxOrderSize", "started": "21:38:05.649", "dependents": [221, 227], "id": 197, "thread": "build-10"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.TestsAsBeansProcessor#testClassBeans", "started": "21:38:05.664", "dependents": [466, 441], "id": 225, "thread": "build-38"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#produceJcaSecurityProviders", "started": "21:38:05.614", "dependents": [289, 215, 189], "id": 155, "thread": "build-62"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.PreloadClassesBuildStep#registerPreInitClasses", "started": "21:38:05.537", "dependents": [], "id": 39, "thread": "build-21"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#collectEntityClasses", "started": "21:38:06.376", "dependents": [568], "id": 349, "thread": "build-82"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#createAllRoutes", "started": "21:38:10.127", "dependents": [583], "id": 581, "thread": "build-82"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#contributePersistenceXmlToJpaModel", "started": "21:38:05.792", "dependents": [410], "id": 270, "thread": "build-35"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.BannerProcessor#watchBannerChanges", "started": "21:38:05.586", "dependents": [438], "id": 112, "thread": "build-41"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#resolveConfigExpressionRoles", "started": "21:38:06.609", "dependents": [594], "id": 459, "thread": "build-54"}, {"duration": 0, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#feature", "started": "21:38:05.605", "dependents": [594], "id": 140, "thread": "build-12"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#produceLoggingCategories", "started": "21:38:05.595", "dependents": [239], "id": 124, "thread": "build-21"}, {"duration": 0, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#contributeClassesToIndex", "started": "21:38:05.561", "dependents": [346], "id": 73, "thread": "build-40"}, {"duration": 0, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#handle", "started": "21:38:05.563", "dependents": [419, 439], "id": 77, "thread": "build-9"}, {"duration": 0, "stepId": "io.quarkus.deployment.dev.ConfigureDisableInstrumentationBuildStep#configure", "started": "21:38:05.625", "dependents": [588, 589], "id": 165, "thread": "build-21"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#setUpDenyAllJaxRs", "started": "21:38:05.587", "dependents": [460], "id": 113, "thread": "build-45"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#conditionTransformer", "started": "21:38:06.391", "dependents": [466], "id": 366, "thread": "build-53"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.CurateOutcomeBuildStep#curateOutcome", "started": "21:38:05.605", "dependents": [575, 486, 163, 369, 166, 144, 195, 541, 288, 342, 544, 580, 440, 572, 573, 246, 426, 220, 167, 189, 252, 345, 326, 434, 576, 577, 257, 542], "id": 141, "thread": "build-50"}, {"duration": 0, "stepId": "io.quarkus.elytron.security.common.deployment.QuarkusSecurityCommonProcessor#services", "started": "21:38:05.608", "dependents": [592], "id": 145, "thread": "build-56"}, {"duration": 0, "stepId": "io.quarkus.deployment.pkg.steps.NativeImageBuildStep#ignoreBuildPropertyChanges", "started": "21:38:05.657", "dependents": [309], "id": 211, "thread": "build-78"}, {"duration": 0, "stepId": "io.quarkus.flyway.deployment.FlywayAlwaysEnabledProcessor#build", "started": "21:38:05.649", "dependents": [594], "id": 198, "thread": "build-75"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ObserverValidationProcessor#validateApplicationObserver", "started": "21:38:07.103", "dependents": [520], "id": 500, "thread": "build-26"}, {"duration": 0, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#agroal", "started": "21:38:05.637", "dependents": [594], "id": 177, "thread": "build-73"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#create", "started": "21:38:05.595", "dependents": [542, 573], "id": 125, "thread": "build-53"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#unremovableContextMethodParams", "started": "21:38:06.409", "dependents": [510, 498], "id": 386, "thread": "build-52"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForParamConverters_59e3169e3a646b7fcf3083416f558434b73816c5", "started": "21:38:06.407", "dependents": [397], "id": 382, "thread": "build-67"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#registerAnnotations", "started": "21:38:05.554", "dependents": [466, 441, 268], "id": 64, "thread": "build-36"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.HotDeploymentConfigBuildStep#configFile", "started": "21:38:05.529", "dependents": [438], "id": 16, "thread": "build-22"}, {"duration": 0, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLogFilters", "started": "21:38:05.572", "dependents": [419, 439], "id": 93, "thread": "build-42"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#additionalBeans", "started": "21:38:05.576", "dependents": [466, 441], "id": 97, "thread": "build-34"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#buildNamedQueryMap", "started": "21:38:06.496", "dependents": [594], "id": 415, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#notFoundRoutes", "started": "21:38:10.387", "dependents": [586], "id": 584, "thread": "build-110"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.DevServicesProcessor#createDevServicesPages", "started": "21:38:07.422", "dependents": [576], "id": 539, "thread": "build-72"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#handleFieldSecurity", "started": "21:38:07.566", "dependents": [552], "id": 550, "thread": "build-29"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#checkTransactionsSupport", "started": "21:38:05.664", "dependents": [520], "id": 226, "thread": "build-34"}, {"duration": 0, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#unremoveableBeans", "started": "21:38:05.545", "dependents": [510, 498], "id": 50, "thread": "build-19"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initializeAuthenticationHandler", "started": "21:38:07.392", "dependents": [594], "id": 524, "thread": "build-55"}, {"duration": 0, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#autoRegisterModules", "started": "21:38:06.377", "dependents": [404], "id": 350, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#collectEntityClasses", "started": "21:38:06.376", "dependents": [570], "id": 348, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveAlwaysEnabledProcessor#feature", "started": "21:38:05.643", "dependents": [594], "id": 187, "thread": "build-10"}, {"duration": 0, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDefaultLogCleanupFilters", "started": "21:38:06.501", "dependents": [546], "id": 419, "thread": "build-67"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#addPersistenceUnitAnnotationToIndex", "started": "21:38:05.528", "dependents": [346], "id": 12, "thread": "build-2"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigPropertiesBean", "started": "21:38:06.927", "dependents": [495], "id": 469, "thread": "build-26"}, {"duration": 0, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#filterNettyHostsFileParsingWarn", "started": "21:38:05.537", "dependents": [419, 439], "id": 40, "thread": "build-24"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmAlwaysEnabledProcessor#featureBuildItem", "started": "21:38:05.566", "dependents": [594], "id": 83, "thread": "build-19"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ObservabilityProcessor#methodScanner", "started": "21:38:05.663", "dependents": [549], "id": 222, "thread": "build-64"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#transformAdditionalSecuredClassesToMethods", "started": "21:38:05.639", "dependents": [380, 458], "id": 179, "thread": "build-73"}, {"duration": 0, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#unremovableBeans", "started": "21:38:05.578", "dependents": [510, 498], "id": 101, "thread": "build-31"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#collectInterceptedMethods", "started": "21:38:06.405", "dependents": [453, 383], "id": 379, "thread": "build-67"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#warnOfSchemaProblems", "started": "21:38:10.400", "dependents": [594], "id": 588, "thread": "build-35"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#hotDeploymentWatchedFiles", "started": "21:38:05.602", "dependents": [438], "id": 131, "thread": "build-30"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.ReflectionDiagnosticProcessor#writeReflectionData", "started": "21:38:10.404", "dependents": [], "id": 592, "thread": "build-87"}, {"duration": 0, "stepId": "io.quarkus.caffeine.deployment.CaffeineProcessor#cacheLoaders", "started": "21:38:06.379", "dependents": [592], "id": 352, "thread": "build-53"}], "started": "2025-10-08T21:38:05.513", "items": [{"count": 1314, "class": "io.quarkus.deployment.builditem.ConfigDescriptionBuildItem"}, {"count": 877, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveClassBuildItem"}, {"count": 640, "class": "io.quarkus.deployment.builditem.GeneratedClassBuildItem"}, {"count": 569, "class": "io.quarkus.deployment.builditem.BytecodeTransformerBuildItem"}, {"count": 178, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveMethodBuildItem"}, {"count": 138, "class": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheNamedQueryEntityClassBuildStep"}, {"count": 138, "class": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheNamedQueryEntityClassBuildStep"}, {"count": 95, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveFieldBuildItem"}, {"count": 93, "class": "io.quarkus.hibernate.validator.spi.AdditionalConstrainedClassBuildItem"}, {"count": 80, "class": "io.quarkus.deployment.builditem.MainBytecodeRecorderBuildItem"}, {"count": 79, "class": "io.quarkus.arc.deployment.AdditionalBeanBuildItem"}, {"count": 55, "class": "io.quarkus.deployment.builditem.StaticBytecodeRecorderBuildItem"}, {"count": 45, "class": "io.quarkus.vertx.http.deployment.RouteBuildItem"}, {"count": 36, "class": "io.quarkus.deployment.builditem.nativeimage.RuntimeInitializedClassBuildItem"}, {"count": 36, "class": "io.quarkus.arc.deployment.SyntheticBeanBuildItem"}, {"count": 35, "class": "io.quarkus.deployment.builditem.HotDeploymentWatchedFileBuildItem"}, {"count": 34, "class": "io.quarkus.hibernate.reactive.panache.deployment.PanacheEntityClassBuildItem"}, {"count": 34, "class": "io.quarkus.deployment.builditem.ConfigClassBuildItem"}, {"count": 31, "class": "io.quarkus.arc.deployment.ConfigPropertyBuildItem"}, {"count": 28, "class": "io.quarkus.deployment.builditem.RunTimeConfigurationDefaultBuildItem"}, {"count": 26, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveHierarchyBuildItem"}, {"count": 26, "class": "io.quarkus.arc.deployment.UnremovableBeanBuildItem"}, {"count": 23, "class": "io.quarkus.deployment.builditem.CapabilityBuildItem"}, {"count": 23, "class": "io.quarkus.deployment.builditem.FeatureBuildItem"}, {"count": 20, "class": "io.quarkus.deployment.logging.LogCleanupFilterBuildItem"}, {"count": 16, "class": "io.quarkus.deployment.builditem.AdditionalIndexedClassesBuildItem"}, {"count": 14, "class": "io.quarkus.devui.spi.JsonRPCProvidersBuildItem"}, {"count": 12, "class": "io.quarkus.vertx.http.deployment.webjar.WebJarBuildItem"}, {"count": 11, "class": "io.quarkus.devui.deployment.DevUIWebJarBuildItem"}, {"count": 11, "class": "io.quarkus.devui.deployment.DevUIRoutesBuildItem"}, {"count": 11, "class": "io.quarkus.arc.deployment.AnnotationsTransformerBuildItem"}, {"count": 10, "class": "io.quarkus.deployment.builditem.SuppressNonRuntimeConfigChangedWarningBuildItem"}, {"count": 10, "class": "io.quarkus.deployment.builditem.AdditionalApplicationArchiveMarkerBuildItem"}, {"count": 9, "class": "io.quarkus.devui.spi.page.CardPageBuildItem"}, {"count": 9, "class": "io.quarkus.devui.deployment.InternalPageBuildItem"}, {"count": 8, "class": "io.quarkus.deployment.builditem.ConsoleCommandBuildItem"}, {"count": 8, "class": "io.quarkus.hibernate.orm.deployment.spi.DatabaseKindDialectBuildItem"}, {"count": 8, "class": "io.quarkus.resteasy.reactive.spi.ExceptionMapperBuildItem"}, {"count": 8, "class": "io.quarkus.deployment.builditem.nativeimage.RuntimeReinitializedClassBuildItem"}, {"count": 7, "class": "io.quarkus.resteasy.reactive.server.spi.MethodScannerBuildItem"}, {"count": 7, "class": "io.quarkus.devui.spi.buildtime.BuildTimeActionBuildItem"}, {"count": 7, "class": "io.quarkus.vertx.http.deployment.devmode.NotFoundPageDisplayableEndpointBuildItem"}, {"count": 7, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyWriterBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.builditem.SystemPropertyBuildItem"}, {"count": 6, "class": "io.quarkus.arc.deployment.BeanDefiningAnnotationBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.builditem.ServiceStartBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageSystemPropertyBuildItem"}, {"count": 6, "class": "io.quarkus.arc.deployment.AutoAddScopeBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsAllowedBuildItem"}, {"count": 5, "class": "io.quarkus.vertx.http.deployment.FilterBuildItem"}, {"count": 5, "class": "io.quarkus.arc.deployment.GeneratedBeanBuildItem"}, {"count": 5, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageResourceBuildItem"}, {"count": 5, "class": "io.quarkus.devui.deployment.BuildTimeConstBuildItem"}, {"count": 4, "class": "io.quarkus.vertx.http.deployment.HttpAuthMechanismAnnotationBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.RunTimeConfigBuilderBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageConfigBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyWriterOverrideBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyReaderBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.LogCategoryBuildItem"}, {"count": 4, "class": "io.quarkus.arc.deployment.BeanRegistrationPhaseBuildItem$BeanConfiguratorBuildItem"}, {"count": 4, "class": "io.quarkus.vertx.http.deployment.spi.RouteBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.server.spi.UnwrappedExceptionBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyReaderOverrideBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.StaticInitConfigBuilderBuildItem"}, {"count": 3, "class": "io.quarkus.jackson.spi.ClassPathJacksonModuleBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.ApplicationClassPredicateBuildItem"}, {"count": 3, "class": "io.quarkus.resteasy.reactive.spi.CustomExceptionMapperBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.ConfigMappingBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.GeneratedResourceBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.ShutdownListenerBuildItem"}, {"count": 2, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceInterceptorsContributorBuildItem"}, {"count": 2, "class": "io.quarkus.hibernate.orm.panache.deployment.PanacheEntityClassBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.ObjectSubstitutionBuildItem"}, {"count": 2, "class": "io.quarkus.devui.spi.buildtime.QuteTemplateBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.RecordableConstructorBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.ExtensionSslNativeSupportBuildItem"}, {"count": 2, "class": "io.quarkus.hibernate.orm.deployment.spi.AdditionalJpaModelBuildItem"}, {"count": 2, "class": "io.quarkus.smallrye.openapi.deployment.spi.AddToOpenAPIDefinitionBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.BytecodeRecorderObjectLoaderBuildItem"}, {"count": 2, "class": "io.quarkus.devui.spi.buildtime.StaticContentBuildItem"}, {"count": 2, "class": "io.quarkus.arc.deployment.BeanContainerListenerBuildItem"}, {"count": 2, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceConfigurationHandlerBuildItem"}, {"count": 2, "class": "io.quarkus.datasource.deployment.spi.DefaultDataSourceDbKindBuildItem"}, {"count": 2, "class": "io.quarkus.devui.spi.page.FooterPageBuildItem"}, {"count": 2, "class": "io.quarkus.hibernate.orm.deployment.PersistenceProviderSetUpBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.dev.testing.TestListenerBuildItem"}, {"count": 2, "class": "io.quarkus.devui.deployment.InternalImportMapBuildItem"}, {"count": 2, "class": "io.quarkus.arc.deployment.AutoInjectAnnotationBuildItem"}, {"count": 2, "class": "io.quarkus.hibernate.orm.panache.deployment.EntityToPersistenceUnitBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.MvnpmBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.AnnotationProxyBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.BytecodeRecorderConstantDefinitionBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.console.ConsoleInstalledBuildItem"}, {"count": 1, "class": "io.quarkus.panache.hibernate.common.deployment.HibernateModelClassCandidatesForFieldAccessBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.SynthesisFinishedBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageResourceBundleBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.core.deployment.EventLoopCountBuildItem"}, {"count": 1, "class": "io.quarkus.security.jpa.common.deployment.PanacheEntityPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.core.deployment.CoreVertxBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ContextResolversBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.DockerStatusBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveHierarchyIgnoreWarningBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.deployment.LocalCodecSelectorTypesBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.InitialRouterBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.openapi.deployment.spi.OpenApiDocumentBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.dev.ExceptionNotificationBuildItem"}, {"count": 1, "class": "io.quarkus.swaggerui.deployment.SwaggerUiBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.CompiledJavaVersionBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ValidationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.EventLoopSupplierBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.BooleanSupplierFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.validator.spi.BeanValidationAnnotationsBuildItem"}, {"count": 1, "class": "io.quarkus.tls.TlsRegistryBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ParamConverterProvidersBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.ImpliedBlockingPersistenceUnitTypeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.spi.HandlerConfigurationProviderBuildItem"}, {"count": 1, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceProviderBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.DevServicesLauncherConfigResultBuildItem"}, {"count": 1, "class": "io.quarkus.security.spi.AdditionalSecurityConstrainerEventPropsBuildItem"}, {"count": 1, "class": "io.quarkus.panache.common.deployment.PanacheEntityClassesBuildItem"}, {"count": 1, "class": "io.quarkus.panache.hibernate.common.deployment.HibernateEnhancersRegisteredBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationIndexBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ThreadFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.spi.JdbcDriverBuildItem"}, {"count": 1, "class": "io.quarkus.security.spi.PermissionsAllowedMetaAnnotationBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.logging.LoggingSetupBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InterceptorBindingRegistrarBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.spi.JdbcInitialSQLGeneratorBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ArcContainerBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.JsonRPCRuntimeMethodsBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.context.deployment.spi.ThreadContextProviderBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationClassNameBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.InitTaskCompletedBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.SecurityInformationBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.StreamingLogHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.dev.DisableInstrumentationForIndexPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.logging.LoggingDecorateBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.spi.GlobalHandlerCustomizerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CurrentContextFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ParameterContainersBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ConfigurationBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ApplicationResultBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.BodyHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildExclusionsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.LogCategoryMinLevelDefaultsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.IOThreadDetectorBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InvokerFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.SslNativeConfigBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CustomScopeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ServerDefaultProducesHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.integration.HibernateOrmIntegrationRuntimeConfiguredBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.IdeRunningProcessBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.TransformedClassesBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.EventLoopGroupBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelIndexBuildItem"}, {"count": 1, "class": "io.quarkus.security.deployment.SecurityProcessor$MethodSecurityChecks"}, {"count": 1, "class": "io.quarkus.arc.deployment.devui.ArcBeanInfoBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanDiscoveryFinishedBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.RunTimeConfigurationProxyBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceInterceptorsBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.VertxDevUILogBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildCompatibleExtensionsBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.ThemeVarsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelPersistenceUnitMappingBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.context.deployment.ContextPropagationInitializedBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ExceptionMappersBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InterceptorResolverBuildItem"}, {"count": 1, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceResultBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.IndexDependencyBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor$AdditionalConstrainedClassesIndexBuildItem"}, {"count": 1, "class": "io.quarkus.security.deployment.PermissionSecurityChecksBuilderBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanArchiveIndexBuildItem"}, {"count": 1, "class": "io.quarkus.jackson.spi.JacksonModuleBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ConsoleFormatterBannerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.SuppressConditionGeneratorBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildTimeEnabledStereotypesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationArchivesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ContextHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.TransformedAnnotationsBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveResourceMethodEntriesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.GeneratedFileSystemResourceHandledBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.OutputTargetBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.AuthorizationPolicyInstancesBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.PreBeanContainerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InjectionPointTransformerBuildItem"}, {"count": 1, "class": "io.quarkus.panache.hibernate.common.deployment.HibernateMetamodelForFieldAccessBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.webjar.WebJarResultsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelPersistenceUnitContributionBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.MinNettyAllocatorMaxOrderBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.openapi.deployment.OpenApiFilteredIndexViewBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.NonApplicationRootPathBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.VertxWebRouterBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.CombinedIndexBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.Capabilities"}, {"count": 1, "class": "io.quarkus.devui.deployment.ExtensionsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ExecutorBuildItem"}, {"count": 1, "class": "io.quarkus.security.deployment.JCAProviderBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.SetupEndpointsResultBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDeploymentInfoBuildItem"}, {"count": 1, "class": "io.quarkus.reactive.pg.client.deployment.PgPoolBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ObserverRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceScanningResultBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ServerSerialisersBuildItem"}, {"count": 1, "class": "io.quarkus.scheduler.deployment.DiscoveredImplementationsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.deployment.VertxBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDeploymentBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanContainerBuildItem"}, {"count": 1, "class": "io.quarkus.devui.spi.buildtime.FooterLogBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.EffectiveIdeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.JaxRsResourceIndexBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.CurateOutcomeBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.HttpRootPathBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.DeploymentMethodBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.steps.CapabilityAggregationStep$CapabilitiesConfiguredInDescriptorsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationStartBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.RelocationImportMapBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor$HttpAuthenticationHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.reactive.datasource.deployment.VertxPoolBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.AggregatedParameterContainersBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ContextRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.scheduler.deployment.SchedulerImplementationBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CustomScopeAnnotationsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.PersistenceUnitDescriptorBuildItem"}, {"count": 1, "class": "io.quarkus.flyway.deployment.FlywayProcessor$MigrationStateBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ContextRegistrationPhaseBuildItem$ContextConfiguratorBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.spi.JdbcDataSourceSchemaReadyBuildItem"}, {"count": 1, "class": "io.quarkus.panache.common.deployment.PanacheMethodCustomizerBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.BuiltInReaderOverrideBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CompletedApplicationClassPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationInfoBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.IdeFileBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.MainClassBuildItem"}], "itemsCount": 5125, "buildTarget": "quarkus-application"}