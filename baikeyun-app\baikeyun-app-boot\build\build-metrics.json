{"duration": 5697, "records": [{"duration": 1950, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#getAllExtensions", "started": "21:52:24.164", "dependents": [576, 574, 575, 580, 581], "id": 573, "thread": "build-71"}, {"duration": 1764, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#pregenProxies", "started": "21:52:25.876", "dependents": [594], "id": 593, "thread": "build-128"}, {"duration": 1352, "stepId": "io.quarkus.deployment.steps.ClassTransformingBuildStep#handleClassTransformation", "started": "21:52:24.524", "dependents": [593], "id": 572, "thread": "build-47"}, {"duration": 1165, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#build", "started": "21:52:23.244", "dependents": [594], "id": 564, "thread": "build-47"}, {"duration": 893, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#enhancerDomainObjects", "started": "21:52:23.622", "dependents": [568, 569, 572, 570], "id": 567, "thread": "build-39"}, {"duration": 409, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateConfigClass", "started": "21:52:22.236", "dependents": [], "id": 341, "thread": "build-15"}, {"duration": 395, "stepId": "io.quarkus.deployment.index.ApplicationArchiveBuildStep#build", "started": "21:52:22.543", "dependents": [344, 568, 424, 500, 441, 586, 572, 345, 346, 425, 376, 343], "id": 342, "thread": "build-14"}, {"duration": 390, "stepId": "io.quarkus.vertx.http.deployment.webjar.WebJarProcessor#processWebJarDevMode", "started": "21:52:26.114", "dependents": [594, 582, 581], "id": 580, "thread": "build-29"}, {"duration": 372, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#setupConsole", "started": "21:52:22.233", "dependents": [338, 339, 440, 335, 345], "id": 334, "thread": "build-24"}, {"duration": 336, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#parsePersistenceXmlDescriptors", "started": "21:52:22.212", "dependents": [424, 425, 330], "id": 329, "thread": "build-22"}, {"duration": 330, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#logConsoleCommand", "started": "21:52:22.213", "dependents": [553], "id": 327, "thread": "build-29"}, {"duration": 302, "stepId": "io.quarkus.deployment.steps.ApplicationIndexBuildStep#build", "started": "21:52:22.239", "dependents": [328, 419, 431, 466, 475, 477, 549, 342], "id": 326, "thread": "build-12"}, {"duration": 300, "stepId": "io.quarkus.virtual.threads.deployment.VirtualThreadsProcessor#setup", "started": "21:52:22.219", "dependents": [441, 491, 594, 492, 466, 493], "id": 321, "thread": "build-39"}, {"duration": 294, "stepId": "io.quarkus.deployment.ide.IdeProcessor#detectRunningIdeProcesses", "started": "21:52:22.205", "dependents": [317], "id": 316, "thread": "build-5"}, {"duration": 286, "stepId": "io.quarkus.arc.deployment.ArcProcessor#generateResources", "started": "21:52:23.840", "dependents": [521, 572, 592], "id": 520, "thread": "build-14"}, {"duration": 282, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#createDevUILog", "started": "21:52:22.327", "dependents": [587, 594, 540], "id": 337, "thread": "build-43"}, {"duration": 282, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#bodyHandler", "started": "21:52:22.328", "dependents": [587, 517, 594], "id": 336, "thread": "build-46"}, {"duration": 280, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerBeans", "started": "21:52:23.321", "dependents": [479, 478, 473, 470, 492, 474, 495, 517, 472, 491, 476, 471, 484, 469, 482, 475, 509, 556, 477, 493], "id": 468, "thread": "build-29"}, {"duration": 263, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#buildStatic", "started": "21:52:22.234", "dependents": [594], "id": 315, "thread": "build-55"}, {"duration": 242, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#registerJsonRpcService", "started": "21:52:22.246", "dependents": [541, 544, 491, 594, 492, 318, 493], "id": 313, "thread": "build-59"}, {"duration": 239, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#create", "started": "21:52:22.194", "dependents": [594], "id": 279, "thread": "build-11"}, {"duration": 239, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#ioThreadDetector", "started": "21:52:22.214", "dependents": [307, 594], "id": 295, "thread": "build-31"}, {"duration": 238, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#build", "started": "21:52:27.640", "dependents": [], "id": 594, "thread": "build-63"}, {"duration": 228, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#produceNamedHttpSecurityPolicies", "started": "21:52:22.197", "dependents": [491, 594, 492, 493], "id": 269, "thread": "build-17"}, {"duration": 228, "stepId": "io.quarkus.netty.deployment.NettyProcessor#eagerlyInitClass", "started": "21:52:22.205", "dependents": [594], "id": 273, "thread": "build-4"}, {"duration": 226, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#createVertxContextHandlers", "started": "21:52:22.226", "dependents": [311, 594, 314], "id": 299, "thread": "build-49"}, {"duration": 225, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#shutdown", "started": "21:52:22.207", "dependents": [594], "id": 282, "thread": "build-16"}, {"duration": 225, "stepId": "io.quarkus.deployment.steps.BannerProcessor#recordBanner", "started": "21:52:22.327", "dependents": [594, 439], "id": 331, "thread": "build-6"}, {"duration": 224, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initFormAuth", "started": "21:52:22.211", "dependents": [441, 594, 466, 584, 585], "id": 286, "thread": "build-21"}, {"duration": 222, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#createVertxThreadFactory", "started": "21:52:22.231", "dependents": [311, 594], "id": 296, "thread": "build-37"}, {"duration": 220, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#registerAdditionalBeans", "started": "21:52:22.259", "dependents": [441, 491, 594, 492, 466, 448, 493, 498, 510], "id": 310, "thread": "build-14"}, {"duration": 219, "stepId": "io.quarkus.deployment.dev.io.NioThreadPoolDevModeProcessor#setupTCCL", "started": "21:52:22.214", "dependents": [594], "id": 276, "thread": "build-28"}, {"duration": 219, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#currentContextFactory", "started": "21:52:22.233", "dependents": [521, 594], "id": 294, "thread": "build-52"}, {"duration": 219, "stepId": "io.quarkus.elytron.security.common.deployment.QuarkusSecurityCommonProcessor#registerPasswordProvider", "started": "21:52:22.214", "dependents": [594], "id": 284, "thread": "build-2"}, {"duration": 211, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#checkForBuildTimeConfigChange", "started": "21:52:22.260", "dependents": [594], "id": 309, "thread": "build-35"}, {"duration": 207, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#jsonDefault", "started": "21:52:22.252", "dependents": [549], "id": 306, "thread": "build-67"}, {"duration": 206, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#beanDefiningAnnotations", "started": "21:52:22.253", "dependents": [308, 441, 466], "id": 305, "thread": "build-50"}, {"duration": 203, "stepId": "io.quarkus.arc.deployment.ArcProcessor#buildCompatibleExtensions", "started": "21:52:22.221", "dependents": [441, 466], "id": 268, "thread": "build-20"}, {"duration": 196, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#registerMetrics", "started": "21:52:22.243", "dependents": [594, 439], "id": 291, "thread": "build-32"}, {"duration": 195, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#createHttpAuthenticationHandler", "started": "21:52:22.258", "dependents": [594, 320, 524], "id": 302, "thread": "build-23"}, {"duration": 193, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#generateJpaConfigBean", "started": "21:52:22.327", "dependents": [491, 594, 492, 493], "id": 323, "thread": "build-27"}, {"duration": 193, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#addRoutingCtxToSecurityEventsForCdiBeans", "started": "21:52:22.259", "dependents": [594, 322], "id": 300, "thread": "build-74"}, {"duration": 192, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLoggingStaticInit", "started": "21:52:22.240", "dependents": [594], "id": 278, "thread": "build-42"}, {"duration": 190, "stepId": "io.quarkus.mutiny.deployment.MutinyProcessor#buildTimeInit", "started": "21:52:22.242", "dependents": [594], "id": 283, "thread": "build-64"}, {"duration": 189, "stepId": "io.quarkus.security.deployment.SecurityProcessor#recordBouncyCastleProviders", "started": "21:52:22.236", "dependents": [594], "id": 270, "thread": "build-56"}, {"duration": 189, "stepId": "io.quarkus.elytron.security.common.deployment.QuarkusSecurityCommonProcessor#registerPasswordProviderForNative", "started": "21:52:22.243", "dependents": [594], "id": 277, "thread": "build-45"}, {"duration": 188, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#resetMapper", "started": "21:52:22.244", "dependents": [594], "id": 280, "thread": "build-68"}, {"duration": 187, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createBuildTimeConstJsTemplate", "started": "21:52:26.165", "dependents": [579, 578], "id": 577, "thread": "build-63"}, {"duration": 185, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setupEndpoints", "started": "21:52:24.134", "dependents": [559, 550, 557, 552, 572, 592, 594, 556], "id": 549, "thread": "build-15"}, {"duration": 184, "stepId": "io.quarkus.deployment.steps.ClassPathSystemPropBuildStep#set", "started": "21:52:22.242", "dependents": [594], "id": 271, "thread": "build-10"}, {"duration": 183, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#shutdownConfigValidator", "started": "21:52:22.249", "dependents": [594], "id": 275, "thread": "build-72"}, {"duration": 179, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#generateDataSourceSupportBean", "started": "21:52:22.278", "dependents": [441, 491, 594, 492, 466, 493, 498, 510], "id": 303, "thread": "build-36"}, {"duration": 179, "stepId": "io.quarkus.deployment.steps.PreloadClassesBuildStep#preInit", "started": "21:52:22.248", "dependents": [594], "id": 272, "thread": "build-63"}, {"duration": 177, "stepId": "io.quarkus.vertx.http.deployment.ManagementInterfaceSecurityProcessor#createManagementAuthMechHandler", "started": "21:52:22.259", "dependents": [293, 594, 525], "id": 289, "thread": "build-58"}, {"duration": 177, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#replaceDefaultAuthFailureHandler", "started": "21:52:22.258", "dependents": [587, 565, 594], "id": 288, "thread": "build-3"}, {"duration": 175, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#releaseConfigOnShutdown", "started": "21:52:22.258", "dependents": [594], "id": 281, "thread": "build-80"}, {"duration": 172, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#configureLogging", "started": "21:52:22.260", "dependents": [594], "id": 274, "thread": "build-82"}, {"duration": 171, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#getSwaggerUiFinalDestination", "started": "21:52:24.147", "dependents": [580], "id": 548, "thread": "build-84"}, {"duration": 160, "stepId": "io.quarkus.agroal.deployment.AgroalMetricsProcessor#registerMetrics", "started": "21:52:22.278", "dependents": [594], "id": 290, "thread": "build-18"}, {"duration": 132, "stepId": "io.quarkus.datasource.deployment.DataSourcesExcludedFromHealthChecksProcessor#produceBean", "started": "21:52:22.328", "dependents": [491, 594, 492, 493], "id": 304, "thread": "build-57"}, {"duration": 131, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#build", "started": "21:52:22.953", "dependents": [422, 421, 441, 592, 594, 439, 466, 494], "id": 420, "thread": "build-22"}, {"duration": 127, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#buildTimeRunTimeConfig", "started": "21:52:22.241", "dependents": [592, 547], "id": 267, "thread": "build-61"}, {"duration": 125, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#cors", "started": "21:52:22.328", "dependents": [587, 565, 594], "id": 298, "thread": "build-47"}, {"duration": 125, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#eventLoopCount", "started": "21:52:22.327", "dependents": [486, 591, 594], "id": 297, "thread": "build-9"}, {"duration": 125, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initBasicAuth", "started": "21:52:22.328", "dependents": [461, 491, 594, 462, 492, 493], "id": 301, "thread": "build-87"}, {"duration": 123, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#registerDevUiHandlers", "started": "21:52:26.506", "dependents": [594, 584, 585], "id": 583, "thread": "build-71"}, {"duration": 119, "stepId": "io.quarkus.arc.deployment.ArcProcessor#validate", "started": "21:52:23.699", "dependents": [518, 504, 502, 503, 520, 499, 514, 510, 506, 505, 507, 512, 508, 500, 572, 516, 501, 509], "id": 498, "thread": "build-55"}, {"duration": 117, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createIndexHtmlTemplate", "started": "21:52:26.352", "dependents": [579], "id": 578, "thread": "build-47"}, {"duration": 115, "stepId": "io.quarkus.deployment.steps.ConfigDescriptionBuildStep#createConfigDescriptions", "started": "21:52:22.243", "dependents": [546, 543, 536], "id": 266, "thread": "build-66"}, {"duration": 113, "stepId": "io.quarkus.vertx.http.deployment.GeneratedStaticResourcesProcessor#process", "started": "21:52:22.327", "dependents": [586, 594, 584, 585], "id": 292, "thread": "build-34"}, {"duration": 111, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#defineJpaEntities", "started": "21:52:22.960", "dependents": [437, 424, 414, 413, 432, 411, 531, 412, 425, 435, 593, 429, 592, 567, 438], "id": 410, "thread": "build-15"}, {"duration": 110, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#quitCommand", "started": "21:52:22.193", "dependents": [553], "id": 259, "thread": "build-6"}, {"duration": 107, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#setMtlsCertificateRoleProperties", "started": "21:52:22.328", "dependents": [594], "id": 287, "thread": "build-84"}, {"duration": 106, "stepId": "io.quarkus.security.deployment.SecurityProcessor#recordRuntimeConfigReady", "started": "21:52:22.328", "dependents": [594], "id": 285, "thread": "build-13"}, {"duration": 92, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#helpCommand", "started": "21:52:22.211", "dependents": [553], "id": 258, "thread": "build-27"}, {"duration": 90, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#vertxIntegration", "started": "21:52:22.194", "dependents": [558, 555, 556], "id": 254, "thread": "build-13"}, {"duration": 83, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#build_2776f39a7cbf851c2510e1959c8b2b421193add9", "started": "21:52:22.482", "dependents": [333, 591, 529, 481, 492, 588, 587, 491, 594, 340, 589, 493, 585], "id": 332, "thread": "build-35"}, {"duration": 82, "stepId": "io.quarkus.vertx.http.deployment.console.ConsoleProcessor#setupConsole", "started": "21:52:22.241", "dependents": [588, 589], "id": 264, "thread": "build-43"}, {"duration": 82, "stepId": "io.quarkus.arc.deployment.BeanArchiveProcessor#build", "started": "21:52:23.106", "dependents": [458, 452, 448, 450, 507, 451, 484, 482, 466, 556, 509, 463, 549, 479, 465, 532, 456, 443, 449, 514, 447, 517, 442, 444, 454], "id": 441, "thread": "build-43"}, {"duration": 78, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLoggingRuntimeInit", "started": "21:52:23.085", "dependents": [592, 594, 440, 590], "id": 439, "thread": "build-57"}, {"duration": 74, "stepId": "io.quarkus.deployment.steps.RuntimeConfigSetupBuildStep#setupRuntimeConfig", "started": "21:52:22.252", "dependents": [486, 337, 582, 529, 485, 590, 525, 323, 524, 297, 311, 292, 565, 561, 501, 340, 594, 331, 439, 494, 434, 461, 459, 422, 432, 591, 304, 287, 587, 530, 517, 332, 490, 526, 589, 336, 488, 562, 285, 298, 420, 585, 301], "id": 265, "thread": "build-60"}, {"duration": 72, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#autoAddScope", "started": "21:52:22.209", "dependents": [448], "id": 248, "thread": "build-26"}, {"duration": 70, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateMappings", "started": "21:52:22.954", "dependents": [464, 409, 465, 502, 592, 469, 474, 510], "id": 408, "thread": "build-55"}, {"duration": 69, "stepId": "io.quarkus.caffeine.deployment.devui.CaffeineDevUIProcessor#createCard", "started": "21:52:22.205", "dependents": [573, 542], "id": 241, "thread": "build-19"}, {"duration": 66, "stepId": "io.quarkus.vertx.deployment.VertxJsonProcessor#registerJacksonSerDeser", "started": "21:52:22.248", "dependents": [404], "id": 260, "thread": "build-9"}, {"duration": 66, "stepId": "io.quarkus.security.deployment.SecurityProcessor#registerSecurityInterceptors", "started": "21:52:22.453", "dependents": [441, 491, 594, 492, 466, 493], "id": 322, "thread": "build-49"}, {"duration": 63, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#setupAuthenticationMechanisms", "started": "21:52:22.453", "dependents": [587, 461, 565, 441, 594, 462, 466], "id": 320, "thread": "build-87"}, {"duration": 60, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateBuilders", "started": "21:52:24.147", "dependents": [592], "id": 547, "thread": "build-52"}, {"duration": 60, "stepId": "io.quarkus.arc.deployment.devui.ArcDevModeApiProcessor#collectBeanInfo", "started": "21:52:23.819", "dependents": [519], "id": 518, "thread": "build-15"}, {"duration": 59, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#beanValidationAnnotations", "started": "21:52:23.189", "dependents": [517, 464, 465], "id": 463, "thread": "build-57"}, {"duration": 58, "stepId": "io.quarkus.deployment.steps.CompiledJavaVersionBuildStep#compiledJavaVersion", "started": "21:52:22.242", "dependents": [549], "id": 257, "thread": "build-57"}, {"duration": 54, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#registerConfigs", "started": "21:52:24.147", "dependents": [594], "id": 546, "thread": "build-140"}, {"duration": 50, "stepId": "io.quarkus.security.deployment.SecurityProcessor#makeSecurityAnnotationsInherited", "started": "21:52:22.220", "dependents": [466], "id": 237, "thread": "build-33"}, {"duration": 48, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#preinitializeRouter", "started": "21:52:22.566", "dependents": [491, 594, 492, 493, 585], "id": 340, "thread": "build-5"}, {"duration": 46, "stepId": "io.quarkus.arc.deployment.ArcProcessor#setupExecutor", "started": "21:52:22.483", "dependents": [594], "id": 324, "thread": "build-14"}, {"duration": 45, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#setupConfigOverride", "started": "21:52:22.194", "dependents": [], "id": 66, "thread": "build-12"}, {"duration": 45, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createBuildTimeData", "started": "21:52:26.118", "dependents": [577, 578], "id": 576, "thread": "build-47"}, {"duration": 45, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#generateCustomizer", "started": "21:52:22.954", "dependents": [441], "id": 404, "thread": "build-59"}, {"duration": 45, "stepId": "io.quarkus.netty.deployment.NettyProcessor#setNettyMachineId", "started": "21:52:22.231", "dependents": [594], "id": 244, "thread": "build-34"}, {"duration": 45, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#unremovable", "started": "21:52:22.953", "dependents": [441, 466, 498, 510], "id": 403, "thread": "build-39"}, {"duration": 44, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#testConsoleCommand", "started": "21:52:22.952", "dependents": [553], "id": 402, "thread": "build-29"}, {"duration": 44, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#createJsonRPCService", "started": "21:52:22.193", "dependents": [541, 544, 318], "id": 62, "thread": "build-7"}, {"duration": 42, "stepId": "io.quarkus.devui.deployment.ide.IdeProcessor#createOpenInIDEService", "started": "21:52:22.499", "dependents": [541, 594, 584, 585], "id": 325, "thread": "build-5"}, {"duration": 41, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#unremovableBeans", "started": "21:52:22.239", "dependents": [498, 510], "id": 249, "thread": "build-40"}, {"duration": 40, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#compositeScheduler", "started": "21:52:22.276", "dependents": [507, 441, 262, 263, 466, 483], "id": 261, "thread": "build-34"}, {"duration": 38, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#createJsonRPCService", "started": "21:52:22.196", "dependents": [541, 544, 318], "id": 56, "thread": "build-15"}, {"duration": 38, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#logging", "started": "21:52:22.246", "dependents": [255], "id": 253, "thread": "build-47"}, {"duration": 37, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerAutoSecurityFilter", "started": "21:52:23.207", "dependents": [491, 594, 492, 493], "id": 461, "thread": "build-47"}, {"duration": 37, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#registerAdditionalBeans", "started": "21:52:22.247", "dependents": [441, 592, 466], "id": 252, "thread": "build-46"}, {"duration": 37, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#serverSerializers", "started": "21:52:24.332", "dependents": [559, 592, 594], "id": 558, "thread": "build-15"}, {"duration": 37, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#addAutoFilters", "started": "21:52:23.207", "dependents": [564], "id": 462, "thread": "build-29"}, {"duration": 36, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#build", "started": "21:52:23.249", "dependents": [592, 594, 466, 522, 498, 510], "id": 465, "thread": "build-15"}, {"duration": 36, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#handleCustomAnnotatedMethods", "started": "21:52:22.978", "dependents": [441, 407, 466, 406], "id": 405, "thread": "build-57"}, {"duration": 35, "stepId": "io.quarkus.deployment.CollectionClassProcessor#setupCollectionClasses", "started": "21:52:22.209", "dependents": [592], "id": 91, "thread": "build-25"}, {"duration": 35, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#build", "started": "21:52:22.482", "dependents": [491, 594, 492, 493], "id": 319, "thread": "build-52"}, {"duration": 35, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#recordableConstructor", "started": "21:52:22.219", "dependents": [594], "id": 163, "thread": "build-35"}, {"duration": 35, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initRuntime", "started": "21:52:23.652", "dependents": [588, 530, 495, 594, 531, 589, 494], "id": 493, "thread": "build-55"}, {"duration": 35, "stepId": "io.quarkus.deployment.dev.HotDeploymentWatchedFileBuildStep#setupWatchedFileHotDeployment", "started": "21:52:23.096", "dependents": [588, 589], "id": 438, "thread": "build-29"}, {"duration": 35, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#reflection", "started": "21:52:22.208", "dependents": [592], "id": 90, "thread": "build-23"}, {"duration": 34, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#beans", "started": "21:52:22.194", "dependents": [441, 466], "id": 38, "thread": "build-10"}, {"duration": 33, "stepId": "io.quarkus.flyway.deployment.FlywayProcessor#reflection", "started": "21:52:22.952", "dependents": [557, 592], "id": 399, "thread": "build-43"}, {"duration": 32, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#fileHandling", "started": "21:52:22.215", "dependents": [558, 556], "id": 109, "thread": "build-9"}, {"duration": 32, "stepId": "io.quarkus.vertx.http.deployment.StaticResourcesProcessor#collectStaticResources", "started": "21:52:22.261", "dependents": [529], "id": 256, "thread": "build-84"}, {"duration": 32, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#build", "started": "21:52:23.619", "dependents": [588, 491, 594, 489, 492, 487, 589, 493], "id": 486, "thread": "build-57"}, {"duration": 32, "stepId": "io.quarkus.arc.deployment.ConfigStaticInitBuildSteps#transformConfigProducer", "started": "21:52:22.250", "dependents": [466], "id": 250, "thread": "build-70"}, {"duration": 31, "stepId": "io.quarkus.arc.deployment.ArcProcessor#initialize", "started": "21:52:23.286", "dependents": [518, 467, 484, 468], "id": 466, "thread": "build-29"}, {"duration": 31, "stepId": "io.quarkus.security.deployment.SecurityProcessor#registerAdditionalBeans", "started": "21:52:22.223", "dependents": [441, 466], "id": 154, "thread": "build-41"}, {"duration": 30, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setupDeployment", "started": "21:52:24.370", "dependents": [587, 560, 565, 561, 592, 594, 562, 584, 585, 563], "id": 559, "thread": "build-52"}, {"duration": 29, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#additionalBean", "started": "21:52:22.489", "dependents": [441, 466, 346], "id": 318, "thread": "build-57"}, {"duration": 28, "stepId": "io.quarkus.vertx.http.deployment.console.ConsoleProcessor#config", "started": "21:52:24.147", "dependents": [553], "id": 543, "thread": "build-12"}, {"duration": 28, "stepId": "io.quarkus.deployment.steps.ThreadPoolSetup#createExecutor", "started": "21:52:22.453", "dependents": [587, 594, 324, 332, 312, 319, 314], "id": 311, "thread": "build-47"}, {"duration": 27, "stepId": "io.quarkus.deployment.ide.IdeProcessor#detectIdeFiles", "started": "21:52:22.215", "dependents": [317], "id": 81, "thread": "build-32"}, {"duration": 26, "stepId": "io.quarkus.agroal.deployment.devui.AgroalDevUIProcessor#devUI", "started": "21:52:22.220", "dependents": [573, 544, 318, 542], "id": 107, "thread": "build-8"}, {"duration": 25, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#build_9d6b7122fb368970c50c3a870d1f672392cd8afb", "started": "21:52:22.219", "dependents": [592, 489], "id": 96, "thread": "build-38"}, {"duration": 25, "stepId": "io.quarkus.security.deployment.SecurityProcessor#gatherSecurityChecks", "started": "21:52:23.203", "dependents": [459, 517, 460, 592, 594, 466, 547, 549], "id": 458, "thread": "build-23"}, {"duration": 25, "stepId": "io.quarkus.arc.deployment.LoggingBeanSupportProcessor#discoveredComponents", "started": "21:52:22.207", "dependents": [308, 441, 466], "id": 53, "thread": "build-14"}, {"duration": 24, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#autoAddScope", "started": "21:52:22.220", "dependents": [448], "id": 92, "thread": "build-3"}, {"duration": 22, "stepId": "io.quarkus.vertx.deployment.EventBusCodecProcessor#registerCodecs", "started": "21:52:23.190", "dependents": [592, 481], "id": 456, "thread": "build-15"}, {"duration": 22, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#buildReactivePersistenceUnit", "started": "21:52:23.072", "dependents": [437, 432, 436, 487, 430, 427, 431, 435, 593, 429, 594, 428, 533, 438], "id": 425, "thread": "build-29"}, {"duration": 22, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#registerAuthMechanismSelectionInterceptor", "started": "21:52:22.952", "dependents": [379, 380, 458, 594, 378, 477], "id": 377, "thread": "build-12"}, {"duration": 21, "stepId": "io.quarkus.devservices.postgresql.deployment.PostgresqlDevServicesProcessor#setupPostgres", "started": "21:52:22.230", "dependents": [440], "id": 135, "thread": "build-51"}, {"duration": 21, "stepId": "io.quarkus.arc.deployment.CommandLineArgumentsProcessor#commandLineArgs", "started": "21:52:22.192", "dependents": [441, 491, 492, 466, 493], "id": 20, "thread": "build-3"}, {"duration": 20, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanResources", "started": "21:52:22.957", "dependents": [397, 559, 388, 532, 389, 551, 384, 449, 393, 383, 466, 385, 405, 549, 398], "id": 382, "thread": "build-23"}, {"duration": 20, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#exceptionMappers", "started": "21:52:22.263", "dependents": [407], "id": 251, "thread": "build-87"}, {"duration": 19, "stepId": "io.quarkus.vertx.http.deployment.devmode.ArcDevProcessor#registerRoutes", "started": "21:52:23.820", "dependents": [586, 594, 520, 584, 585], "id": 516, "thread": "build-36"}, {"duration": 19, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#gatherMvnpmJars", "started": "21:52:22.243", "dependents": [583, 578], "id": 216, "thread": "build-65"}, {"duration": 19, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#build_7a4403d699506d83ac39616f3c11e5e1b448d863", "started": "21:52:23.094", "dependents": [594, 522], "id": 437, "thread": "build-15"}, {"duration": 18, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#contextInjection", "started": "21:52:22.261", "dependents": [441, 445, 466, 448], "id": 247, "thread": "build-62"}, {"duration": 18, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#findAllJsonRPCMethods", "started": "21:52:24.165", "dependents": [577, 545], "id": 544, "thread": "build-150"}, {"duration": 18, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#exceptionMapper", "started": "21:52:22.256", "dependents": [592, 407], "id": 242, "thread": "build-25"}, {"duration": 18, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#mainClassBuildStep", "started": "21:52:22.956", "dependents": [572], "id": 376, "thread": "build-46"}, {"duration": 18, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#implementation", "started": "21:52:22.257", "dependents": [262, 261], "id": 245, "thread": "build-36"}, {"duration": 17, "stepId": "io.quarkus.deployment.pkg.steps.FileSystemResourcesBuildStep#notNormalMode", "started": "21:52:22.220", "dependents": [], "id": 65, "thread": "build-40"}, {"duration": 17, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#collectInterceptedStaticMethods", "started": "21:52:23.602", "dependents": [484, 528, 498, 510], "id": 482, "thread": "build-39"}, {"duration": 17, "stepId": "io.quarkus.smallrye.jwt.build.deployment.SmallRyeJwtBuildProcessor#addClassesForReflection", "started": "21:52:22.246", "dependents": [592], "id": 217, "thread": "build-69"}, {"duration": 17, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#pages", "started": "21:52:23.879", "dependents": [573, 542], "id": 519, "thread": "build-12"}, {"duration": 16, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#handleApplication", "started": "21:52:22.962", "dependents": [559, 391, 394, 532, 407, 390, 392, 387, 592, 558, 406, 395, 401, 549], "id": 386, "thread": "build-52"}, {"duration": 16, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#registerSecurityBeans", "started": "21:52:22.257", "dependents": [441, 466], "id": 240, "thread": "build-30"}, {"duration": 16, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#createJsonRpcRouter", "started": "21:52:24.184", "dependents": [594], "id": 545, "thread": "build-12"}, {"duration": 16, "stepId": "io.quarkus.security.deployment.SecurityProcessor#validateStartUpObserversNotSecured", "started": "21:52:23.820", "dependents": [520], "id": 514, "thread": "build-52"}, {"duration": 16, "stepId": "io.quarkus.devui.deployment.menu.ExtensionsProcessor#createBuildTimeActions", "started": "21:52:22.208", "dependents": [541], "id": 35, "thread": "build-24"}, {"duration": 16, "stepId": "io.quarkus.deployment.steps.ReflectiveHierarchyStep#build", "started": "21:52:24.321", "dependents": [592], "id": 557, "thread": "build-52"}, {"duration": 16, "stepId": "io.quarkus.devservices.deployment.DevServicesProcessor#config", "started": "21:52:24.147", "dependents": [553, 539, 540], "id": 538, "thread": "build-148"}, {"duration": 16, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#createJsonRPCService", "started": "21:52:22.223", "dependents": [544, 318], "id": 74, "thread": "build-46"}, {"duration": 16, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#securityExceptionMappers", "started": "21:52:22.254", "dependents": [407], "id": 238, "thread": "build-8"}, {"duration": 16, "stepId": "io.quarkus.deployment.DockerStatusProcessor#IsDockerWorking", "started": "21:52:22.193", "dependents": [538, 440], "id": 15, "thread": "build-8"}, {"duration": 15, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#build", "started": "21:52:22.263", "dependents": [422, 303, 290, 441, 592, 489, 466], "id": 246, "thread": "build-75"}, {"duration": 15, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initRegular", "started": "21:52:23.652", "dependents": [495], "id": 492, "thread": "build-50"}, {"duration": 15, "stepId": "io.quarkus.tls.CertificatesProcessor#initializeCertificate", "started": "21:52:23.620", "dependents": [587, 491, 594, 492, 493], "id": 485, "thread": "build-15"}, {"duration": 15, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#smallryeOpenApiIndex", "started": "21:52:23.191", "dependents": [461, 457, 462, 564, 455], "id": 454, "thread": "build-59"}, {"duration": 14, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#buildExclusions", "started": "21:52:22.962", "dependents": [454], "id": 381, "thread": "build-36"}, {"duration": 14, "stepId": "io.quarkus.security.deployment.SecurityProcessor#registerJCAProvidersForReflection", "started": "21:52:22.244", "dependents": [592], "id": 192, "thread": "build-18"}, {"duration": 14, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#filterMultipleVertxInstancesWarning", "started": "21:52:22.206", "dependents": [421, 439], "id": 28, "thread": "build-20"}, {"duration": 14, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#validateInterceptedMethods", "started": "21:52:23.820", "dependents": [520], "id": 512, "thread": "build-29"}, {"duration": 14, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#registerInterceptors", "started": "21:52:22.250", "dependents": [441, 466], "id": 220, "thread": "build-44"}, {"duration": 13, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerAnnotatedUserDefinedRuntimeFilters", "started": "21:52:23.207", "dependents": [491, 592, 594, 492, 493], "id": 457, "thread": "build-22"}, {"duration": 13, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#reinitializeClassesForNetty", "started": "21:52:22.228", "dependents": [489], "id": 77, "thread": "build-43"}, {"duration": 13, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#unremovableBean", "started": "21:52:22.216", "dependents": [498, 510], "id": 42, "thread": "build-34"}, {"duration": 12, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateConfigMappingsInjectionPoints", "started": "21:52:23.820", "dependents": [515, 547], "id": 510, "thread": "build-59"}, {"duration": 12, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#overrideContextInternalInterfaceToAddSafeGuards", "started": "21:52:22.194", "dependents": [572], "id": 8, "thread": "build-14"}, {"duration": 12, "stepId": "io.quarkus.arc.deployment.ConfigStaticInitBuildSteps#registerBeans", "started": "21:52:22.238", "dependents": [441, 466], "id": 130, "thread": "build-60"}, {"duration": 12, "stepId": "io.quarkus.scheduler.deployment.devui.SchedulerDevUIProcessor#rpcProvider", "started": "21:52:22.262", "dependents": [544, 318], "id": 243, "thread": "build-18"}, {"duration": 12, "stepId": "io.quarkus.smallrye.openapi.deployment.devui.OpenApiDevUIProcessor#pages", "started": "21:52:22.232", "dependents": [573, 542], "id": 98, "thread": "build-36"}, {"duration": 12, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#collectEventConsumers", "started": "21:52:23.602", "dependents": [495, 481], "id": 478, "thread": "build-57"}, {"duration": 12, "stepId": "io.quarkus.security.deployment.SecurityProcessor#transformPermissionsAllowedMetaAnnotations", "started": "21:52:23.189", "dependents": [451, 466, 452, 453], "id": 450, "thread": "build-29"}, {"duration": 12, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#transformResourceMethods", "started": "21:52:22.952", "dependents": [466], "id": 373, "thread": "build-6"}, {"duration": 12, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#collectScheduledMethods_84ea631eea52cbbcaee3e56019e68e7826861add", "started": "21:52:23.602", "dependents": [507, 483, 480], "id": 479, "thread": "build-15"}, {"duration": 11, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#generateDataSourceBeans", "started": "21:52:23.095", "dependents": [441, 491, 594, 492, 466, 493], "id": 436, "thread": "build-55"}, {"duration": 11, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#gatherAuthorizationPolicyInstances", "started": "21:52:22.957", "dependents": [375, 453], "id": 374, "thread": "build-47"}, {"duration": 11, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#additionalBeans", "started": "21:52:22.261", "dependents": [441, 466], "id": 239, "thread": "build-79"}, {"duration": 11, "stepId": "io.quarkus.deployment.steps.CombinedIndexBuildStep#build", "started": "21:52:22.940", "dependents": [544, 363, 347, 379, 413, 364, 399, 425, 348, 417, 373, 351, 469, 395, 426, 355, 453, 463, 504, 424, 386, 464, 414, 349, 456, 551, 547, 402, 377, 390, 350, 352, 557, 359, 435, 392, 570, 446, 370, 403, 405, 356, 420, 391, 353, 365, 372, 503, 407, 568, 362, 569, 354, 371, 408, 404, 387, 439, 466, 376, 465, 394, 361, 357, 474, 369, 366, 429, 374, 367, 454, 358, 401], "id": 346, "thread": "build-35"}, {"duration": 11, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#validateBeanDeployment", "started": "21:52:23.820", "dependents": [517, 520], "id": 509, "thread": "build-14"}, {"duration": 11, "stepId": "io.quarkus.vertx.http.deployment.ManagementInterfaceSecurityProcessor#setupAuthenticationMechanisms", "started": "21:52:22.437", "dependents": [587, 441, 594, 466], "id": 293, "thread": "build-3"}, {"duration": 11, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupStackTraceFormatter", "started": "21:52:22.938", "dependents": [587, 360, 439], "id": 345, "thread": "build-5"}, {"duration": 11, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#detectBasicAuthImplicitlyRequired", "started": "21:52:23.602", "dependents": [594], "id": 477, "thread": "build-24"}, {"duration": 11, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#cacheControlSupport", "started": "21:52:22.196", "dependents": [549], "id": 7, "thread": "build-16"}, {"duration": 11, "stepId": "io.quarkus.panache.hibernate.common.deployment.PanacheHibernateCommonResourceProcessor#findEntityClasses", "started": "21:52:23.072", "dependents": [568, 418], "id": 417, "thread": "build-15"}, {"duration": 11, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#transformEndpoints", "started": "21:52:23.190", "dependents": [466], "id": 449, "thread": "build-23"}, {"duration": 11, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#watchConfigFiles", "started": "21:52:22.234", "dependents": [438], "id": 101, "thread": "build-14"}, {"duration": 11, "stepId": "io.quarkus.deployment.ExtensionLoader#config", "started": "21:52:22.198", "dependents": [317, 502, 440, 588, 311, 521, 565, 451, 378, 286, 509, 533, 434, 426, 19, 192, 459, 329, 27, 433, 424, 591, 24, 39, 360, 321, 30, 189, 514, 530, 377, 517, 435, 93, 445, 564, 446, 37, 298, 420, 559, 107, 270, 458, 529, 323, 345, 452, 237, 303, 344, 457, 29, 594, 516, 331, 32, 494, 477, 376, 465, 55, 98, 154, 49, 287, 299, 510, 46, 460, 332, 336, 285, 41, 486, 363, 473, 135, 322, 337, 485, 320, 50, 450, 425, 326, 297, 72, 507, 519, 63, 51, 340, 534, 294, 334, 118, 79, 54, 453, 461, 309, 52, 308, 386, 339, 68, 462, 304, 61, 520, 80, 341, 547, 267, 145, 67, 471, 85, 589, 488, 562, 342, 585, 70, 108, 302, 78, 288, 582, 116, 246, 590, 525, 261, 524, 278, 583, 75, 292, 431, 576, 548, 408, 561, 580, 439, 466, 475, 131, 483, 549, 518, 422, 290, 432, 266, 291, 447, 587, 123, 252, 97, 289, 572, 441, 99, 490, 526, 114, 100, 117, 301], "id": 14, "thread": "build-18"}, {"duration": 11, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#unremovableBeans", "started": "21:52:22.226", "dependents": [498, 510], "id": 64, "thread": "build-48"}, {"duration": 10, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#registerBeans", "started": "21:52:23.095", "dependents": [441, 466, 498, 510], "id": 435, "thread": "build-43"}, {"duration": 10, "stepId": "io.quarkus.deployment.steps.DevModeBuildStep#watchChanges", "started": "21:52:22.229", "dependents": [438], "id": 72, "thread": "build-44"}, {"duration": 10, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#additionalBeans", "started": "21:52:22.214", "dependents": [441, 466], "id": 34, "thread": "build-30"}, {"duration": 10, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#mapPageBuildTimeData", "started": "21:52:24.164", "dependents": [577], "id": 542, "thread": "build-14"}, {"duration": 10, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#validatePersistenceUnitExtensions", "started": "21:52:23.820", "dependents": [520], "id": 508, "thread": "build-12"}, {"duration": 9, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#resolveRolesAllowedConfigExpressions", "started": "21:52:22.954", "dependents": [458, 491, 594, 501, 492, 493], "id": 372, "thread": "build-57"}, {"duration": 9, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#unremoveableSkipPredicates", "started": "21:52:22.251", "dependents": [498, 510], "id": 201, "thread": "build-62"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#vetoMPConfigProperties", "started": "21:52:22.234", "dependents": [466], "id": 89, "thread": "build-47"}, {"duration": 9, "stepId": "io.quarkus.credentials.CredentialsProcessor#unremoveable", "started": "21:52:22.211", "dependents": [498, 510], "id": 26, "thread": "build-8"}, {"duration": 9, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#setupEndpoints", "started": "21:52:24.134", "dependents": [592, 558, 555, 556], "id": 532, "thread": "build-14"}, {"duration": 9, "stepId": "io.quarkus.mutiny.deployment.MutinyProcessor#runtimeInit", "started": "21:52:22.483", "dependents": [594], "id": 314, "thread": "build-67"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerContextPropagation", "started": "21:52:22.218", "dependents": [315], "id": 39, "thread": "build-37"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateRuntimeConfigProperty", "started": "21:52:23.826", "dependents": [592, 594], "id": 513, "thread": "build-24"}, {"duration": 9, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#handler", "started": "21:52:22.950", "dependents": [594, 439], "id": 360, "thread": "build-15"}, {"duration": 9, "stepId": "io.quarkus.deployment.steps.BlockingOperationControlBuildStep#blockingOP", "started": "21:52:22.452", "dependents": [594], "id": 307, "thread": "build-52"}, {"duration": 9, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#initializeRouter", "started": "21:52:26.630", "dependents": [587, 586, 594], "id": 585, "thread": "build-63"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initStatic", "started": "21:52:23.654", "dependents": [495, 594], "id": 491, "thread": "build-15"}, {"duration": 9, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#register", "started": "21:52:22.953", "dependents": [557, 441, 592, 466], "id": 370, "thread": "build-24"}, {"duration": 9, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#indexAdditionalConstrainedClasses", "started": "21:52:23.024", "dependents": [465, 463], "id": 409, "thread": "build-59"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.ReflectiveBeanClassesProcessor#implicitReflectiveBeanClasses", "started": "21:52:23.602", "dependents": [520], "id": 476, "thread": "build-50"}, {"duration": 9, "stepId": "io.quarkus.devui.deployment.menu.EndpointsProcessor#createJsonRPCService", "started": "21:52:22.256", "dependents": [544, 318], "id": 232, "thread": "build-48"}, {"duration": 9, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#responseHeaderSupport", "started": "21:52:22.222", "dependents": [549], "id": 48, "thread": "build-36"}, {"duration": 9, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#provideSecurityInformation", "started": "21:52:22.233", "dependents": [461, 462], "id": 79, "thread": "build-53"}, {"duration": 9, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForContextResolvers", "started": "21:52:22.979", "dependents": [559, 441, 592, 466, 554], "id": 401, "thread": "build-50"}, {"duration": 9, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#frameworkRoot", "started": "21:52:22.223", "dependents": [264, 98, 552, 582, 566, 325, 189, 583, 587, 576, 565, 548, 516, 577, 73, 578, 585], "id": 49, "thread": "build-45"}, {"duration": 9, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForExceptionMappers", "started": "21:52:23.014", "dependents": [559, 441, 592, 466], "id": 407, "thread": "build-59"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#registerMonitoringComponents", "started": "21:52:22.460", "dependents": [441, 466], "id": 308, "thread": "build-57"}, {"duration": 8, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#configFiles", "started": "21:52:22.240", "dependents": [438], "id": 116, "thread": "build-44"}, {"duration": 8, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#checkMixingStacks", "started": "21:52:22.257", "dependents": [588, 589], "id": 228, "thread": "build-7"}, {"duration": 8, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#additionalProviders", "started": "21:52:24.325", "dependents": [558, 555, 556], "id": 554, "thread": "build-84"}, {"duration": 8, "stepId": "io.quarkus.security.deployment.SecurityProcessor#createSecurityCheckStorage", "started": "21:52:23.229", "dependents": [517, 491, 594, 492, 466, 493, 549], "id": 460, "thread": "build-15"}, {"duration": 8, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#asyncSupport", "started": "21:52:22.221", "dependents": [549], "id": 45, "thread": "build-42"}, {"duration": 8, "stepId": "io.quarkus.deployment.recording.substitutions.AdditionalSubstitutionsBuildStep#additionalSubstitutions", "started": "21:52:22.250", "dependents": [594], "id": 178, "thread": "build-23"}, {"duration": 8, "stepId": "io.quarkus.flyway.deployment.FlywayProcessor#build", "started": "21:52:23.088", "dependents": [592, 594, 526, 434, 438], "id": 426, "thread": "build-22"}, {"duration": 8, "stepId": "io.quarkus.deployment.steps.CapabilityAggregationStep#provideCapabilities", "started": "21:52:22.243", "dependents": [173], "id": 137, "thread": "build-58"}, {"duration": 8, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#registerVerticleClasses", "started": "21:52:22.952", "dependents": [592], "id": 364, "thread": "build-14"}, {"duration": 8, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#addAdditionalRoutes", "started": "21:52:23.831", "dependents": [587, 557, 565, 586, 592, 594, 584, 585], "id": 517, "thread": "build-12"}, {"duration": 8, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateStaticInitConfigProperty", "started": "21:52:23.826", "dependents": [592, 594], "id": 511, "thread": "build-50"}, {"duration": 8, "stepId": "io.quarkus.arc.deployment.AutoAddScopeProcessor#annotationTransformer", "started": "21:52:23.191", "dependents": [466, 498, 510], "id": 448, "thread": "build-22"}, {"duration": 8, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#validateScheduledBusinessMethods", "started": "21:52:23.820", "dependents": [520], "id": 507, "thread": "build-43"}, {"duration": 8, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#supportMixins", "started": "21:52:22.954", "dependents": [491, 592, 594, 492, 493], "id": 371, "thread": "build-67"}, {"duration": 8, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#devDbHandler", "started": "21:52:22.250", "dependents": [440], "id": 182, "thread": "build-54"}, {"duration": 8, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#finalizeRouter", "started": "21:52:26.640", "dependents": [588, 594, 590, 589], "id": 587, "thread": "build-71"}, {"duration": 8, "stepId": "io.quarkus.datasource.deployment.devservices.DevServicesDatasourceProcessor#launchDatabases", "started": "21:52:23.164", "dependents": [535, 538, 539], "id": 440, "thread": "build-29"}, {"duration": 8, "stepId": "io.quarkus.vertx.http.deployment.devmode.NotFoundProcessor#routeNotFound", "started": "21:52:26.640", "dependents": [594], "id": 586, "thread": "build-29"}, {"duration": 8, "stepId": "io.quarkus.flyway.deployment.devui.FlywayDevUIProcessor#registerJsonRpcBackend", "started": "21:52:22.262", "dependents": [544, 318], "id": 236, "thread": "build-85"}, {"duration": 8, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#defaultUnwrappedExceptions", "started": "21:52:22.246", "dependents": [407], "id": 155, "thread": "build-25"}, {"duration": 7, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#registerVerticleClasses", "started": "21:52:22.954", "dependents": [592], "id": 365, "thread": "build-52"}, {"duration": 7, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createKnownInternalImportMap", "started": "21:52:22.232", "dependents": [578], "id": 73, "thread": "build-30"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerSyntheticObservers", "started": "21:52:23.689", "dependents": [497, 496, 592, 520, 498, 510], "id": 495, "thread": "build-50"}, {"duration": 7, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#buildResourceInterceptors", "started": "21:52:23.014", "dependents": [559, 441, 466, 556, 449, 549], "id": 406, "thread": "build-39"}, {"duration": 7, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#sharedStateListener", "started": "21:52:22.260", "dependents": [339], "id": 234, "thread": "build-51"}, {"duration": 7, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#startTesting", "started": "21:52:22.606", "dependents": [588, 439, 589], "id": 339, "thread": "build-35"}, {"duration": 7, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#registerOptionalClaimProducer", "started": "21:52:23.602", "dependents": [495], "id": 473, "thread": "build-22"}, {"duration": 7, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#feature", "started": "21:52:22.226", "dependents": [594], "id": 55, "thread": "build-47"}, {"duration": 7, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#transformInjectionPoint", "started": "21:52:22.236", "dependents": [466], "id": 83, "thread": "build-54"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.WrongAnnotationUsageProcessor#detect", "started": "21:52:23.602", "dependents": [520], "id": 475, "thread": "build-52"}, {"duration": 7, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#validateBeans", "started": "21:52:23.820", "dependents": [520], "id": 506, "thread": "build-46"}, {"duration": 7, "stepId": "io.quarkus.hibernate.orm.deployment.metrics.HibernateOrmMetricsProcessor#metrics", "started": "21:52:24.138", "dependents": [594], "id": 534, "thread": "build-140"}, {"duration": 7, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#jacksonSupport", "started": "21:52:22.952", "dependents": [491, 594, 492, 493], "id": 363, "thread": "build-5"}, {"duration": 7, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#devDbHandler", "started": "21:52:22.254", "dependents": [440], "id": 212, "thread": "build-78"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#unremovableBeans", "started": "21:52:22.228", "dependents": [498, 510], "id": 59, "thread": "build-10"}, {"duration": 7, "stepId": "io.quarkus.panache.hibernate.common.deployment.PanacheHibernateCommonResourceProcessor#replaceFieldAccesses", "started": "21:52:24.516", "dependents": [572], "id": 568, "thread": "build-47"}, {"duration": 6, "stepId": "io.quarkus.devui.deployment.menu.DependenciesProcessor#createBuildTimeActions", "started": "21:52:22.242", "dependents": [541], "id": 120, "thread": "build-53"}, {"duration": 6, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#setupExceptionHandler", "started": "21:52:22.606", "dependents": [345], "id": 338, "thread": "build-6"}, {"duration": 6, "stepId": "io.quarkus.security.deployment.SecurityProcessor#prepareBouncyCastleProviders", "started": "21:52:22.244", "dependents": [592], "id": 131, "thread": "build-67"}, {"duration": 6, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#contributeQuarkusConfigToJpaModel", "started": "21:52:22.236", "dependents": [410], "id": 80, "thread": "build-10"}, {"duration": 6, "stepId": "io.quarkus.vertx.deployment.EventConsumerMethodsProcessor#eventConsumerMethods", "started": "21:52:22.237", "dependents": [446], "id": 84, "thread": "build-45"}, {"duration": 6, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#compressionSupport", "started": "21:52:22.224", "dependents": [549], "id": 46, "thread": "build-24"}, {"duration": 6, "stepId": "io.quarkus.devui.deployment.menu.DependenciesProcessor#createAppDeps", "started": "21:52:22.242", "dependents": [576], "id": 119, "thread": "build-30"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.AutoProducerMethodsProcessor#annotationTransformer", "started": "21:52:23.190", "dependents": [466], "id": 447, "thread": "build-47"}, {"duration": 6, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#build", "started": "21:52:24.518", "dependents": [571, 572], "id": 570, "thread": "build-52"}, {"duration": 6, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevServicesProcessor#devServicesAutoGenerateByDefault", "started": "21:52:24.138", "dependents": [535], "id": 533, "thread": "build-148"}, {"duration": 6, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#handler", "started": "21:52:24.404", "dependents": [586, 594, 566], "id": 565, "thread": "build-15"}, {"duration": 6, "stepId": "io.quarkus.flyway.deployment.devui.FlywayDevUIProcessor#create", "started": "21:52:23.099", "dependents": [573, 594, 542], "id": 433, "thread": "build-47"}, {"duration": 6, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#build", "started": "21:52:24.517", "dependents": [572], "id": 569, "thread": "build-15"}, {"duration": 6, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#feature", "started": "21:52:22.221", "dependents": [594], "id": 37, "thread": "build-43"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerCustomConfigBeanTypes", "started": "21:52:23.602", "dependents": [491, 592, 492, 493], "id": 472, "thread": "build-59"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigMappingsBean", "started": "21:52:23.602", "dependents": [495], "id": 474, "thread": "build-55"}, {"duration": 6, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#ensureBeanLookupAvailable", "started": "21:52:22.237", "dependents": [498, 510], "id": 86, "thread": "build-7"}, {"duration": 6, "stepId": "io.quarkus.flyway.deployment.FlywayProcessor#createBeans", "started": "21:52:23.099", "dependents": [530, 441, 491, 594, 492, 531, 466, 494, 493], "id": 434, "thread": "build-23"}, {"duration": 5, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#unlessBuildProperty", "started": "21:52:22.954", "dependents": [381, 386, 368], "id": 362, "thread": "build-49"}, {"duration": 5, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#configValidator", "started": "21:52:23.249", "dependents": [592, 547], "id": 464, "thread": "build-29"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForParamConverters_dcdfdd2a310a09abe5ee3f0ed2b2bc49f36f3d07", "started": "21:52:22.979", "dependents": [559, 441, 592, 466, 549], "id": 396, "thread": "build-52"}, {"duration": 5, "stepId": "io.quarkus.arc.deployment.HotDeploymentConfigBuildStep#startup", "started": "21:52:22.248", "dependents": [143], "id": 141, "thread": "build-38"}, {"duration": 5, "stepId": "io.quarkus.deployment.pkg.steps.JarResultBuildStep#outputTarget", "started": "21:52:22.215", "dependents": [116, 564, 345, 65], "id": 27, "thread": "build-33"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#responseStatusSupport", "started": "21:52:22.217", "dependents": [549], "id": 31, "thread": "build-36"}, {"duration": 5, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#build", "started": "21:52:23.615", "dependents": [491, 592, 594, 492, 493], "id": 483, "thread": "build-50"}, {"duration": 5, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#configureAgroalConnection", "started": "21:52:22.259", "dependents": [441, 466], "id": 226, "thread": "build-38"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForDynamicFeatures", "started": "21:52:22.978", "dependents": [559, 400], "id": 394, "thread": "build-46"}, {"duration": 5, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#ifBuildProperty", "started": "21:52:22.953", "dependents": [381, 386, 368], "id": 359, "thread": "build-87"}, {"duration": 5, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#yamlConfig", "started": "21:52:22.234", "dependents": [547], "id": 71, "thread": "build-42"}, {"duration": 5, "stepId": "io.quarkus.netty.deployment.NettyProcessor#registerEventLoopBeans", "started": "21:52:22.565", "dependents": [491, 594, 492, 493], "id": 333, "thread": "build-6"}, {"duration": 5, "stepId": "io.quarkus.vertx.http.deployment.StaticResourcesProcessor#runtimeInit", "started": "21:52:24.134", "dependents": [587, 594], "id": 529, "thread": "build-71"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveVertxWebSocketIntegrationProcessor#scanner", "started": "21:52:22.262", "dependents": [549], "id": 235, "thread": "build-76"}, {"duration": 5, "stepId": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsProcessor#devuiJsonRpcServices", "started": "21:52:22.246", "dependents": [446], "id": 133, "thread": "build-7"}, {"duration": 5, "stepId": "io.quarkus.security.deployment.SecurityProcessor#configurePermissionCheckers", "started": "21:52:23.602", "dependents": [491, 594, 492, 493], "id": 471, "thread": "build-46"}, {"duration": 5, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#registerServiceBinding", "started": "21:52:22.257", "dependents": [486, 440, 246, 425], "id": 213, "thread": "build-75"}, {"duration": 5, "stepId": "io.quarkus.devui.deployment.menu.ReadmeProcessor#createJsonRPCServiceForCache", "started": "21:52:22.209", "dependents": [544, 318], "id": 22, "thread": "build-9"}, {"duration": 5, "stepId": "io.quarkus.vertx.http.deployment.GeneratedStaticResourcesProcessor#produceResources", "started": "21:52:22.251", "dependents": [256], "id": 166, "thread": "build-75"}, {"duration": 5, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#createSynthBeansForConfiguredInjectionPoints", "started": "21:52:23.602", "dependents": [491, 594, 492, 493], "id": 470, "thread": "build-23"}, {"duration": 5, "stepId": "io.quarkus.deployment.steps.RegisterForReflectionBuildStep#build", "started": "21:52:22.957", "dependents": [557, 592], "id": 369, "thread": "build-50"}, {"duration": 5, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#configurationDescriptorBuilding", "started": "21:52:23.088", "dependents": [437, 432, 436, 487, 430, 427, 431, 435, 593, 429, 594, 428, 533, 438], "id": 424, "thread": "build-15"}, {"duration": 5, "stepId": "io.quarkus.flyway.deployment.FlywayAlwaysEnabledProcessor#build", "started": "21:52:22.221", "dependents": [594], "id": 36, "thread": "build-44"}, {"duration": 5, "stepId": "io.quarkus.deployment.steps.CapabilityAggregationStep#aggregateCapabilities", "started": "21:52:22.251", "dependents": [228, 440, 320, 425, 507, 373, 335, 455, 453, 437, 424, 591, 304, 240, 189, 213, 377, 560, 174, 557, 517, 435, 175, 564, 195, 488, 403, 420, 559, 375, 302, 288, 372, 179, 246, 407, 323, 256, 214, 303, 466, 549, 422, 465, 423, 436, 369, 310, 460, 289, 374, 300, 226], "id": 173, "thread": "build-76"}, {"duration": 5, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#create", "started": "21:52:22.214", "dependents": [573, 542], "id": 23, "thread": "build-3"}, {"duration": 5, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#build_d182d2fe7ae008890806ec353e99fa052582ee2d", "started": "21:52:23.094", "dependents": [570, 594], "id": 432, "thread": "build-39"}, {"duration": 5, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#configPropertyInjectionPoints", "started": "21:52:23.820", "dependents": [592, 511, 513], "id": 505, "thread": "build-23"}, {"duration": 5, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#createJsonRPCService", "started": "21:52:22.243", "dependents": [544, 318], "id": 121, "thread": "build-54"}, {"duration": 5, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#silenceLogging", "started": "21:52:22.253", "dependents": [255], "id": 188, "thread": "build-14"}, {"duration": 5, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#metrics", "started": "21:52:22.241", "dependents": [466], "id": 108, "thread": "build-63"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#registerCustomExceptionMappers", "started": "21:52:22.258", "dependents": [405], "id": 225, "thread": "build-81"}, {"duration": 4, "stepId": "io.quarkus.hibernate.orm.deployment.GraalVMFeatures#registerGeneratorClassesForReflections", "started": "21:52:22.262", "dependents": [592], "id": 231, "thread": "build-86"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#deprioritizeLegacyProviders", "started": "21:52:22.259", "dependents": [558], "id": 215, "thread": "build-54"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDevModeProcessor#openCommand", "started": "21:52:24.321", "dependents": [553], "id": 552, "thread": "build-12"}, {"duration": 4, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerOpenApiSchemaClassesForReflection", "started": "21:52:23.207", "dependents": [557, 592], "id": 455, "thread": "build-55"}, {"duration": 4, "stepId": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheJpaCommonResourceProcessor#lookupNamedQueries_5a86a91ed8ef1aa483288c8239df231983eeb766", "started": "21:52:23.071", "dependents": [416], "id": 413, "thread": "build-55"}, {"duration": 4, "stepId": "io.quarkus.devui.deployment.menu.ReportIssuesProcessor#registerJsonRpcService", "started": "21:52:22.250", "dependents": [544, 318], "id": 150, "thread": "build-53"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigRootsAsBeans", "started": "21:52:22.238", "dependents": [491, 492, 493], "id": 85, "thread": "build-48"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForFeatures", "started": "21:52:22.979", "dependents": [559, 400], "id": 395, "thread": "build-24"}, {"duration": 4, "stepId": "io.quarkus.security.jpa.deployment.QuarkusSecurityJpaProcessor#configureJpaAuthConfig", "started": "21:52:23.095", "dependents": [441], "id": 431, "thread": "build-46"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.SplitPackageProcessor#splitPackageDetection", "started": "21:52:22.938", "dependents": [520], "id": 344, "thread": "build-15"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#unlessBuildProfile", "started": "21:52:22.957", "dependents": [381, 386, 368], "id": 366, "thread": "build-36"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#securityContextOverrideHandler", "started": "21:52:22.261", "dependents": [559], "id": 230, "thread": "build-77"}, {"duration": 4, "stepId": "io.quarkus.deployment.steps.ReflectiveHierarchyStep#ignoreJavaClassWarnings", "started": "21:52:22.254", "dependents": [557], "id": 190, "thread": "build-51"}, {"duration": 4, "stepId": "io.quarkus.devservices.postgresql.deployment.PostgresqlDevServicesProcessor#psqlCommand", "started": "21:52:24.147", "dependents": [553], "id": 537, "thread": "build-71"}, {"duration": 4, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#additionalBean", "started": "21:52:22.250", "dependents": [441, 466], "id": 152, "thread": "build-30"}, {"duration": 4, "stepId": "io.quarkus.security.deployment.SecurityProcessor#produceJcaSecurityProviders", "started": "21:52:22.224", "dependents": [270, 131, 192], "id": 41, "thread": "build-30"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#ifBuildProfile", "started": "21:52:22.958", "dependents": [381, 386, 368], "id": 367, "thread": "build-74"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#addScope", "started": "21:52:22.251", "dependents": [448], "id": 164, "thread": "build-36"}, {"duration": 4, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#createJsonRPCService", "started": "21:52:22.254", "dependents": [544, 318], "id": 187, "thread": "build-58"}, {"duration": 4, "stepId": "io.quarkus.devui.deployment.menu.ReadmeProcessor#createReadmePage", "started": "21:52:22.251", "dependents": [576], "id": 165, "thread": "build-71"}, {"duration": 4, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#lookupNamedQueries_5a86a91ed8ef1aa483288c8239df231983eeb766", "started": "21:52:23.071", "dependents": [415], "id": 414, "thread": "build-59"}, {"duration": 4, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#runtimeOverrideConfig", "started": "21:52:22.209", "dependents": [547], "id": 18, "thread": "build-2"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.server.deployment.devui.ResteasyReactiveDevUIProcessor#createJsonRPCService", "started": "21:52:22.248", "dependents": [544, 318], "id": 138, "thread": "build-50"}, {"duration": 4, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#build", "started": "21:52:23.615", "dependents": [588, 486, 594, 485, 589], "id": 481, "thread": "build-24"}, {"duration": 3, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#setUpPersistenceProviderAndWaitForVertxPool", "started": "21:52:23.653", "dependents": [594, 534, 531], "id": 490, "thread": "build-57"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#handleJsonAnnotations", "started": "21:52:24.321", "dependents": [592, 594, 554], "id": 551, "thread": "build-15"}, {"duration": 3, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#registerBeans", "started": "21:52:23.095", "dependents": [441, 466], "id": 429, "thread": "build-47"}, {"duration": 3, "stepId": "io.quarkus.deployment.SecureRandomProcessor#registerReflectiveMethods", "started": "21:52:22.251", "dependents": [592], "id": 153, "thread": "build-74"}, {"duration": 3, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#validate", "started": "21:52:23.820", "dependents": [520], "id": 504, "thread": "build-50"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.GraalVMFeatures#registerJdbcArrayTypesForReflection", "started": "21:52:22.241", "dependents": [592], "id": 94, "thread": "build-46"}, {"duration": 3, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#registerBean", "started": "21:52:22.237", "dependents": [441, 466], "id": 76, "thread": "build-50"}, {"duration": 3, "stepId": "io.quarkus.flyway.deployment.FlywayProcessor#startActions", "started": "21:52:24.134", "dependents": [588, 530, 594, 534, 531, 589, 533], "id": 526, "thread": "build-44"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.common.deployment.JaxrsMethodsProcessor#jaxrsMethods", "started": "21:52:23.191", "dependents": [446], "id": 444, "thread": "build-46"}, {"duration": 3, "stepId": "io.quarkus.scheduler.deployment.SchedulerMethodsProcessor#schedulerMethods", "started": "21:52:22.241", "dependents": [446], "id": 95, "thread": "build-50"}, {"duration": 3, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#transactionContext", "started": "21:52:23.317", "dependents": [468], "id": 467, "thread": "build-15"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#handleInitialSql", "started": "21:52:23.096", "dependents": [433, 434], "id": 430, "thread": "build-59"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanForParameterContainers", "started": "21:52:22.979", "dependents": [393], "id": 392, "thread": "build-23"}, {"duration": 3, "stepId": "io.quarkus.security.deployment.SecurityProcessor#supportBlockingExecutionOfPermissionChecks", "started": "21:52:22.244", "dependents": [446], "id": 114, "thread": "build-3"}, {"duration": 3, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#continuousTestingState", "started": "21:52:24.134", "dependents": [594], "id": 527, "thread": "build-150"}, {"duration": 3, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesMethodsProcessor#reactiveRoutesMethods", "started": "21:52:22.254", "dependents": [446], "id": 183, "thread": "build-77"}, {"duration": 3, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#config", "started": "21:52:22.241", "dependents": [547], "id": 102, "thread": "build-62"}, {"duration": 3, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#generateAuthorizationPolicyStorage", "started": "21:52:22.969", "dependents": [458, 441], "id": 375, "thread": "build-6"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.LookupConditionsProcessor#suppressConditionsGenerators", "started": "21:52:23.190", "dependents": [466], "id": 443, "thread": "build-55"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#addScope", "started": "21:52:22.251", "dependents": [448], "id": 162, "thread": "build-7"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#unremovableBeans", "started": "21:52:22.253", "dependents": [498, 510], "id": 176, "thread": "build-38"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForInterceptors", "started": "21:52:22.978", "dependents": [406], "id": 390, "thread": "build-12"}, {"duration": 3, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#registerBean", "started": "21:52:22.256", "dependents": [441, 466], "id": 194, "thread": "build-71"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#validate", "started": "21:52:23.820", "dependents": [520], "id": 503, "thread": "build-24"}, {"duration": 3, "stepId": "io.quarkus.vertx.http.deployment.devmode.NotFoundProcessor#resourceNotFoundDataAvailable", "started": "21:52:22.262", "dependents": [441, 466], "id": 233, "thread": "build-71"}, {"duration": 3, "stepId": "io.quarkus.deployment.steps.DevServicesConfigBuildStep#deprecated", "started": "21:52:22.243", "dependents": [535], "id": 110, "thread": "build-48"}, {"duration": 3, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#shouldNotRemoveHttpServerOptionsCustomizers", "started": "21:52:22.254", "dependents": [498, 510], "id": 177, "thread": "build-3"}, {"duration": 3, "stepId": "io.quarkus.devui.deployment.welcome.WelcomeProcessor#createWelcomePages", "started": "21:52:26.114", "dependents": [576], "id": 575, "thread": "build-63"}, {"duration": 3, "stepId": "io.quarkus.security.deployment.SecurityProcessor#makePermissionCheckerClassBeansUnremovable", "started": "21:52:22.244", "dependents": [498, 510], "id": 117, "thread": "build-23"}, {"duration": 3, "stepId": "io.quarkus.netty.deployment.NettyProcessor#build", "started": "21:52:22.250", "dependents": [592, 489], "id": 145, "thread": "build-73"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#startPersistenceUnits", "started": "21:52:24.138", "dependents": [588, 594, 589], "id": 531, "thread": "build-84"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigClasses", "started": "21:52:23.832", "dependents": [594], "id": 515, "thread": "build-14"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#convertJpaResourceAnnotationsToQualifier", "started": "21:52:23.095", "dependents": [466], "id": 428, "thread": "build-23"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#callInitializer", "started": "21:52:24.134", "dependents": [594], "id": 528, "thread": "build-52"}, {"duration": 3, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#registerReflectivelyAccessedMethods", "started": "21:52:22.208", "dependents": [592], "id": 17, "thread": "build-22"}, {"duration": 3, "stepId": "io.quarkus.deployment.steps.NativeImageConfigBuildStep#build", "started": "21:52:23.652", "dependents": [594], "id": 489, "thread": "build-24"}, {"duration": 3, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#registerSwaggerUiHandler", "started": "21:52:26.506", "dependents": [594, 584, 585], "id": 582, "thread": "build-63"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.GraalVMFeatures#registerStandardStackElementTypesForReflection", "started": "21:52:22.250", "dependents": [592], "id": 142, "thread": "build-14"}, {"duration": 3, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#registerServiceBinding", "started": "21:52:22.259", "dependents": [486, 440, 246, 425], "id": 214, "thread": "build-53"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#additionalReflection", "started": "21:52:24.333", "dependents": [592], "id": 556, "thread": "build-140"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ArcProcessor#initializeContainer", "started": "21:52:24.127", "dependents": [594, 522], "id": 521, "thread": "build-84"}, {"duration": 3, "stepId": "io.quarkus.netty.deployment.NettyProcessor#cleanupMacDNSInLog", "started": "21:52:22.256", "dependents": [421, 439], "id": 191, "thread": "build-41"}, {"duration": 3, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#additionalBean", "started": "21:52:22.254", "dependents": [441, 466], "id": 180, "thread": "build-74"}, {"duration": 3, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#openSocket", "started": "21:52:26.650", "dependents": [592, 594], "id": 591, "thread": "build-29"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.AutoInjectFieldProcessor#annotationTransformer", "started": "21:52:23.191", "dependents": [466], "id": 445, "thread": "build-50"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#jpaEntitiesIndexer", "started": "21:52:22.956", "dependents": [410, 593], "id": 361, "thread": "build-27"}, {"duration": 3, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#mutinyReturnTypes", "started": "21:52:22.239", "dependents": [569, 570], "id": 87, "thread": "build-58"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanForIOInterceptors", "started": "21:52:22.978", "dependents": [406], "id": 391, "thread": "build-47"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#integrateEagerSecurity", "started": "21:52:23.203", "dependents": [549], "id": 453, "thread": "build-47"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#additionalBeans", "started": "21:52:22.983", "dependents": [441, 592, 466], "id": 400, "thread": "build-46"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#runtimeConfiguration", "started": "21:52:24.400", "dependents": [594, 562], "id": 561, "thread": "build-140"}, {"duration": 2, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#cleanupVertxWarnings", "started": "21:52:22.192", "dependents": [421, 439], "id": 1, "thread": "build-4"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ArcProcessor#loggerProducer", "started": "21:52:22.193", "dependents": [441, 466], "id": 4, "thread": "build-5"}, {"duration": 2, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#convertRoutes", "started": "21:52:24.411", "dependents": [584, 585], "id": 566, "thread": "build-47"}, {"duration": 2, "stepId": "io.quarkus.devui.deployment.menu.EndpointsProcessor#createEndpointsPage", "started": "21:52:22.257", "dependents": [576], "id": 189, "thread": "build-53"}, {"duration": 2, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#unremovableBeans", "started": "21:52:22.257", "dependents": [498, 510], "id": 202, "thread": "build-73"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.init.InitializationTaskProcessor#startApplicationInitializer", "started": "21:52:24.138", "dependents": [594], "id": 530, "thread": "build-12"}, {"duration": 2, "stepId": "io.quarkus.deployment.logging.LoggingWithPanacheProcessor#process", "started": "21:52:22.952", "dependents": [572], "id": 355, "thread": "build-46"}, {"duration": 2, "stepId": "io.quarkus.deployment.dev.IsolatedDevModeMain$AddApplicationClassPredicateBuildStep$1@746c45ac", "started": "21:52:22.192", "dependents": [517, 466, 549], "id": 3, "thread": "build-2"}, {"duration": 2, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#watchYamlConfig", "started": "21:52:22.237", "dependents": [438], "id": 69, "thread": "build-58"}, {"duration": 2, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#loadAllBuildTimeTemplates", "started": "21:52:26.470", "dependents": [583], "id": 579, "thread": "build-63"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#generateConfigProperties", "started": "21:52:22.953", "dependents": [464, 409, 465, 502, 592, 469, 474, 510], "id": 356, "thread": "build-27"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ArcProcessor#notifyBeanContainerListeners", "started": "21:52:24.130", "dependents": [594, 523], "id": 522, "thread": "build-14"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.UnremovableAnnotationsProcessor#unremovableBeans", "started": "21:52:22.205", "dependents": [498, 510], "id": 9, "thread": "build-9"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#setupPersistenceProvider", "started": "21:52:23.653", "dependents": [594, 534, 531], "id": 488, "thread": "build-22"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#handleClassLevelExceptionMappers", "started": "21:52:22.978", "dependents": [592, 549], "id": 389, "thread": "build-6"}, {"duration": 2, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#registerHttpAuthMechanismAnnotations", "started": "21:52:22.228", "dependents": [377], "id": 43, "thread": "build-50"}, {"duration": 2, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#autoAddScope", "started": "21:52:22.251", "dependents": [448], "id": 147, "thread": "build-8"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ObservabilityProcessor#preAuthFailureFilter", "started": "21:52:24.400", "dependents": [587, 565, 594, 563], "id": 560, "thread": "build-15"}, {"duration": 2, "stepId": "io.quarkus.datasource.deployment.devui.DevUIDatasourceProcessor#registerJsonRpcBackend", "started": "21:52:22.252", "dependents": [544, 318], "id": 156, "thread": "build-48"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#registerStartupObservers", "started": "21:52:23.696", "dependents": [498], "id": 497, "thread": "build-15"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#customExceptionMappers", "started": "21:52:22.256", "dependents": [405], "id": 184, "thread": "build-35"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ArcProcessor#launchMode", "started": "21:52:22.261", "dependents": [441, 466], "id": 218, "thread": "build-41"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateConfigPropertiesInjectionPoints", "started": "21:52:23.820", "dependents": [515], "id": 502, "thread": "build-57"}, {"duration": 2, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#registerScope", "started": "21:52:22.248", "dependents": [134], "id": 128, "thread": "build-8"}, {"duration": 2, "stepId": "io.quarkus.security.deployment.SecurityProcessor#authorizationController", "started": "21:52:22.216", "dependents": [441, 466], "id": 24, "thread": "build-35"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#subResourcesAsBeans", "started": "21:52:22.978", "dependents": [441, 466, 498, 510], "id": 388, "thread": "build-36"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigPropertiesBean", "started": "21:52:23.602", "dependents": [495], "id": 469, "thread": "build-43"}, {"duration": 2, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#registerHttpAuthMechanismAnnotation", "started": "21:52:22.219", "dependents": [377], "id": 30, "thread": "build-41"}, {"duration": 2, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#overrideStandardValidationFactoryResolution", "started": "21:52:22.207", "dependents": [572], "id": 16, "thread": "build-21"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#findEnablementStereotypes", "started": "21:52:22.952", "dependents": [362, 359, 367, 366], "id": 351, "thread": "build-22"}, {"duration": 1, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#addDefaultAuthFailureHandler", "started": "21:52:24.403", "dependents": [587, 565, 594], "id": 563, "thread": "build-52"}, {"duration": 1, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#processFooterLogs", "started": "21:52:24.163", "dependents": [573, 541, 542], "id": 540, "thread": "build-150"}, {"duration": 1, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#installCliCommands", "started": "21:52:24.326", "dependents": [588, 589], "id": 553, "thread": "build-15"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.deployment.ResteasyReactiveServerIntegrationProcessor#unwrappedExceptions", "started": "21:52:22.246", "dependents": [407], "id": 111, "thread": "build-50"}, {"duration": 1, "stepId": "io.quarkus.netty.deployment.NettyProcessor#registerQualifiers", "started": "21:52:22.259", "dependents": [441, 466], "id": 203, "thread": "build-79"}, {"duration": 1, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#mapDeploymentMethods", "started": "21:52:24.164", "dependents": [544, 545], "id": 541, "thread": "build-148"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#registerShutdownObservers", "started": "21:52:23.696", "dependents": [498], "id": 496, "thread": "build-55"}, {"duration": 1, "stepId": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsProcessor#check", "started": "21:52:23.195", "dependents": [], "id": 446, "thread": "build-55"}, {"duration": 1, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#unremovableBeans", "started": "21:52:22.983", "dependents": [498, 510], "id": 397, "thread": "build-23"}, {"duration": 1, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDarkeningDefault", "started": "21:52:22.262", "dependents": [547], "id": 219, "thread": "build-83"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#multitenancy", "started": "21:52:23.095", "dependents": [491, 594, 492, 493, 498, 510], "id": 427, "thread": "build-50"}, {"duration": 1, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#generateCustomProducer", "started": "21:52:22.983", "dependents": [441, 466], "id": 398, "thread": "build-47"}, {"duration": 1, "stepId": "io.quarkus.scheduler.deployment.devui.SchedulerDevUIProcessor#page", "started": "21:52:23.615", "dependents": [573, 542], "id": 480, "thread": "build-57"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.DevServicesConfigBuildStep#setup", "started": "21:52:24.145", "dependents": [588, 546, 538, 548, 543, 537, 589, 547, 536], "id": 535, "thread": "build-14"}, {"duration": 1, "stepId": "io.quarkus.netty.deployment.NettyProcessor#cleanupUnsafeLog", "started": "21:52:22.264", "dependents": [421, 439], "id": 229, "thread": "build-65"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheJpaCommonResourceProcessor#buildNamedQueryMap", "started": "21:52:23.075", "dependents": [594], "id": 416, "thread": "build-39"}, {"duration": 1, "stepId": "io.quarkus.deployment.recording.AnnotationProxyBuildStep#build", "started": "21:52:22.543", "dependents": [481, 483], "id": 328, "thread": "build-5"}, {"duration": 1, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#beans", "started": "21:52:22.317", "dependents": [441, 466], "id": 263, "thread": "build-27"}, {"duration": 1, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#brandingFiles", "started": "21:52:22.206", "dependents": [438], "id": 10, "thread": "build-21"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#registerHibernateOrmMetadataForCoreDialects", "started": "21:52:22.250", "dependents": [424, 425], "id": 129, "thread": "build-3"}, {"duration": 1, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#logCleanup", "started": "21:52:22.193", "dependents": [421, 439], "id": 2, "thread": "build-9"}, {"duration": 1, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#createContinuousTestingPages", "started": "21:52:22.219", "dependents": [576], "id": 25, "thread": "build-40"}, {"duration": 1, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#startRecoveryService", "started": "21:52:23.689", "dependents": [594], "id": 494, "thread": "build-15"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#setupVersionField", "started": "21:52:22.252", "dependents": [592], "id": 144, "thread": "build-58"}, {"duration": 1, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#httpRoot", "started": "21:52:22.240", "dependents": [587, 264, 552, 586, 566, 564, 583], "id": 78, "thread": "build-30"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ArcProcessor#exposeCustomScopeNames", "started": "21:52:22.250", "dependents": [308, 162, 441, 164, 466, 475, 448, 447], "id": 134, "thread": "build-48"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.ShutdownListenerBuildStep#setupShutdown", "started": "21:52:26.649", "dependents": [594], "id": 590, "thread": "build-63"}, {"duration": 1, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#contributeClassesToIndex", "started": "21:52:22.205", "dependents": [346], "id": 5, "thread": "build-2"}, {"duration": 1, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#registerSafeDuplicatedContextInterceptor", "started": "21:52:22.207", "dependents": [441, 466], "id": 13, "thread": "build-2"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.CurateOutcomeBuildStep#curateOutcome", "started": "21:52:22.237", "dependents": [486, 544, 228, 440, 246, 345, 425, 326, 576, 580, 542, 131, 216, 271, 433, 120, 575, 257, 137, 573, 119, 541, 93, 572, 577, 370, 173, 342], "id": 68, "thread": "build-59"}, {"duration": 1, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#doNotRemoveVertxOptionsCustomizers", "started": "21:52:22.257", "dependents": [498, 510], "id": 181, "thread": "build-79"}, {"duration": 1, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#generateDataSourceBeans", "started": "21:52:23.085", "dependents": [424, 423, 491, 594, 436, 526, 492, 531, 494, 434, 493, 426], "id": 422, "thread": "build-15"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#unknownConfigFiles", "started": "21:52:22.940", "dependents": [594], "id": 343, "thread": "build-6"}, {"duration": 1, "stepId": "io.quarkus.deployment.SslProcessor#setupNativeSsl", "started": "21:52:22.238", "dependents": [422, 303, 82, 489], "id": 70, "thread": "build-57"}, {"duration": 1, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#build", "started": "21:52:23.072", "dependents": [594], "id": 412, "thread": "build-57"}, {"duration": 1, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#providersFromClasspath", "started": "21:52:22.252", "dependents": [558, 555, 556], "id": 151, "thread": "build-3"}, {"duration": 1, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDefaultLogCleanupFilters", "started": "21:52:23.085", "dependents": [547], "id": 421, "thread": "build-39"}, {"duration": 1, "stepId": "io.quarkus.security.deployment.SecurityProcessor#gatherClassSecurityChecks", "started": "21:52:23.202", "dependents": [458], "id": 452, "thread": "build-22"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#processInterceptedStaticMethods", "started": "21:52:23.620", "dependents": [568, 569, 572, 570, 592, 567], "id": 484, "thread": "build-24"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.ApplicationInfoBuildStep#create", "started": "21:52:22.232", "dependents": [594], "id": 50, "thread": "build-53"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ArcProcessor#signalBeanContainerReady", "started": "21:52:24.133", "dependents": [559, 532, 527, 529, 531, 525, 524, 545, 587, 586, 594, 558, 526, 589, 528, 549], "id": 523, "thread": "build-84"}, {"duration": 0, "stepId": "io.quarkus.deployment.JniProcessor#setupJni", "started": "21:52:22.249", "dependents": [489], "id": 123, "thread": "build-70"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#produceEagerSecurityInterceptorStorage", "started": "21:52:22.975", "dependents": [491, 594, 492, 493], "id": 380, "thread": "build-6"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.ProfileBuildStep#defaultProfile", "started": "21:52:22.261", "dependents": [547], "id": 210, "thread": "build-85"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.CurateOutcomeBuildStep#removeResources", "started": "21:52:22.244", "dependents": [572], "id": 93, "thread": "build-69"}, {"duration": 0, "stepId": "io.quarkus.security.jpa.common.deployment.QuarkusSecurityJpaCommonProcessor#provideJpaSecurityDefinition", "started": "21:52:23.083", "dependents": [431], "id": 419, "thread": "build-15"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#ensureBeanLookupAvailable", "started": "21:52:22.261", "dependents": [498, 510], "id": 205, "thread": "build-83"}, {"duration": 0, "stepId": "io.quarkus.deployment.ide.IdeProcessor#effectiveIde", "started": "21:52:22.499", "dependents": [576, 338, 325, 345], "id": 317, "thread": "build-55"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.AdditionalClassLoaderResourcesBuildStep#appendAdditionalClassloaderResources", "started": "21:52:22.252", "dependents": [346], "id": 139, "thread": "build-51"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#candidatesForFieldAccess", "started": "21:52:23.071", "dependents": [417], "id": 411, "thread": "build-39"}, {"duration": 0, "stepId": "io.quarkus.datasource.deployment.devui.DevUIDatasourceProcessor#create", "started": "21:52:22.233", "dependents": [573, 542], "id": 54, "thread": "build-50"}, {"duration": 0, "stepId": "io.quarkus.deployment.index.ApplicationArchiveBuildStep#addConfiguredIndexedDependencies", "started": "21:52:22.244", "dependents": [342], "id": 99, "thread": "build-59"}, {"duration": 0, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setMinLevelForInitialConfigurator", "started": "21:52:22.236", "dependents": [594], "id": 63, "thread": "build-57"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#quarkusApplication", "started": "21:52:22.958", "dependents": [441, 466], "id": 358, "thread": "build-9"}, {"duration": 0, "stepId": "io.quarkus.security.jpa.deployment.QuarkusSecurityJpaProcessor#panacheEntityPredicate", "started": "21:52:23.083", "dependents": [419, 431], "id": 418, "thread": "build-39"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#marker", "started": "21:52:22.250", "dependents": [342], "id": 125, "thread": "build-62"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#produceModel", "started": "21:52:22.254", "dependents": [593, 361, 567], "id": 157, "thread": "build-30"}, {"duration": 0, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setProperty", "started": "21:52:22.264", "dependents": [594], "id": 221, "thread": "build-83"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#perClassExceptionMapperSupport", "started": "21:52:22.978", "dependents": [466], "id": 383, "thread": "build-12"}, {"duration": 0, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#enableSslInNative", "started": "21:52:22.238", "dependents": [489], "id": 67, "thread": "build-40"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#applicationSpecificUnwrappedExceptions", "started": "21:52:22.953", "dependents": [407], "id": 352, "thread": "build-49"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.AutoInjectFieldProcessor#autoInjectQualifiers", "started": "21:52:23.190", "dependents": [445, 448], "id": 442, "thread": "build-39"}, {"duration": 0, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#registerRSASigProvider", "started": "21:52:22.244", "dependents": [192], "id": 97, "thread": "build-69"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#addAllWriteableMarker", "started": "21:52:24.332", "dependents": [572], "id": 555, "thread": "build-12"}, {"duration": 0, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#missingDevUIMessageHandler", "started": "21:52:22.606", "dependents": [588, 589], "id": 335, "thread": "build-22"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.devui.ResteasyReactiveDevUIProcessor#createPages", "started": "21:52:22.260", "dependents": [573, 542], "id": 200, "thread": "build-71"}, {"duration": 0, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#feature", "started": "21:52:22.234", "dependents": [594], "id": 58, "thread": "build-45"}, {"duration": 0, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#preventLoggerContention", "started": "21:52:22.246", "dependents": [255], "id": 105, "thread": "build-14"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#additionalBeans", "started": "21:52:22.256", "dependents": [441, 466], "id": 167, "thread": "build-36"}, {"duration": 0, "stepId": "io.quarkus.deployment.ForkJoinPoolProcessor#setProperty", "started": "21:52:22.256", "dependents": [594], "id": 170, "thread": "build-53"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.StaticResourcesProcessor#indexHtmlFile", "started": "21:52:22.231", "dependents": [438], "id": 47, "thread": "build-52"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.GeneratedStaticResourcesProcessor#devMode", "started": "21:52:22.248", "dependents": [166, 292, 438], "id": 112, "thread": "build-48"}, {"duration": 0, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#produceCoroutineScope", "started": "21:52:22.256", "dependents": [441, 466], "id": 171, "thread": "build-30"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#initializeRolesAllowedConfigExp", "started": "21:52:23.820", "dependents": [594], "id": 501, "thread": "build-67"}, {"duration": 0, "stepId": "io.quarkus.flyway.deployment.FlywayAlwaysEnabledProcessor#indexFlyway", "started": "21:52:22.264", "dependents": [342], "id": 227, "thread": "build-88"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#featureBuildItem", "started": "21:52:22.264", "dependents": [594], "id": 224, "thread": "build-69"}, {"duration": 0, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#config", "started": "21:52:22.261", "dependents": [489], "id": 206, "thread": "build-73"}, {"duration": 0, "stepId": "io.quarkus.security.jpa.deployment.QuarkusSecurityJpaProcessor#feature", "started": "21:52:22.251", "dependents": [594], "id": 136, "thread": "build-76"}, {"duration": 0, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#featureAndCapability", "started": "21:52:22.246", "dependents": [594, 173], "id": 106, "thread": "build-36"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initMtlsClientAuth", "started": "21:52:22.222", "dependents": [441, 466], "id": 32, "thread": "build-45"}, {"duration": 0, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#logCleanupFilters", "started": "21:52:22.264", "dependents": [421, 439], "id": 223, "thread": "build-54"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#feature", "started": "21:52:22.259", "dependents": [594], "id": 185, "thread": "build-38"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#unremovableAsyncObserverExceptionHandlers", "started": "21:52:22.262", "dependents": [498, 510], "id": 211, "thread": "build-73"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#enrollBeanValidationTypeSafeActivatorForReflection", "started": "21:52:22.257", "dependents": [592], "id": 174, "thread": "build-73"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#aggregateParameterContainers", "started": "21:52:22.982", "dependents": [397, 449, 549, 398], "id": 393, "thread": "build-12"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ExecutorServiceProcessor#executorServiceBean", "started": "21:52:22.482", "dependents": [491, 492, 493], "id": 312, "thread": "build-57"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#buildSetup", "started": "21:52:22.259", "dependents": [594], "id": 186, "thread": "build-74"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.LifecycleEventsBuildStep#startupEvent", "started": "21:52:26.649", "dependents": [591, 594], "id": 589, "thread": "build-47"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#includeArchivesHostingEntityPackagesInIndex", "started": "21:52:22.233", "dependents": [342], "id": 51, "thread": "build-45"}, {"duration": 0, "stepId": "io.quarkus.deployment.ConstructorPropertiesProcessor#build", "started": "21:52:22.952", "dependents": [592], "id": 347, "thread": "build-6"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#featureBuildItem", "started": "21:52:22.246", "dependents": [594], "id": 104, "thread": "build-38"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#pathInterfaceImpls", "started": "21:52:22.978", "dependents": [441, 466], "id": 384, "thread": "build-46"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.ReportIssuesProcessor#createReportIssuePage", "started": "21:52:22.261", "dependents": [576], "id": 208, "thread": "build-71"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#configureHandlers", "started": "21:52:24.403", "dependents": [594], "id": 562, "thread": "build-15"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.BuildMetricsProcessor#createBuildMetricsPages", "started": "21:52:22.261", "dependents": [576], "id": 209, "thread": "build-18"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#createConfigurationPages", "started": "21:52:24.147", "dependents": [576], "id": 536, "thread": "build-150"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#feature", "started": "21:52:22.236", "dependents": [594], "id": 61, "thread": "build-45"}, {"duration": 0, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#reduceLogging", "started": "21:52:22.222", "dependents": [255], "id": 33, "thread": "build-41"}, {"duration": 0, "stepId": "io.quarkus.deployment.ExtensionLoader#booleanSupplierFactory", "started": "21:52:22.243", "dependents": [137], "id": 88, "thread": "build-7"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateLogFilterBuildStep#setupLogFilters", "started": "21:52:22.253", "dependents": [421, 439], "id": 149, "thread": "build-58"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#feature", "started": "21:52:22.228", "dependents": [594], "id": 40, "thread": "build-44"}, {"duration": 0, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#logCleanup", "started": "21:52:22.250", "dependents": [421, 439], "id": 126, "thread": "build-36"}, {"duration": 0, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#feature", "started": "21:52:22.252", "dependents": [594], "id": 140, "thread": "build-77"}, {"duration": 0, "stepId": "io.quarkus.jdbc.postgresql.deployment.PostgreSQLJDBCReflections#build", "started": "21:52:22.254", "dependents": [592], "id": 158, "thread": "build-53"}, {"duration": 0, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#transformSchedulerBeans", "started": "21:52:22.317", "dependents": [466], "id": 262, "thread": "build-9"}, {"duration": 0, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#configFile", "started": "21:52:22.213", "dependents": [438], "id": 21, "thread": "build-28"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateUserTypeProcessor#build", "started": "21:52:22.954", "dependents": [592], "id": 354, "thread": "build-55"}, {"duration": 0, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#feature", "started": "21:52:22.261", "dependents": [594], "id": 207, "thread": "build-76"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#suppressNonRuntimeConfigChanged", "started": "21:52:22.206", "dependents": [309], "id": 6, "thread": "build-2"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createRelocationMap", "started": "21:52:22.260", "dependents": [578], "id": 204, "thread": "build-77"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.ExtensionsProcessor#createExtensionsPages", "started": "21:52:26.114", "dependents": [576], "id": 574, "thread": "build-47"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#transformSecurityAnnotations", "started": "21:52:22.975", "dependents": [466], "id": 378, "thread": "build-6"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#searchForProviders", "started": "21:52:22.257", "dependents": [342], "id": 175, "thread": "build-36"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#defineTypeOfImpliedPU", "started": "21:52:23.088", "dependents": [424, 436, 428], "id": 423, "thread": "build-39"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#applicationReflection", "started": "21:52:22.248", "dependents": [592], "id": 113, "thread": "build-36"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.TestsAsBeansProcessor#testAnnotations", "started": "21:52:22.260", "dependents": [308, 441, 466], "id": 196, "thread": "build-18"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#recordEntityToPersistenceUnit", "started": "21:52:24.524", "dependents": [594], "id": 571, "thread": "build-15"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#resourceIndex", "started": "21:52:22.957", "dependents": [550, 441, 382], "id": 357, "thread": "build-47"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#produceModel", "started": "21:52:22.254", "dependents": [593, 361, 567], "id": 160, "thread": "build-25"}, {"duration": 0, "stepId": "io.quarkus.netty.deployment.NettyProcessor#limitArenaSize", "started": "21:52:22.249", "dependents": [594], "id": 118, "thread": "build-14"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#validateAsyncObserverExceptionHandlers", "started": "21:52:23.820", "dependents": [520], "id": 499, "thread": "build-22"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.ManagementInterfaceSecurityProcessor#initializeAuthMechanismHandler", "started": "21:52:24.134", "dependents": [594], "id": 525, "thread": "build-12"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setMinimalNettyMaxOrderSize", "started": "21:52:22.246", "dependents": [145, 118], "id": 103, "thread": "build-62"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.TestsAsBeansProcessor#testClassBeans", "started": "21:52:22.251", "dependents": [441, 466], "id": 132, "thread": "build-36"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.PreloadClassesBuildStep#registerPreInitClasses", "started": "21:52:22.208", "dependents": [], "id": 12, "thread": "build-9"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#collectEntityClasses", "started": "21:52:22.952", "dependents": [569], "id": 349, "thread": "build-24"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#quarkusMain", "started": "21:52:22.259", "dependents": [308, 441, 466], "id": 193, "thread": "build-77"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#createAllRoutes", "started": "21:52:26.506", "dependents": [583], "id": 581, "thread": "build-47"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#contributePersistenceXmlToJpaModel", "started": "21:52:22.549", "dependents": [410], "id": 330, "thread": "build-5"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.BannerProcessor#watchBannerChanges", "started": "21:52:22.233", "dependents": [438], "id": 52, "thread": "build-42"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#resolveConfigExpressionRoles", "started": "21:52:23.229", "dependents": [594], "id": 459, "thread": "build-22"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#produceLoggingCategories", "started": "21:52:22.244", "dependents": [255], "id": 100, "thread": "build-25"}, {"duration": 0, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#handle", "started": "21:52:22.248", "dependents": [421, 439], "id": 115, "thread": "build-14"}, {"duration": 0, "stepId": "io.quarkus.deployment.dev.ConfigureDisableInstrumentationBuildStep#configure", "started": "21:52:22.253", "dependents": [588, 589], "id": 143, "thread": "build-77"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#setUpDenyAllJaxRs", "started": "21:52:22.220", "dependents": [460], "id": 29, "thread": "build-20"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#conditionTransformer", "started": "21:52:22.962", "dependents": [466], "id": 368, "thread": "build-14"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.deployment.ResteasyReactiveServerIntegrationProcessor#unwrappedExceptions", "started": "21:52:22.254", "dependents": [407], "id": 161, "thread": "build-73"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#waitForVertxPool", "started": "21:52:23.652", "dependents": [490, 488], "id": 487, "thread": "build-52"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveLogFilter#setupLogFilters", "started": "21:52:22.207", "dependents": [421, 439], "id": 11, "thread": "build-9"}, {"duration": 0, "stepId": "io.quarkus.elytron.security.common.deployment.QuarkusSecurityCommonProcessor#services", "started": "21:52:22.253", "dependents": [592], "id": 146, "thread": "build-51"}, {"duration": 0, "stepId": "io.quarkus.deployment.pkg.steps.NativeImageBuildStep#ignoreBuildPropertyChanges", "started": "21:52:22.260", "dependents": [309], "id": 199, "thread": "build-41"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ObserverValidationProcessor#validateApplicationObserver", "started": "21:52:23.820", "dependents": [520], "id": 500, "thread": "build-6"}, {"duration": 0, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#agroal", "started": "21:52:22.260", "dependents": [594], "id": 198, "thread": "build-35"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#unremovableContextMethodParams", "started": "21:52:22.978", "dependents": [498, 510], "id": 385, "thread": "build-47"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForParamConverters_59e3169e3a646b7fcf3083416f558434b73816c5", "started": "21:52:22.979", "dependents": [396], "id": 387, "thread": "build-67"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#additionalAsyncTypeMethodScanners", "started": "21:52:22.250", "dependents": [549], "id": 127, "thread": "build-74"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#registerAnnotations", "started": "21:52:22.253", "dependents": [308, 441, 466], "id": 148, "thread": "build-77"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.HotDeploymentConfigBuildStep#configFile", "started": "21:52:22.231", "dependents": [438], "id": 44, "thread": "build-50"}, {"duration": 0, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLogFilters", "started": "21:52:22.250", "dependents": [421, 439], "id": 124, "thread": "build-71"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#additionalBeans", "started": "21:52:22.260", "dependents": [441, 466], "id": 197, "thread": "build-76"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#buildNamedQueryMap", "started": "21:52:23.075", "dependents": [594], "id": 415, "thread": "build-57"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#notFoundRoutes", "started": "21:52:26.630", "dependents": [586], "id": 584, "thread": "build-63"}, {"duration": 0, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#registerDriver", "started": "21:52:22.242", "dependents": [246], "id": 82, "thread": "build-66"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.DevServicesProcessor#createDevServicesPages", "started": "21:52:24.163", "dependents": [576], "id": 539, "thread": "build-71"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#handleFieldSecurity", "started": "21:52:24.321", "dependents": [551], "id": 550, "thread": "build-84"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#checkTransactionsSupport", "started": "21:52:22.258", "dependents": [520], "id": 179, "thread": "build-38"}, {"duration": 0, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#unremoveableBeans", "started": "21:52:22.234", "dependents": [498, 510], "id": 57, "thread": "build-15"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initializeAuthenticationHandler", "started": "21:52:24.134", "dependents": [594], "id": 524, "thread": "build-140"}, {"duration": 0, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#autoRegisterModules", "started": "21:52:22.952", "dependents": [404], "id": 348, "thread": "build-46"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#collectEntityClasses", "started": "21:52:22.953", "dependents": [570], "id": 350, "thread": "build-27"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveAlwaysEnabledProcessor#feature", "started": "21:52:22.236", "dependents": [594], "id": 60, "thread": "build-50"}, {"duration": 0, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#registerRowSetSupport", "started": "21:52:22.249", "dependents": [592], "id": 122, "thread": "build-71"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#addPersistenceUnitAnnotationToIndex", "started": "21:52:22.256", "dependents": [346], "id": 168, "thread": "build-75"}, {"duration": 0, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#filterNettyHostsFileParsingWarn", "started": "21:52:22.254", "dependents": [421, 439], "id": 159, "thread": "build-41"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#createPermissionSecurityChecksBuilder", "started": "21:52:23.202", "dependents": [458, 471], "id": 451, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.reactive.datasource.deployment.ReactiveDataSourceProcessor#addQualifierAsBean", "started": "21:52:22.256", "dependents": [441, 466], "id": 172, "thread": "build-7"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmAlwaysEnabledProcessor#featureBuildItem", "started": "21:52:22.256", "dependents": [594], "id": 169, "thread": "build-73"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ObservabilityProcessor#methodScanner", "started": "21:52:22.260", "dependents": [549], "id": 195, "thread": "build-82"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#transformAdditionalSecuredClassesToMethods", "started": "21:52:22.213", "dependents": [458, 378], "id": 19, "thread": "build-30"}, {"duration": 0, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDefaultLevels", "started": "21:52:22.284", "dependents": [439, 547], "id": 255, "thread": "build-46"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#collectInterceptedMethods", "started": "21:52:22.975", "dependents": [380, 453], "id": 379, "thread": "build-46"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#warnOfSchemaProblems", "started": "21:52:26.649", "dependents": [594], "id": 588, "thread": "build-29"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#hotDeploymentWatchedFiles", "started": "21:52:22.240", "dependents": [438], "id": 75, "thread": "build-46"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#reflections", "started": "21:52:22.264", "dependents": [592], "id": 222, "thread": "build-41"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.ReflectionDiagnosticProcessor#writeReflectionData", "started": "21:52:26.653", "dependents": [], "id": 592, "thread": "build-63"}, {"duration": 0, "stepId": "io.quarkus.caffeine.deployment.CaffeineProcessor#cacheLoaders", "started": "21:52:22.953", "dependents": [592], "id": 353, "thread": "build-57"}], "started": "2025-10-08T21:52:22.191", "items": [{"count": 1314, "class": "io.quarkus.deployment.builditem.ConfigDescriptionBuildItem"}, {"count": 877, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveClassBuildItem"}, {"count": 640, "class": "io.quarkus.deployment.builditem.GeneratedClassBuildItem"}, {"count": 569, "class": "io.quarkus.deployment.builditem.BytecodeTransformerBuildItem"}, {"count": 178, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveMethodBuildItem"}, {"count": 138, "class": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheNamedQueryEntityClassBuildStep"}, {"count": 138, "class": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheNamedQueryEntityClassBuildStep"}, {"count": 95, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveFieldBuildItem"}, {"count": 93, "class": "io.quarkus.hibernate.validator.spi.AdditionalConstrainedClassBuildItem"}, {"count": 80, "class": "io.quarkus.deployment.builditem.MainBytecodeRecorderBuildItem"}, {"count": 79, "class": "io.quarkus.arc.deployment.AdditionalBeanBuildItem"}, {"count": 55, "class": "io.quarkus.deployment.builditem.StaticBytecodeRecorderBuildItem"}, {"count": 45, "class": "io.quarkus.vertx.http.deployment.RouteBuildItem"}, {"count": 36, "class": "io.quarkus.deployment.builditem.nativeimage.RuntimeInitializedClassBuildItem"}, {"count": 36, "class": "io.quarkus.arc.deployment.SyntheticBeanBuildItem"}, {"count": 35, "class": "io.quarkus.deployment.builditem.HotDeploymentWatchedFileBuildItem"}, {"count": 34, "class": "io.quarkus.hibernate.reactive.panache.deployment.PanacheEntityClassBuildItem"}, {"count": 34, "class": "io.quarkus.deployment.builditem.ConfigClassBuildItem"}, {"count": 31, "class": "io.quarkus.arc.deployment.ConfigPropertyBuildItem"}, {"count": 28, "class": "io.quarkus.deployment.builditem.RunTimeConfigurationDefaultBuildItem"}, {"count": 26, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveHierarchyBuildItem"}, {"count": 26, "class": "io.quarkus.arc.deployment.UnremovableBeanBuildItem"}, {"count": 23, "class": "io.quarkus.deployment.builditem.CapabilityBuildItem"}, {"count": 23, "class": "io.quarkus.deployment.builditem.FeatureBuildItem"}, {"count": 20, "class": "io.quarkus.deployment.logging.LogCleanupFilterBuildItem"}, {"count": 16, "class": "io.quarkus.deployment.builditem.AdditionalIndexedClassesBuildItem"}, {"count": 14, "class": "io.quarkus.devui.spi.JsonRPCProvidersBuildItem"}, {"count": 12, "class": "io.quarkus.vertx.http.deployment.webjar.WebJarBuildItem"}, {"count": 11, "class": "io.quarkus.devui.deployment.DevUIWebJarBuildItem"}, {"count": 11, "class": "io.quarkus.devui.deployment.DevUIRoutesBuildItem"}, {"count": 11, "class": "io.quarkus.arc.deployment.AnnotationsTransformerBuildItem"}, {"count": 10, "class": "io.quarkus.deployment.builditem.SuppressNonRuntimeConfigChangedWarningBuildItem"}, {"count": 10, "class": "io.quarkus.deployment.builditem.AdditionalApplicationArchiveMarkerBuildItem"}, {"count": 9, "class": "io.quarkus.devui.spi.page.CardPageBuildItem"}, {"count": 9, "class": "io.quarkus.devui.deployment.InternalPageBuildItem"}, {"count": 8, "class": "io.quarkus.deployment.builditem.ConsoleCommandBuildItem"}, {"count": 8, "class": "io.quarkus.hibernate.orm.deployment.spi.DatabaseKindDialectBuildItem"}, {"count": 8, "class": "io.quarkus.resteasy.reactive.spi.ExceptionMapperBuildItem"}, {"count": 8, "class": "io.quarkus.deployment.builditem.nativeimage.RuntimeReinitializedClassBuildItem"}, {"count": 7, "class": "io.quarkus.resteasy.reactive.server.spi.MethodScannerBuildItem"}, {"count": 7, "class": "io.quarkus.devui.spi.buildtime.BuildTimeActionBuildItem"}, {"count": 7, "class": "io.quarkus.vertx.http.deployment.devmode.NotFoundPageDisplayableEndpointBuildItem"}, {"count": 7, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyWriterBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.builditem.SystemPropertyBuildItem"}, {"count": 6, "class": "io.quarkus.arc.deployment.BeanDefiningAnnotationBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.builditem.ServiceStartBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageSystemPropertyBuildItem"}, {"count": 6, "class": "io.quarkus.arc.deployment.AutoAddScopeBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsAllowedBuildItem"}, {"count": 5, "class": "io.quarkus.vertx.http.deployment.FilterBuildItem"}, {"count": 5, "class": "io.quarkus.arc.deployment.GeneratedBeanBuildItem"}, {"count": 5, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageResourceBuildItem"}, {"count": 5, "class": "io.quarkus.devui.deployment.BuildTimeConstBuildItem"}, {"count": 4, "class": "io.quarkus.vertx.http.deployment.HttpAuthMechanismAnnotationBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.RunTimeConfigBuilderBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageConfigBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyWriterOverrideBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyReaderBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.LogCategoryBuildItem"}, {"count": 4, "class": "io.quarkus.arc.deployment.BeanRegistrationPhaseBuildItem$BeanConfiguratorBuildItem"}, {"count": 4, "class": "io.quarkus.vertx.http.deployment.spi.RouteBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.server.spi.UnwrappedExceptionBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyReaderOverrideBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.StaticInitConfigBuilderBuildItem"}, {"count": 3, "class": "io.quarkus.jackson.spi.ClassPathJacksonModuleBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.ApplicationClassPredicateBuildItem"}, {"count": 3, "class": "io.quarkus.resteasy.reactive.spi.CustomExceptionMapperBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.ConfigMappingBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.GeneratedResourceBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.ShutdownListenerBuildItem"}, {"count": 2, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceInterceptorsContributorBuildItem"}, {"count": 2, "class": "io.quarkus.hibernate.orm.panache.deployment.PanacheEntityClassBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.ObjectSubstitutionBuildItem"}, {"count": 2, "class": "io.quarkus.devui.spi.buildtime.QuteTemplateBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.RecordableConstructorBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.ExtensionSslNativeSupportBuildItem"}, {"count": 2, "class": "io.quarkus.hibernate.orm.deployment.spi.AdditionalJpaModelBuildItem"}, {"count": 2, "class": "io.quarkus.smallrye.openapi.deployment.spi.AddToOpenAPIDefinitionBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.BytecodeRecorderObjectLoaderBuildItem"}, {"count": 2, "class": "io.quarkus.devui.spi.buildtime.StaticContentBuildItem"}, {"count": 2, "class": "io.quarkus.arc.deployment.BeanContainerListenerBuildItem"}, {"count": 2, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceConfigurationHandlerBuildItem"}, {"count": 2, "class": "io.quarkus.datasource.deployment.spi.DefaultDataSourceDbKindBuildItem"}, {"count": 2, "class": "io.quarkus.devui.spi.page.FooterPageBuildItem"}, {"count": 2, "class": "io.quarkus.hibernate.orm.deployment.PersistenceProviderSetUpBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.dev.testing.TestListenerBuildItem"}, {"count": 2, "class": "io.quarkus.devui.deployment.InternalImportMapBuildItem"}, {"count": 2, "class": "io.quarkus.arc.deployment.AutoInjectAnnotationBuildItem"}, {"count": 2, "class": "io.quarkus.hibernate.orm.panache.deployment.EntityToPersistenceUnitBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.MvnpmBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.AnnotationProxyBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.BytecodeRecorderConstantDefinitionBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.console.ConsoleInstalledBuildItem"}, {"count": 1, "class": "io.quarkus.panache.hibernate.common.deployment.HibernateModelClassCandidatesForFieldAccessBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.SynthesisFinishedBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageResourceBundleBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.core.deployment.EventLoopCountBuildItem"}, {"count": 1, "class": "io.quarkus.security.jpa.common.deployment.PanacheEntityPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.core.deployment.CoreVertxBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ContextResolversBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.DockerStatusBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveHierarchyIgnoreWarningBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.deployment.LocalCodecSelectorTypesBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.InitialRouterBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.openapi.deployment.spi.OpenApiDocumentBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.dev.ExceptionNotificationBuildItem"}, {"count": 1, "class": "io.quarkus.swaggerui.deployment.SwaggerUiBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.CompiledJavaVersionBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ValidationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.EventLoopSupplierBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.BooleanSupplierFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.validator.spi.BeanValidationAnnotationsBuildItem"}, {"count": 1, "class": "io.quarkus.tls.TlsRegistryBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ParamConverterProvidersBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.ImpliedBlockingPersistenceUnitTypeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.spi.HandlerConfigurationProviderBuildItem"}, {"count": 1, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceProviderBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.DevServicesLauncherConfigResultBuildItem"}, {"count": 1, "class": "io.quarkus.security.spi.AdditionalSecurityConstrainerEventPropsBuildItem"}, {"count": 1, "class": "io.quarkus.panache.common.deployment.PanacheEntityClassesBuildItem"}, {"count": 1, "class": "io.quarkus.panache.hibernate.common.deployment.HibernateEnhancersRegisteredBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationIndexBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ThreadFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.spi.JdbcDriverBuildItem"}, {"count": 1, "class": "io.quarkus.security.spi.PermissionsAllowedMetaAnnotationBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.logging.LoggingSetupBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InterceptorBindingRegistrarBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.spi.JdbcInitialSQLGeneratorBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ArcContainerBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.JsonRPCRuntimeMethodsBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.context.deployment.spi.ThreadContextProviderBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationClassNameBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.InitTaskCompletedBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.SecurityInformationBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.StreamingLogHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.dev.DisableInstrumentationForIndexPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.logging.LoggingDecorateBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.spi.GlobalHandlerCustomizerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CurrentContextFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ParameterContainersBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ConfigurationBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ApplicationResultBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.BodyHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildExclusionsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.LogCategoryMinLevelDefaultsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.IOThreadDetectorBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InvokerFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.SslNativeConfigBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CustomScopeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ServerDefaultProducesHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.integration.HibernateOrmIntegrationRuntimeConfiguredBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.IdeRunningProcessBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.TransformedClassesBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.EventLoopGroupBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelIndexBuildItem"}, {"count": 1, "class": "io.quarkus.security.deployment.SecurityProcessor$MethodSecurityChecks"}, {"count": 1, "class": "io.quarkus.arc.deployment.devui.ArcBeanInfoBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanDiscoveryFinishedBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.RunTimeConfigurationProxyBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceInterceptorsBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.VertxDevUILogBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildCompatibleExtensionsBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.ThemeVarsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelPersistenceUnitMappingBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.context.deployment.ContextPropagationInitializedBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ExceptionMappersBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InterceptorResolverBuildItem"}, {"count": 1, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceResultBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.IndexDependencyBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor$AdditionalConstrainedClassesIndexBuildItem"}, {"count": 1, "class": "io.quarkus.security.deployment.PermissionSecurityChecksBuilderBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanArchiveIndexBuildItem"}, {"count": 1, "class": "io.quarkus.jackson.spi.JacksonModuleBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ConsoleFormatterBannerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.SuppressConditionGeneratorBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildTimeEnabledStereotypesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationArchivesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ContextHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.TransformedAnnotationsBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveResourceMethodEntriesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.GeneratedFileSystemResourceHandledBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.OutputTargetBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.AuthorizationPolicyInstancesBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.PreBeanContainerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InjectionPointTransformerBuildItem"}, {"count": 1, "class": "io.quarkus.panache.hibernate.common.deployment.HibernateMetamodelForFieldAccessBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.webjar.WebJarResultsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelPersistenceUnitContributionBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.MinNettyAllocatorMaxOrderBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.openapi.deployment.OpenApiFilteredIndexViewBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.NonApplicationRootPathBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.VertxWebRouterBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.CombinedIndexBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.Capabilities"}, {"count": 1, "class": "io.quarkus.devui.deployment.ExtensionsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ExecutorBuildItem"}, {"count": 1, "class": "io.quarkus.security.deployment.JCAProviderBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.SetupEndpointsResultBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDeploymentInfoBuildItem"}, {"count": 1, "class": "io.quarkus.reactive.pg.client.deployment.PgPoolBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ObserverRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceScanningResultBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ServerSerialisersBuildItem"}, {"count": 1, "class": "io.quarkus.scheduler.deployment.DiscoveredImplementationsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.deployment.VertxBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDeploymentBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanContainerBuildItem"}, {"count": 1, "class": "io.quarkus.devui.spi.buildtime.FooterLogBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.EffectiveIdeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.JaxRsResourceIndexBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.CurateOutcomeBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.HttpRootPathBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.DeploymentMethodBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.steps.CapabilityAggregationStep$CapabilitiesConfiguredInDescriptorsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationStartBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.RelocationImportMapBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor$HttpAuthenticationHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.reactive.datasource.deployment.VertxPoolBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.AggregatedParameterContainersBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ContextRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.scheduler.deployment.SchedulerImplementationBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CustomScopeAnnotationsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.PersistenceUnitDescriptorBuildItem"}, {"count": 1, "class": "io.quarkus.flyway.deployment.FlywayProcessor$MigrationStateBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ContextRegistrationPhaseBuildItem$ContextConfiguratorBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.spi.JdbcDataSourceSchemaReadyBuildItem"}, {"count": 1, "class": "io.quarkus.panache.common.deployment.PanacheMethodCustomizerBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.BuiltInReaderOverrideBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CompletedApplicationClassPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationInfoBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.IdeFileBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.MainClassBuildItem"}], "itemsCount": 5125, "buildTarget": "quarkus-application"}