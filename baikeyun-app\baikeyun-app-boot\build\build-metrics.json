{"duration": 5703, "records": [{"duration": 1951, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#getAllExtensions", "started": "21:42:59.171", "dependents": [580, 581, 574, 576, 575], "id": 573, "thread": "build-59"}, {"duration": 1804, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#pregenProxies", "started": "21:43:00.912", "dependents": [594], "id": 593, "thread": "build-155"}, {"duration": 1382, "stepId": "io.quarkus.deployment.steps.ClassTransformingBuildStep#handleClassTransformation", "started": "21:42:59.531", "dependents": [593], "id": 572, "thread": "build-57"}, {"duration": 1095, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#build", "started": "21:42:58.320", "dependents": [594], "id": 563, "thread": "build-3"}, {"duration": 875, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#enhancerDomainObjects", "started": "21:42:58.649", "dependents": [572, 569, 570, 568], "id": 567, "thread": "build-41"}, {"duration": 463, "stepId": "io.quarkus.vertx.http.deployment.webjar.WebJarProcessor#processWebJarDevMode", "started": "21:43:01.123", "dependents": [581, 594, 582], "id": 580, "thread": "build-57"}, {"duration": 432, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateConfigClass", "started": "21:42:57.273", "dependents": [], "id": 340, "thread": "build-52"}, {"duration": 426, "stepId": "io.quarkus.deployment.index.ApplicationArchiveBuildStep#build", "started": "21:42:57.593", "dependents": [344, 345, 501, 572, 569, 420, 343, 377, 586, 441, 425, 356], "id": 342, "thread": "build-68"}, {"duration": 402, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#setupConsole", "started": "21:42:57.275", "dependents": [337, 440, 334, 336, 356], "id": 333, "thread": "build-42"}, {"duration": 400, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#parsePersistenceXmlDescriptors", "started": "21:42:57.268", "dependents": [420, 331, 425], "id": 330, "thread": "build-15"}, {"duration": 358, "stepId": "io.quarkus.deployment.ide.IdeProcessor#detectRunningIdeProcesses", "started": "21:42:57.255", "dependents": [326], "id": 325, "thread": "build-13"}, {"duration": 333, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#logConsoleCommand", "started": "21:42:57.298", "dependents": [553], "id": 328, "thread": "build-64"}, {"duration": 322, "stepId": "io.quarkus.deployment.steps.ApplicationIndexBuildStep#build", "started": "21:42:57.270", "dependents": [431, 478, 475, 419, 324, 466, 549, 342], "id": 322, "thread": "build-24"}, {"duration": 301, "stepId": "io.quarkus.virtual.threads.deployment.VirtualThreadsProcessor#setup", "started": "21:42:57.279", "dependents": [493, 441, 491, 594, 466, 492], "id": 316, "thread": "build-62"}, {"duration": 293, "stepId": "io.quarkus.arc.deployment.ArcProcessor#generateResources", "started": "21:42:58.845", "dependents": [521, 572, 592], "id": 520, "thread": "build-23"}, {"duration": 277, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#releaseConfigOnShutdown", "started": "21:42:57.243", "dependents": [594], "id": 285, "thread": "build-14"}, {"duration": 276, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#createDevUILog", "started": "21:42:57.426", "dependents": [540, 594, 587], "id": 339, "thread": "build-78"}, {"duration": 276, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#buildStatic", "started": "21:42:57.286", "dependents": [594], "id": 315, "thread": "build-55"}, {"duration": 276, "stepId": "io.quarkus.netty.deployment.NettyProcessor#eagerlyInitClass", "started": "21:42:57.244", "dependents": [594], "id": 287, "thread": "build-16"}, {"duration": 276, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#bodyHandler", "started": "21:42:57.426", "dependents": [594, 517, 587], "id": 338, "thread": "build-66"}, {"duration": 275, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#create", "started": "21:42:57.244", "dependents": [594], "id": 278, "thread": "build-17"}, {"duration": 272, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#shutdown", "started": "21:42:57.247", "dependents": [594], "id": 283, "thread": "build-22"}, {"duration": 272, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#resetMapper", "started": "21:42:57.248", "dependents": [594], "id": 281, "thread": "build-7"}, {"duration": 272, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#produceNamedHttpSecurityPolicies", "started": "21:42:57.243", "dependents": [493, 491, 594, 492], "id": 273, "thread": "build-12"}, {"duration": 271, "stepId": "io.quarkus.deployment.dev.io.NioThreadPoolDevModeProcessor#setupTCCL", "started": "21:42:57.248", "dependents": [594], "id": 280, "thread": "build-4"}, {"duration": 268, "stepId": "io.quarkus.deployment.steps.PreloadClassesBuildStep#preInit", "started": "21:42:57.247", "dependents": [594], "id": 274, "thread": "build-11"}, {"duration": 268, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#registerJsonRpcService", "started": "21:42:57.283", "dependents": [493, 544, 491, 594, 492, 541, 320], "id": 313, "thread": "build-67"}, {"duration": 265, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#createVertxContextHandlers", "started": "21:42:57.269", "dependents": [314, 311, 594], "id": 299, "thread": "build-29"}, {"duration": 263, "stepId": "io.quarkus.mutiny.deployment.MutinyProcessor#buildTimeInit", "started": "21:42:57.256", "dependents": [594], "id": 279, "thread": "build-30"}, {"duration": 262, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#currentContextFactory", "started": "21:42:57.273", "dependents": [521, 594], "id": 306, "thread": "build-23"}, {"duration": 261, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#ioThreadDetector", "started": "21:42:57.274", "dependents": [308, 594], "id": 301, "thread": "build-54"}, {"duration": 256, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#registerMetrics", "started": "21:42:57.265", "dependents": [439, 594], "id": 292, "thread": "build-41"}, {"duration": 256, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#shutdownConfigValidator", "started": "21:42:57.263", "dependents": [594], "id": 282, "thread": "build-39"}, {"duration": 253, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLoggingStaticInit", "started": "21:42:57.267", "dependents": [594], "id": 284, "thread": "build-44"}, {"duration": 252, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerBeans", "started": "21:42:58.374", "dependents": [479, 478, 472, 474, 495, 477, 491, 470, 517, 469, 557, 493, 484, 473, 475, 485, 492, 509, 471, 476], "id": 468, "thread": "build-37"}, {"duration": 248, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#checkForBuildTimeConfigChange", "started": "21:42:57.295", "dependents": [594], "id": 309, "thread": "build-61"}, {"duration": 247, "stepId": "io.quarkus.deployment.steps.ClassPathSystemPropBuildStep#set", "started": "21:42:57.275", "dependents": [594], "id": 291, "thread": "build-56"}, {"duration": 244, "stepId": "io.quarkus.elytron.security.common.deployment.QuarkusSecurityCommonProcessor#registerPasswordProviderForNative", "started": "21:42:57.276", "dependents": [594], "id": 277, "thread": "build-46"}, {"duration": 242, "stepId": "io.quarkus.elytron.security.common.deployment.QuarkusSecurityCommonProcessor#registerPasswordProvider", "started": "21:42:57.278", "dependents": [594], "id": 288, "thread": "build-47"}, {"duration": 239, "stepId": "io.quarkus.security.deployment.SecurityProcessor#recordBouncyCastleProviders", "started": "21:42:57.277", "dependents": [594], "id": 275, "thread": "build-60"}, {"duration": 238, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createBuildTimeConstJsTemplate", "started": "21:43:01.177", "dependents": [579, 578], "id": 577, "thread": "build-66"}, {"duration": 238, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initFormAuth", "started": "21:42:57.282", "dependents": [584, 441, 594, 466, 585], "id": 290, "thread": "build-57"}, {"duration": 238, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#createVertxThreadFactory", "started": "21:42:57.298", "dependents": [311, 594], "id": 307, "thread": "build-19"}, {"duration": 237, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#registerAdditionalBeans", "started": "21:42:57.312", "dependents": [493, 448, 441, 491, 510, 594, 466, 492, 498], "id": 310, "thread": "build-45"}, {"duration": 233, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#configureLogging", "started": "21:42:57.287", "dependents": [594], "id": 286, "thread": "build-59"}, {"duration": 227, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#createHttpAuthenticationHandler", "started": "21:42:57.308", "dependents": [317, 524, 594], "id": 300, "thread": "build-70"}, {"duration": 227, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#addRoutingCtxToSecurityEventsForCdiBeans", "started": "21:42:57.308", "dependents": [321, 594], "id": 304, "thread": "build-65"}, {"duration": 217, "stepId": "io.quarkus.vertx.http.deployment.ManagementInterfaceSecurityProcessor#createManagementAuthMechHandler", "started": "21:42:57.305", "dependents": [296, 525, 594], "id": 294, "thread": "build-51"}, {"duration": 214, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#build", "started": "21:43:02.718", "dependents": [], "id": 594, "thread": "build-66"}, {"duration": 202, "stepId": "io.quarkus.deployment.steps.BannerProcessor#recordBanner", "started": "21:42:57.426", "dependents": [439, 594], "id": 327, "thread": "build-37"}, {"duration": 202, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#replaceDefaultAuthFailureHandler", "started": "21:42:57.316", "dependents": [565, 594, 587], "id": 276, "thread": "build-83"}, {"duration": 196, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#buildTimeRunTimeConfig", "started": "21:42:57.261", "dependents": [592, 547], "id": 271, "thread": "build-34"}, {"duration": 194, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#generateDataSourceSupportBean", "started": "21:42:57.340", "dependents": [493, 441, 491, 510, 594, 466, 492, 498], "id": 297, "thread": "build-36"}, {"duration": 182, "stepId": "io.quarkus.agroal.deployment.AgroalMetricsProcessor#registerMetrics", "started": "21:42:57.340", "dependents": [594], "id": 293, "thread": "build-33"}, {"duration": 174, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setupEndpoints", "started": "21:42:59.146", "dependents": [550, 551, 572, 592, 559, 594, 557, 556], "id": 549, "thread": "build-38"}, {"duration": 174, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#quitCommand", "started": "21:42:57.249", "dependents": [553], "id": 268, "thread": "build-18"}, {"duration": 168, "stepId": "io.quarkus.deployment.steps.ConfigDescriptionBuildStep#createConfigDescriptions", "started": "21:42:57.263", "dependents": [542, 536, 545], "id": 270, "thread": "build-32"}, {"duration": 160, "stepId": "io.quarkus.arc.deployment.ArcProcessor#buildCompatibleExtensions", "started": "21:42:57.255", "dependents": [441, 466], "id": 263, "thread": "build-3"}, {"duration": 159, "stepId": "io.quarkus.deployment.steps.RuntimeConfigSetupBuildStep#setupRuntimeConfig", "started": "21:42:57.266", "dependents": [332, 561, 489, 527, 298, 439, 341, 517, 582, 305, 319, 327, 560, 591, 302, 594, 272, 486, 423, 338, 421, 430, 303, 528, 524, 462, 590, 459, 500, 339, 483, 587, 565, 311, 494, 589, 436, 488, 530, 525, 585, 295, 289], "id": 269, "thread": "build-35"}, {"duration": 158, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#beanDefiningAnnotations", "started": "21:42:57.261", "dependents": [441, 266, 466], "id": 264, "thread": "build-37"}, {"duration": 158, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#generateJpaConfigBean", "started": "21:42:57.426", "dependents": [493, 491, 594, 492], "id": 319, "thread": "build-26"}, {"duration": 151, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#getSwaggerUiFinalDestination", "started": "21:42:59.157", "dependents": [580], "id": 548, "thread": "build-33"}, {"duration": 144, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#build", "started": "21:42:58.040", "dependents": [494, 592, 422, 441, 439, 594, 466, 423], "id": 421, "thread": "build-37"}, {"duration": 143, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#jsonDefault", "started": "21:42:57.276", "dependents": [549], "id": 265, "thread": "build-26"}, {"duration": 139, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#helpCommand", "started": "21:42:57.284", "dependents": [553], "id": 267, "thread": "build-68"}, {"duration": 126, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#registerDevUiHandlers", "started": "21:43:01.587", "dependents": [584, 594, 585], "id": 583, "thread": "build-57"}, {"duration": 125, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#build_2776f39a7cbf851c2510e1959c8b2b421193add9", "started": "21:42:57.551", "dependents": [481, 491, 527, 341, 588, 335, 587, 493, 589, 591, 594, 585, 492], "id": 332, "thread": "build-61"}, {"duration": 117, "stepId": "io.quarkus.arc.deployment.ArcProcessor#validate", "started": "21:42:58.707", "dependents": [514, 504, 505, 510, 506, 508, 499, 500, 512, 501, 516, 518, 572, 507, 503, 502, 509, 520], "id": 498, "thread": "build-56"}, {"duration": 115, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#defineJpaEntities", "started": "21:42:58.050", "dependents": [430, 593, 420, 435, 438, 437, 531, 413, 425, 429, 411, 567, 592, 414, 412], "id": 410, "thread": "build-78"}, {"duration": 114, "stepId": "io.quarkus.vertx.http.deployment.console.ConsoleProcessor#setupConsole", "started": "21:42:57.290", "dependents": [589, 588], "id": 261, "thread": "build-66"}, {"duration": 109, "stepId": "io.quarkus.datasource.deployment.DataSourcesExcludedFromHealthChecksProcessor#produceBean", "started": "21:42:57.426", "dependents": [493, 491, 594, 492], "id": 298, "thread": "build-18"}, {"duration": 109, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#eventLoopCount", "started": "21:42:57.426", "dependents": [591, 594, 486], "id": 303, "thread": "build-2"}, {"duration": 109, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initBasicAuth", "started": "21:42:57.426", "dependents": [493, 462, 491, 463, 594, 492], "id": 305, "thread": "build-68"}, {"duration": 108, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#cors", "started": "21:42:57.426", "dependents": [565, 594, 587], "id": 302, "thread": "build-3"}, {"duration": 108, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createIndexHtmlTemplate", "started": "21:43:01.415", "dependents": [579], "id": 578, "thread": "build-59"}, {"duration": 107, "stepId": "io.quarkus.vertx.deployment.VertxJsonProcessor#registerJacksonSerDeser", "started": "21:42:57.303", "dependents": [404], "id": 262, "thread": "build-9"}, {"duration": 98, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#compositeScheduler", "started": "21:42:57.295", "dependents": [260, 507, 441, 466, 259, 482], "id": 258, "thread": "build-20"}, {"duration": 97, "stepId": "io.quarkus.vertx.http.deployment.GeneratedStaticResourcesProcessor#process", "started": "21:42:57.426", "dependents": [584, 586, 594, 585], "id": 295, "thread": "build-20"}, {"duration": 94, "stepId": "io.quarkus.security.deployment.SecurityProcessor#recordRuntimeConfigReady", "started": "21:42:57.426", "dependents": [594], "id": 289, "thread": "build-71"}, {"duration": 88, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#setMtlsCertificateRoleProperties", "started": "21:42:57.426", "dependents": [594], "id": 272, "thread": "build-9"}, {"duration": 85, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#vertxIntegration", "started": "21:42:57.268", "dependents": [555, 558, 557], "id": 255, "thread": "build-2"}, {"duration": 84, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#logging", "started": "21:42:57.303", "dependents": [257], "id": 256, "thread": "build-78"}, {"duration": 82, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateMappings", "started": "21:42:58.043", "dependents": [464, 409, 474, 592, 510, 469, 502, 465], "id": 408, "thread": "build-3"}, {"duration": 73, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#unremovableBeans", "started": "21:42:57.252", "dependents": [510, 498], "id": 239, "thread": "build-28"}, {"duration": 72, "stepId": "io.quarkus.arc.deployment.devui.ArcDevModeApiProcessor#collectBeanInfo", "started": "21:42:58.824", "dependents": [519], "id": 518, "thread": "build-2"}, {"duration": 71, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#registerAdditionalBeans", "started": "21:42:57.265", "dependents": [592, 441, 466], "id": 249, "thread": "build-36"}, {"duration": 70, "stepId": "io.quarkus.caffeine.deployment.devui.CaffeineDevUIProcessor#createCard", "started": "21:42:57.241", "dependents": [573, 543], "id": 221, "thread": "build-6"}, {"duration": 68, "stepId": "io.quarkus.arc.deployment.BeanArchiveProcessor#build", "started": "21:42:58.205", "dependents": [443, 447, 449, 517, 557, 484, 452, 507, 448, 485, 451, 509, 514, 450, 479, 456, 533, 454, 444, 461, 442, 458, 466, 465, 549], "id": 441, "thread": "build-3"}, {"duration": 65, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLoggingRuntimeInit", "started": "21:42:58.185", "dependents": [592, 440, 594, 590], "id": 439, "thread": "build-78"}, {"duration": 63, "stepId": "io.quarkus.deployment.CollectionClassProcessor#setupCollectionClasses", "started": "21:42:57.287", "dependents": [592], "id": 254, "thread": "build-71"}, {"duration": 59, "stepId": "io.quarkus.netty.deployment.NettyProcessor#setNettyMachineId", "started": "21:42:57.258", "dependents": [594], "id": 227, "thread": "build-25"}, {"duration": 57, "stepId": "io.quarkus.security.deployment.SecurityProcessor#makeSecurityAnnotationsInherited", "started": "21:42:57.264", "dependents": [466], "id": 235, "thread": "build-40"}, {"duration": 56, "stepId": "io.quarkus.deployment.steps.CompiledJavaVersionBuildStep#compiledJavaVersion", "started": "21:42:57.274", "dependents": [549], "id": 242, "thread": "build-53"}, {"duration": 54, "stepId": "io.quarkus.arc.deployment.ConfigStaticInitBuildSteps#transformConfigProducer", "started": "21:42:57.290", "dependents": [466], "id": 253, "thread": "build-72"}, {"duration": 53, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#generateCustomizer", "started": "21:42:58.044", "dependents": [441], "id": 404, "thread": "build-56"}, {"duration": 53, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#testConsoleCommand", "started": "21:42:58.040", "dependents": [553], "id": 402, "thread": "build-15"}, {"duration": 52, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#unremovable", "started": "21:42:58.043", "dependents": [441, 510, 466, 498], "id": 403, "thread": "build-41"}, {"duration": 52, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#createJsonRPCService", "started": "21:42:57.251", "dependents": [544, 541, 320], "id": 211, "thread": "build-10"}, {"duration": 51, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateBuilders", "started": "21:42:59.155", "dependents": [592], "id": 547, "thread": "build-74"}, {"duration": 51, "stepId": "io.quarkus.security.deployment.SecurityProcessor#registerSecurityInterceptors", "started": "21:42:57.535", "dependents": [493, 441, 491, 594, 466, 492], "id": 321, "thread": "build-2"}, {"duration": 50, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#createJsonRPCService", "started": "21:42:57.292", "dependents": [544, 541, 320], "id": 252, "thread": "build-5"}, {"duration": 49, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createBuildTimeData", "started": "21:43:01.127", "dependents": [578, 577], "id": 576, "thread": "build-59"}, {"duration": 47, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#setupAuthenticationMechanisms", "started": "21:42:57.534", "dependents": [565, 462, 441, 463, 594, 466, 587], "id": 317, "thread": "build-18"}, {"duration": 45, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#recordableConstructor", "started": "21:42:57.290", "dependents": [594], "id": 246, "thread": "build-38"}, {"duration": 45, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#serverSerializers", "started": "21:42:59.333", "dependents": [592, 559, 594], "id": 558, "thread": "build-38"}, {"duration": 44, "stepId": "io.quarkus.flyway.deployment.FlywayProcessor#reflection", "started": "21:42:58.042", "dependents": [592, 556], "id": 401, "thread": "build-70"}, {"duration": 44, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#contextInjection", "started": "21:42:57.287", "dependents": [448, 441, 446, 466], "id": 245, "thread": "build-50"}, {"duration": 44, "stepId": "io.quarkus.arc.deployment.ArcProcessor#setupExecutor", "started": "21:42:57.552", "dependents": [594], "id": 323, "thread": "build-19"}, {"duration": 43, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#exceptionMappers", "started": "21:42:57.298", "dependents": [407], "id": 251, "thread": "build-31"}, {"duration": 42, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#beanValidationAnnotations", "started": "21:42:58.274", "dependents": [464, 517, 465], "id": 461, "thread": "build-33"}, {"duration": 42, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#registerConfigs", "started": "21:42:59.154", "dependents": [594], "id": 545, "thread": "build-57"}, {"duration": 40, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#beans", "started": "21:42:57.255", "dependents": [441, 466], "id": 194, "thread": "build-19"}, {"duration": 37, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#autoAddScope", "started": "21:42:57.288", "dependents": [448], "id": 240, "thread": "build-73"}, {"duration": 36, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#fileHandling", "started": "21:42:57.301", "dependents": [558, 557], "id": 248, "thread": "build-33"}, {"duration": 34, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#handleCustomAnnotatedMethods", "started": "21:42:58.075", "dependents": [406, 441, 466, 407], "id": 405, "thread": "build-62"}, {"duration": 33, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanResources", "started": "21:42:58.042", "dependents": [396, 533, 383, 559, 449, 385, 405, 552, 391, 397, 386, 466, 549, 387, 388], "id": 380, "thread": "build-67"}, {"duration": 33, "stepId": "io.quarkus.scheduler.deployment.devui.SchedulerDevUIProcessor#rpcProvider", "started": "21:42:57.285", "dependents": [544, 320], "id": 228, "thread": "build-69"}, {"duration": 33, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#additionalBean", "started": "21:42:57.552", "dependents": [345, 441, 466], "id": 320, "thread": "build-68"}, {"duration": 32, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#registerAuthMechanismSelectionInterceptor", "started": "21:42:58.042", "dependents": [382, 478, 395, 458, 594, 384], "id": 381, "thread": "build-23"}, {"duration": 32, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setupDeployment", "started": "21:42:59.379", "dependents": [565, 584, 562, 560, 592, 561, 594, 585, 564, 587], "id": 559, "thread": "build-57"}, {"duration": 31, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#build", "started": "21:42:57.552", "dependents": [493, 491, 594, 492], "id": 318, "thread": "build-70"}, {"duration": 31, "stepId": "io.quarkus.devui.deployment.ide.IdeProcessor#createOpenInIDEService", "started": "21:42:57.622", "dependents": [584, 594, 585, 541], "id": 329, "thread": "build-13"}, {"duration": 30, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#mainClassBuildStep", "started": "21:42:58.042", "dependents": [572], "id": 377, "thread": "build-55"}, {"duration": 30, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initRuntime", "started": "21:42:58.665", "dependents": [494, 589, 495, 530, 594, 531, 588], "id": 493, "thread": "build-56"}, {"duration": 29, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#preinitializeRouter", "started": "21:42:57.677", "dependents": [493, 491, 594, 585, 492], "id": 341, "thread": "build-13"}, {"duration": 29, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#registerInterceptors", "started": "21:42:57.292", "dependents": [441, 466], "id": 236, "thread": "build-21"}, {"duration": 29, "stepId": "io.quarkus.arc.deployment.ArcProcessor#initialize", "started": "21:42:58.340", "dependents": [518, 468, 485, 467], "id": 466, "thread": "build-37"}, {"duration": 28, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerAutoSecurityFilter", "started": "21:42:58.291", "dependents": [493, 491, 594, 492], "id": 462, "thread": "build-3"}, {"duration": 28, "stepId": "io.quarkus.deployment.dev.HotDeploymentWatchedFileBuildStep#setupWatchedFileHotDeployment", "started": "21:42:58.199", "dependents": [589, 588], "id": 438, "thread": "build-15"}, {"duration": 28, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#autoAddScope", "started": "21:42:57.274", "dependents": [448], "id": 208, "thread": "build-27"}, {"duration": 28, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#addAutoFilters", "started": "21:42:58.291", "dependents": [563], "id": 463, "thread": "build-70"}, {"duration": 28, "stepId": "io.quarkus.arc.deployment.LoggingBeanSupportProcessor#discoveredComponents", "started": "21:42:57.261", "dependents": [441, 266, 466], "id": 171, "thread": "build-21"}, {"duration": 27, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#additionalBeans", "started": "21:42:57.295", "dependents": [441, 466], "id": 237, "thread": "build-48"}, {"duration": 27, "stepId": "io.quarkus.devui.deployment.menu.ExtensionsProcessor#createBuildTimeActions", "started": "21:42:57.303", "dependents": [541], "id": 244, "thread": "build-27"}, {"duration": 27, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#implementation", "started": "21:42:57.268", "dependents": [258, 259], "id": 190, "thread": "build-8"}, {"duration": 26, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigRootsAsBeans", "started": "21:42:57.272", "dependents": [493, 491, 492], "id": 198, "thread": "build-49"}, {"duration": 26, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#securityExceptionMappers", "started": "21:42:57.303", "dependents": [407], "id": 243, "thread": "build-43"}, {"duration": 24, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#build", "started": "21:42:58.640", "dependents": [493, 589, 490, 491, 594, 492, 487, 588], "id": 486, "thread": "build-70"}, {"duration": 24, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#registerSecurityBeans", "started": "21:42:57.312", "dependents": [441, 466], "id": 247, "thread": "build-63"}, {"duration": 24, "stepId": "io.quarkus.security.deployment.SecurityProcessor#registerAdditionalBeans", "started": "21:42:57.267", "dependents": [441, 466], "id": 178, "thread": "build-20"}, {"duration": 23, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupStackTraceFormatter", "started": "21:42:58.021", "dependents": [439, 373, 587], "id": 356, "thread": "build-66"}, {"duration": 23, "stepId": "io.quarkus.security.deployment.SecurityProcessor#gatherSecurityChecks", "started": "21:42:58.287", "dependents": [592, 460, 594, 517, 466, 549, 547, 459], "id": 458, "thread": "build-78"}, {"duration": 22, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#build", "started": "21:42:58.317", "dependents": [522, 592, 510, 594, 466, 498], "id": 465, "thread": "build-62"}, {"duration": 22, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#register", "started": "21:42:58.040", "dependents": [592, 441, 466, 556], "id": 371, "thread": "build-61"}, {"duration": 21, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#gatherAuthorizationPolicyInstances", "started": "21:42:58.043", "dependents": [375, 453], "id": 374, "thread": "build-33"}, {"duration": 21, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#exceptionMapper", "started": "21:42:57.246", "dependents": [592, 407], "id": 92, "thread": "build-2"}, {"duration": 21, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#handleApplication", "started": "21:42:58.052", "dependents": [533, 559, 406, 390, 379, 407, 394, 393, 592, 400, 558, 549, 392, 389], "id": 378, "thread": "build-64"}, {"duration": 21, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#build_7a4403d699506d83ac39616f3c11e5e1b448d863", "started": "21:42:58.192", "dependents": [522, 594], "id": 437, "thread": "build-70"}, {"duration": 21, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#transformResourceMethods", "started": "21:42:58.041", "dependents": [466], "id": 372, "thread": "build-42"}, {"duration": 20, "stepId": "io.quarkus.vertx.http.deployment.devmode.ArcDevProcessor#registerRoutes", "started": "21:42:58.824", "dependents": [584, 586, 594, 585, 520], "id": 516, "thread": "build-37"}, {"duration": 20, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#checkMixingStacks", "started": "21:42:57.308", "dependents": [589, 588], "id": 241, "thread": "build-80"}, {"duration": 20, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#build", "started": "21:42:57.319", "dependents": [592, 490, 441, 466, 423, 297, 293], "id": 250, "thread": "build-58"}, {"duration": 20, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#filterMultipleVertxInstancesWarning", "started": "21:42:57.301", "dependents": [422, 439], "id": 234, "thread": "build-49"}, {"duration": 20, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#addScope", "started": "21:42:57.285", "dependents": [448], "id": 217, "thread": "build-70"}, {"duration": 19, "stepId": "io.quarkus.deployment.steps.CombinedIndexBuildStep#build", "started": "21:42:58.019", "dependents": [347, 358, 504, 346, 371, 361, 429, 394, 569, 503, 360, 414, 389, 384, 556, 464, 421, 378, 456, 402, 363, 435, 372, 420, 453, 379, 413, 407, 405, 348, 349, 350, 351, 400, 570, 445, 355, 362, 401, 357, 364, 474, 377, 369, 439, 359, 381, 393, 552, 352, 353, 568, 547, 392, 408, 365, 370, 432, 390, 454, 469, 425, 417, 354, 374, 368, 461, 367, 544, 466, 403, 465, 404], "id": 345, "thread": "build-52"}, {"duration": 19, "stepId": "io.quarkus.vertx.http.deployment.console.ConsoleProcessor#config", "started": "21:42:59.155", "dependents": [553], "id": 542, "thread": "build-67"}, {"duration": 19, "stepId": "io.quarkus.vertx.deployment.EventBusCodecProcessor#registerCodecs", "started": "21:42:58.274", "dependents": [481, 592], "id": 456, "thread": "build-62"}, {"duration": 19, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#buildReactivePersistenceUnit", "started": "21:42:58.165", "dependents": [431, 430, 593, 435, 438, 434, 437, 426, 429, 532, 428, 427, 594, 487], "id": 420, "thread": "build-41"}, {"duration": 19, "stepId": "io.quarkus.agroal.deployment.devui.AgroalDevUIProcessor#devUI", "started": "21:42:57.270", "dependents": [573, 544, 543, 320], "id": 176, "thread": "build-45"}, {"duration": 18, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#resolveRolesAllowedConfigExpressions", "started": "21:42:58.043", "dependents": [493, 491, 458, 594, 492, 500], "id": 370, "thread": "build-29"}, {"duration": 18, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#registerVerticleClasses", "started": "21:42:58.042", "dependents": [592], "id": 369, "thread": "build-45"}, {"duration": 18, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#buildExclusions", "started": "21:42:58.052", "dependents": [454], "id": 376, "thread": "build-26"}, {"duration": 18, "stepId": "io.quarkus.security.deployment.SecurityProcessor#registerJCAProvidersForReflection", "started": "21:42:57.279", "dependents": [592], "id": 196, "thread": "build-9"}, {"duration": 18, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#collectInterceptedStaticMethods", "started": "21:42:58.629", "dependents": [526, 485, 510, 498], "id": 484, "thread": "build-41"}, {"duration": 18, "stepId": "io.quarkus.security.deployment.SecurityProcessor#validateStartUpObserversNotSecured", "started": "21:42:58.824", "dependents": [520], "id": 514, "thread": "build-45"}, {"duration": 18, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#devDbHandler", "started": "21:42:57.295", "dependents": [440], "id": 224, "thread": "build-76"}, {"duration": 18, "stepId": "io.quarkus.deployment.DockerStatusProcessor#IsDockerWorking", "started": "21:42:57.251", "dependents": [538, 440], "id": 103, "thread": "build-9"}, {"duration": 18, "stepId": "io.quarkus.flyway.deployment.devui.FlywayDevUIProcessor#registerJsonRpcBackend", "started": "21:42:57.298", "dependents": [544, 320], "id": 226, "thread": "build-8"}, {"duration": 17, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#setupConfigOverride", "started": "21:42:57.303", "dependents": [], "id": 232, "thread": "build-77"}, {"duration": 17, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#sharedStateListener", "started": "21:42:57.292", "dependents": [337], "id": 220, "thread": "build-63"}, {"duration": 17, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#addScope", "started": "21:42:57.285", "dependents": [448], "id": 207, "thread": "build-51"}, {"duration": 17, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#handler", "started": "21:42:58.046", "dependents": [439, 594], "id": 373, "thread": "build-2"}, {"duration": 17, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#cacheControlSupport", "started": "21:42:57.303", "dependents": [549], "id": 233, "thread": "build-75"}, {"duration": 17, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#createJsonRpcRouter", "started": "21:42:59.185", "dependents": [594], "id": 546, "thread": "build-128"}, {"duration": 17, "stepId": "io.quarkus.arc.deployment.CommandLineArgumentsProcessor#commandLineArgs", "started": "21:42:57.251", "dependents": [493, 441, 491, 466, 492], "id": 100, "thread": "build-24"}, {"duration": 16, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#registerVerticleClasses", "started": "21:42:58.043", "dependents": [592], "id": 368, "thread": "build-20"}, {"duration": 16, "stepId": "io.quarkus.smallrye.openapi.deployment.devui.OpenApiDevUIProcessor#pages", "started": "21:42:57.290", "dependents": [573, 543], "id": 218, "thread": "build-58"}, {"duration": 16, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#smallryeOpenApiIndex", "started": "21:42:58.274", "dependents": [563, 462, 463, 457, 455], "id": 454, "thread": "build-42"}, {"duration": 16, "stepId": "io.quarkus.devservices.deployment.DevServicesProcessor#config", "started": "21:42:59.154", "dependents": [540, 553, 539], "id": 538, "thread": "build-128"}, {"duration": 16, "stepId": "io.quarkus.smallrye.jwt.build.deployment.SmallRyeJwtBuildProcessor#addClassesForReflection", "started": "21:42:57.251", "dependents": [592], "id": 93, "thread": "build-27"}, {"duration": 15, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#gatherMvnpmJars", "started": "21:42:57.271", "dependents": [583, 578], "id": 163, "thread": "build-38"}, {"duration": 15, "stepId": "io.quarkus.deployment.steps.ReflectiveHierarchyStep#build", "started": "21:42:59.322", "dependents": [592], "id": 556, "thread": "build-128"}, {"duration": 15, "stepId": "io.quarkus.deployment.steps.ThreadPoolSetup#createExecutor", "started": "21:42:57.536", "dependents": [314, 332, 318, 594, 323, 312, 587], "id": 311, "thread": "build-23"}, {"duration": 14, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initRegular", "started": "21:42:58.665", "dependents": [495], "id": 492, "thread": "build-45"}, {"duration": 14, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#validateInterceptedMethods", "started": "21:42:58.824", "dependents": [520], "id": 512, "thread": "build-42"}, {"duration": 14, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#indexAdditionalConstrainedClasses", "started": "21:42:58.125", "dependents": [461, 465], "id": 409, "thread": "build-41"}, {"duration": 14, "stepId": "io.quarkus.devservices.postgresql.deployment.PostgresqlDevServicesProcessor#setupPostgres", "started": "21:42:57.275", "dependents": [440], "id": 169, "thread": "build-33"}, {"duration": 13, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#configureAgroalConnection", "started": "21:42:57.305", "dependents": [441, 466], "id": 229, "thread": "build-10"}, {"duration": 13, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#overrideStandardValidationFactoryResolution", "started": "21:42:57.259", "dependents": [572], "id": 111, "thread": "build-5"}, {"duration": 12, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerAnnotatedUserDefinedRuntimeFilters", "started": "21:42:58.291", "dependents": [493, 592, 491, 594, 492], "id": 457, "thread": "build-37"}, {"duration": 12, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#unremovableBeans", "started": "21:42:57.281", "dependents": [510, 498], "id": 185, "thread": "build-65"}, {"duration": 12, "stepId": "io.quarkus.vertx.http.deployment.StaticResourcesProcessor#collectStaticResources", "started": "21:42:57.310", "dependents": [527], "id": 238, "thread": "build-81"}, {"duration": 12, "stepId": "io.quarkus.arc.deployment.ConfigStaticInitBuildSteps#registerBeans", "started": "21:42:57.281", "dependents": [441, 466], "id": 187, "thread": "build-61"}, {"duration": 12, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#collectScheduledMethods_84ea631eea52cbbcaee3e56019e68e7826861add", "started": "21:42:58.627", "dependents": [507, 480, 482], "id": 479, "thread": "build-62"}, {"duration": 11, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#unlessBuildProperty", "started": "21:42:58.040", "dependents": [366, 378, 376], "id": 361, "thread": "build-68"}, {"duration": 11, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateConfigMappingsInjectionPoints", "started": "21:42:58.824", "dependents": [515, 547], "id": 510, "thread": "build-29"}, {"duration": 11, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#registerBeans", "started": "21:42:58.192", "dependents": [441, 510, 466, 498], "id": 435, "thread": "build-3"}, {"duration": 11, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#findAllJsonRPCMethods", "started": "21:42:59.173", "dependents": [546, 577], "id": 544, "thread": "build-23"}, {"duration": 11, "stepId": "io.quarkus.flyway.deployment.FlywayProcessor#build", "started": "21:42:58.188", "dependents": [436, 528, 438, 592, 594], "id": 432, "thread": "build-41"}, {"duration": 11, "stepId": "io.quarkus.panache.hibernate.common.deployment.PanacheHibernateCommonResourceProcessor#findEntityClasses", "started": "21:42:58.166", "dependents": [569, 418], "id": 417, "thread": "build-78"}, {"duration": 11, "stepId": "io.quarkus.security.deployment.SecurityProcessor#transformPermissionsAllowedMetaAnnotations", "started": "21:42:58.274", "dependents": [452, 453, 451, 466], "id": 450, "thread": "build-37"}, {"duration": 11, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#jacksonSupport", "started": "21:42:58.043", "dependents": [493, 491, 594, 492], "id": 367, "thread": "build-51"}, {"duration": 11, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#reflection", "started": "21:42:57.255", "dependents": [592], "id": 89, "thread": "build-20"}, {"duration": 10, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#generateDataSourceBeans", "started": "21:42:58.192", "dependents": [493, 441, 491, 594, 466, 492], "id": 434, "thread": "build-62"}, {"duration": 10, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#validateBeanDeployment", "started": "21:42:58.825", "dependents": [517, 520], "id": 509, "thread": "build-23"}, {"duration": 10, "stepId": "io.quarkus.deployment.steps.CapabilityAggregationStep#provideCapabilities", "started": "21:42:57.271", "dependents": [213], "id": 142, "thread": "build-43"}, {"duration": 10, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#ifBuildProperty", "started": "21:42:58.040", "dependents": [366, 378, 376], "id": 363, "thread": "build-64"}, {"duration": 10, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#addAdditionalRoutes", "started": "21:42:58.835", "dependents": [565, 584, 586, 592, 594, 585, 556, 587], "id": 517, "thread": "build-78"}, {"duration": 10, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#generateConfigProperties", "started": "21:42:58.039", "dependents": [464, 409, 474, 592, 510, 469, 502, 465], "id": 358, "thread": "build-78"}, {"duration": 10, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#detectBasicAuthImplicitlyRequired", "started": "21:42:58.627", "dependents": [594], "id": 478, "thread": "build-70"}, {"duration": 10, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#autoAddScope", "started": "21:42:57.295", "dependents": [448], "id": 212, "thread": "build-45"}, {"duration": 10, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initStatic", "started": "21:42:58.665", "dependents": [495, 594], "id": 491, "thread": "build-62"}, {"duration": 10, "stepId": "io.quarkus.arc.deployment.UnremovableAnnotationsProcessor#unremovableBeans", "started": "21:42:57.304", "dependents": [510, 498], "id": 223, "thread": "build-79"}, {"duration": 10, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#transformEndpoints", "started": "21:42:58.274", "dependents": [466], "id": 449, "thread": "build-70"}, {"duration": 10, "stepId": "io.quarkus.deployment.ide.IdeProcessor#detectIdeFiles", "started": "21:42:57.240", "dependents": [326], "id": 27, "thread": "build-3"}, {"duration": 10, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#unremovableBean", "started": "21:42:57.241", "dependents": [510, 498], "id": 30, "thread": "build-8"}, {"duration": 10, "stepId": "io.quarkus.deployment.steps.CapabilityAggregationStep#aggregateCapabilities", "started": "21:42:57.295", "dependents": [434, 214, 229, 319, 440, 423, 294, 455, 556, 421, 375, 435, 372, 420, 317, 453, 407, 297, 562, 215, 334, 549, 362, 238, 225, 300, 437, 559, 298, 517, 241, 381, 250, 304, 219, 591, 231, 507, 222, 424, 460, 247, 563, 370, 310, 276, 425, 374, 488, 466, 403, 465, 230], "id": 213, "thread": "build-74"}, {"duration": 10, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForContextResolvers", "started": "21:42:58.074", "dependents": [592, 559, 554, 441, 466], "id": 400, "thread": "build-42"}, {"duration": 10, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#mapPageBuildTimeData", "started": "21:42:59.171", "dependents": [577], "id": 543, "thread": "build-128"}, {"duration": 10, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#validatePersistenceUnitExtensions", "started": "21:42:58.824", "dependents": [520], "id": 508, "thread": "build-78"}, {"duration": 9, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#configFiles", "started": "21:42:57.281", "dependents": [438], "id": 173, "thread": "build-63"}, {"duration": 9, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#responseStatusSupport", "started": "21:42:57.292", "dependents": [549], "id": 205, "thread": "build-43"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#unlessBuildProfile", "started": "21:42:58.042", "dependents": [366, 378, 376], "id": 364, "thread": "build-62"}, {"duration": 9, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#startTesting", "started": "21:42:57.677", "dependents": [589, 439, 588], "id": 337, "thread": "build-61"}, {"duration": 9, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#validateScheduledBusinessMethods", "started": "21:42:58.824", "dependents": [520], "id": 507, "thread": "build-61"}, {"duration": 9, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#additionalBean", "started": "21:42:57.287", "dependents": [441, 466], "id": 195, "thread": "build-64"}, {"duration": 9, "stepId": "io.quarkus.deployment.steps.RegisterForReflectionBuildStep#build", "started": "21:42:58.042", "dependents": [592, 556], "id": 362, "thread": "build-26"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#ifBuildProfile", "started": "21:42:58.043", "dependents": [366, 378, 376], "id": 365, "thread": "build-54"}, {"duration": 9, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#finalizeRouter", "started": "21:43:01.716", "dependents": [589, 594, 590, 588], "id": 587, "thread": "build-57"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#pages", "started": "21:42:58.898", "dependents": [573, 543], "id": 519, "thread": "build-78"}, {"duration": 9, "stepId": "io.quarkus.vertx.http.deployment.devmode.NotFoundProcessor#routeNotFound", "started": "21:43:01.716", "dependents": [594], "id": 586, "thread": "build-59"}, {"duration": 8, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#additionalProviders", "started": "21:42:59.325", "dependents": [555, 558, 557], "id": 554, "thread": "build-74"}, {"duration": 8, "stepId": "io.quarkus.arc.deployment.AutoAddScopeProcessor#annotationTransformer", "started": "21:42:58.275", "dependents": [510, 466, 498], "id": 448, "thread": "build-3"}, {"duration": 8, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateRuntimeConfigProperty", "started": "21:42:58.831", "dependents": [592, 594], "id": 513, "thread": "build-62"}, {"duration": 8, "stepId": "io.quarkus.jdbc.postgresql.deployment.PostgreSQLJDBCReflections#build", "started": "21:42:57.240", "dependents": [592], "id": 21, "thread": "build-4"}, {"duration": 8, "stepId": "io.quarkus.vertx.http.deployment.GeneratedStaticResourcesProcessor#produceResources", "started": "21:42:57.256", "dependents": [238], "id": 78, "thread": "build-8"}, {"duration": 8, "stepId": "io.quarkus.deployment.steps.NativeImageConfigBuildStep#build", "started": "21:42:58.665", "dependents": [594], "id": 490, "thread": "build-64"}, {"duration": 8, "stepId": "io.quarkus.hibernate.orm.deployment.GraalVMFeatures#registerStandardStackElementTypesForReflection", "started": "21:42:57.292", "dependents": [592], "id": 200, "thread": "build-33"}, {"duration": 8, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#jpaEntitiesIndexer", "started": "21:42:58.042", "dependents": [593, 410], "id": 359, "thread": "build-24"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#vetoMPConfigProperties", "started": "21:42:57.258", "dependents": [466], "id": 83, "thread": "build-35"}, {"duration": 7, "stepId": "io.quarkus.devui.deployment.menu.EndpointsProcessor#createEndpointsPage", "started": "21:42:57.308", "dependents": [576], "id": 225, "thread": "build-58"}, {"duration": 7, "stepId": "io.quarkus.scheduler.deployment.SchedulerMethodsProcessor#schedulerMethods", "started": "21:42:57.285", "dependents": [445], "id": 186, "thread": "build-31"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerSyntheticObservers", "started": "21:42:58.695", "dependents": [592, 497, 496, 510, 498, 520], "id": 495, "thread": "build-62"}, {"duration": 7, "stepId": "io.quarkus.deployment.logging.LoggingWithPanacheProcessor#process", "started": "21:42:58.044", "dependents": [572], "id": 360, "thread": "build-13"}, {"duration": 7, "stepId": "io.quarkus.mutiny.deployment.MutinyProcessor#runtimeInit", "started": "21:42:57.551", "dependents": [594], "id": 314, "thread": "build-45"}, {"duration": 7, "stepId": "io.quarkus.deployment.pkg.steps.JarResultBuildStep#outputTarget", "started": "21:42:57.273", "dependents": [563, 173, 157, 356], "id": 136, "thread": "build-5"}, {"duration": 7, "stepId": "io.quarkus.deployment.recording.substitutions.AdditionalSubstitutionsBuildStep#additionalSubstitutions", "started": "21:42:57.255", "dependents": [594], "id": 73, "thread": "build-29"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateStaticInitConfigProperty", "started": "21:42:58.831", "dependents": [592, 594], "id": 511, "thread": "build-68"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigClasses", "started": "21:42:58.835", "dependents": [594], "id": 515, "thread": "build-23"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ReflectiveBeanClassesProcessor#implicitReflectiveBeanClasses", "started": "21:42:58.630", "dependents": [520], "id": 476, "thread": "build-45"}, {"duration": 7, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#collectEventConsumers", "started": "21:42:58.629", "dependents": [481, 495], "id": 477, "thread": "build-64"}, {"duration": 7, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#httpRoot", "started": "21:42:57.282", "dependents": [261, 583, 563, 551, 586, 566, 587], "id": 174, "thread": "build-66"}, {"duration": 7, "stepId": "io.quarkus.deployment.ExtensionLoader#config", "started": "21:42:57.254", "dependents": [77, 322, 71, 433, 271, 576, 561, 582, 344, 319, 560, 70, 572, 532, 440, 80, 258, 509, 519, 423, 486, 235, 218, 270, 580, 421, 430, 88, 435, 420, 462, 134, 326, 356, 587, 297, 482, 99, 292, 494, 311, 436, 173, 441, 458, 463, 289, 549, 101, 293, 225, 447, 559, 489, 439, 298, 341, 373, 446, 82, 84, 305, 249, 381, 250, 518, 591, 104, 452, 507, 302, 272, 178, 471, 547, 338, 408, 275, 563, 337, 303, 528, 524, 425, 588, 339, 459, 534, 107, 284, 488, 106, 466, 90, 465, 123, 118, 583, 321, 330, 98, 102, 516, 327, 198, 299, 340, 594, 451, 108, 309, 176, 122, 294, 136, 450, 113, 431, 378, 317, 196, 306, 453, 266, 590, 143, 483, 119, 589, 169, 475, 525, 333, 457, 585, 445, 342, 548, 478, 332, 300, 377, 510, 527, 517, 316, 124, 146, 521, 473, 131, 129, 460, 502, 128, 174, 520, 514, 382, 156, 138, 432, 137, 276, 565, 148, 175, 145, 290, 530, 367, 295, 153], "id": 68, "thread": "build-31"}, {"duration": 7, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#registerReflectivelyAccessedMethods", "started": "21:42:57.293", "dependents": [592], "id": 199, "thread": "build-75"}, {"duration": 7, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#additionalBeans", "started": "21:42:57.250", "dependents": [441, 466], "id": 55, "thread": "build-21"}, {"duration": 7, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#validateBeans", "started": "21:42:58.824", "dependents": [520], "id": 506, "thread": "build-70"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerCustomConfigBeanTypes", "started": "21:42:58.627", "dependents": [493, 592, 491, 492], "id": 472, "thread": "build-33"}, {"duration": 7, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#registerSwaggerUiHandler", "started": "21:43:01.585", "dependents": [584, 594, 585], "id": 582, "thread": "build-59"}, {"duration": 7, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#registerServiceBinding", "started": "21:42:57.312", "dependents": [250, 420, 440, 486], "id": 231, "thread": "build-82"}, {"duration": 7, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#frameworkRoot", "started": "21:42:57.282", "dependents": [261, 548, 583, 225, 576, 566, 329, 582, 577, 587, 565, 551, 516, 179, 578, 585, 218], "id": 175, "thread": "build-43"}, {"duration": 6, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForParamConverters_dcdfdd2a310a09abe5ee3f0ed2b2bc49f36f3d07", "started": "21:42:58.075", "dependents": [592, 559, 441, 466, 549], "id": 398, "thread": "build-64"}, {"duration": 6, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#setupExceptionHandler", "started": "21:42:57.677", "dependents": [356], "id": 336, "thread": "build-37"}, {"duration": 6, "stepId": "io.quarkus.deployment.ide.IdeProcessor#effectiveIde", "started": "21:42:57.614", "dependents": [576, 329, 336, 356], "id": 326, "thread": "build-2"}, {"duration": 6, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#setupEndpoints", "started": "21:42:59.146", "dependents": [592, 555, 558, 557], "id": 533, "thread": "build-33"}, {"duration": 6, "stepId": "io.quarkus.security.jpa.deployment.QuarkusSecurityJpaProcessor#configureJpaAuthConfig", "started": "21:42:58.192", "dependents": [441], "id": 431, "thread": "build-37"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.SplitPackageProcessor#splitPackageDetection", "started": "21:42:58.019", "dependents": [520], "id": 344, "thread": "build-13"}, {"duration": 6, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#build", "started": "21:42:58.638", "dependents": [493, 592, 491, 594, 492], "id": 482, "thread": "build-64"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.AutoProducerMethodsProcessor#annotationTransformer", "started": "21:42:58.274", "dependents": [466], "id": 447, "thread": "build-78"}, {"duration": 6, "stepId": "io.quarkus.netty.deployment.NettyProcessor#build", "started": "21:42:57.268", "dependents": [592, 490], "id": 118, "thread": "build-26"}, {"duration": 6, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#supportMixins", "started": "21:42:58.042", "dependents": [493, 592, 491, 594, 492], "id": 357, "thread": "build-18"}, {"duration": 6, "stepId": "io.quarkus.devui.deployment.menu.ReadmeProcessor#createJsonRPCServiceForCache", "started": "21:42:57.257", "dependents": [544, 320], "id": 76, "thread": "build-26"}, {"duration": 6, "stepId": "io.quarkus.flyway.deployment.devui.FlywayDevUIProcessor#create", "started": "21:42:58.196", "dependents": [573, 543, 594], "id": 433, "thread": "build-42"}, {"duration": 6, "stepId": "io.quarkus.tls.CertificatesProcessor#initializeCertificate", "started": "21:42:58.640", "dependents": [493, 491, 594, 492, 587], "id": 483, "thread": "build-56"}, {"duration": 6, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#watchConfigFiles", "started": "21:42:57.248", "dependents": [438], "id": 43, "thread": "build-23"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.WrongAnnotationUsageProcessor#detect", "started": "21:42:58.630", "dependents": [520], "id": 475, "thread": "build-56"}, {"duration": 6, "stepId": "io.quarkus.datasource.deployment.devservices.DevServicesDatasourceProcessor#launchDatabases", "started": "21:42:58.251", "dependents": [538, 535, 539], "id": 440, "thread": "build-15"}, {"duration": 6, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForExceptionMappers", "started": "21:42:58.110", "dependents": [592, 559, 441, 466], "id": 407, "thread": "build-41"}, {"duration": 6, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#ensureBeanLookupAvailable", "started": "21:42:57.276", "dependents": [510, 498], "id": 144, "thread": "build-51"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#configPropertyInjectionPoints", "started": "21:42:58.824", "dependents": [513, 592, 511], "id": 505, "thread": "build-64"}, {"duration": 6, "stepId": "io.quarkus.netty.deployment.NettyProcessor#cleanupMacDNSInLog", "started": "21:42:57.246", "dependents": [422, 439], "id": 34, "thread": "build-15"}, {"duration": 6, "stepId": "io.quarkus.flyway.deployment.FlywayProcessor#createBeans", "started": "21:42:58.199", "dependents": [493, 494, 530, 441, 491, 594, 531, 466, 492], "id": 436, "thread": "build-37"}, {"duration": 6, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#metrics", "started": "21:42:57.277", "dependents": [466], "id": 146, "thread": "build-55"}, {"duration": 6, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#defaultUnwrappedExceptions", "started": "21:42:57.280", "dependents": [407], "id": 160, "thread": "build-48"}, {"duration": 5, "stepId": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheJpaCommonResourceProcessor#lookupNamedQueries_5a86a91ed8ef1aa483288c8239df231983eeb766", "started": "21:42:58.165", "dependents": [416], "id": 413, "thread": "build-3"}, {"duration": 5, "stepId": "io.quarkus.hibernate.orm.deployment.ResteasyReactiveServerIntegrationProcessor#unwrappedExceptions", "started": "21:42:57.274", "dependents": [407], "id": 130, "thread": "build-48"}, {"duration": 5, "stepId": "io.quarkus.security.deployment.SecurityProcessor#prepareBouncyCastleProviders", "started": "21:42:57.275", "dependents": [592], "id": 134, "thread": "build-50"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForFeatures", "started": "21:42:58.075", "dependents": [399, 559], "id": 394, "thread": "build-2"}, {"duration": 5, "stepId": "io.quarkus.deployment.steps.DevModeBuildStep#watchChanges", "started": "21:42:57.271", "dependents": [438], "id": 123, "thread": "build-47"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#buildResourceInterceptors", "started": "21:42:58.110", "dependents": [559, 441, 449, 466, 549, 557], "id": 406, "thread": "build-56"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#compressionSupport", "started": "21:42:57.277", "dependents": [549], "id": 143, "thread": "build-59"}, {"duration": 5, "stepId": "io.quarkus.devui.deployment.menu.DependenciesProcessor#createAppDeps", "started": "21:42:57.272", "dependents": [576], "id": 126, "thread": "build-9"}, {"duration": 5, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#build", "started": "21:42:59.525", "dependents": [572, 571], "id": 570, "thread": "build-3"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForDynamicFeatures", "started": "21:42:58.074", "dependents": [399, 559], "id": 393, "thread": "build-33"}, {"duration": 5, "stepId": "io.quarkus.deployment.pkg.steps.FileSystemResourcesBuildStep#notNormalMode", "started": "21:42:57.281", "dependents": [], "id": 157, "thread": "build-50"}, {"duration": 5, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#registerOptionalClaimProducer", "started": "21:42:58.630", "dependents": [495], "id": 473, "thread": "build-29"}, {"duration": 5, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#handler", "started": "21:42:59.417", "dependents": [586, 566, 594], "id": 565, "thread": "build-3"}, {"duration": 5, "stepId": "io.quarkus.deployment.steps.BlockingOperationControlBuildStep#blockingOP", "started": "21:42:57.535", "dependents": [594], "id": 308, "thread": "build-70"}, {"duration": 5, "stepId": "io.quarkus.devui.deployment.menu.EndpointsProcessor#createJsonRPCService", "started": "21:42:57.242", "dependents": [544, 320], "id": 14, "thread": "build-11"}, {"duration": 5, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#build", "started": "21:42:59.525", "dependents": [572], "id": 568, "thread": "build-38"}, {"duration": 5, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigMappingsBean", "started": "21:42:58.629", "dependents": [495], "id": 474, "thread": "build-78"}, {"duration": 5, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#build_d182d2fe7ae008890806ec353e99fa052582ee2d", "started": "21:42:58.192", "dependents": [570, 594], "id": 430, "thread": "build-15"}, {"duration": 5, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#lookupNamedQueries_5a86a91ed8ef1aa483288c8239df231983eeb766", "started": "21:42:58.166", "dependents": [415], "id": 414, "thread": "build-62"}, {"duration": 5, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#createJsonRPCService", "started": "21:42:57.249", "dependents": [544, 320], "id": 46, "thread": "build-25"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanForIOInterceptors", "started": "21:42:58.074", "dependents": [406], "id": 392, "thread": "build-61"}, {"duration": 5, "stepId": "io.quarkus.panache.hibernate.common.deployment.PanacheHibernateCommonResourceProcessor#replaceFieldAccesses", "started": "21:42:59.525", "dependents": [572], "id": 569, "thread": "build-57"}, {"duration": 4, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#configValidator", "started": "21:42:58.317", "dependents": [592, 547], "id": 464, "thread": "build-37"}, {"duration": 4, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#produceEagerSecurityInterceptorStorage", "started": "21:42:58.075", "dependents": [493, 491, 594, 492], "id": 395, "thread": "build-45"}, {"duration": 4, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#registerBeans", "started": "21:42:58.193", "dependents": [441, 466], "id": 429, "thread": "build-33"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.HotDeploymentConfigBuildStep#startup", "started": "21:42:57.257", "dependents": [74], "id": 65, "thread": "build-15"}, {"duration": 4, "stepId": "io.quarkus.security.deployment.SecurityProcessor#createSecurityCheckStorage", "started": "21:42:58.310", "dependents": [493, 491, 594, 517, 466, 492, 549], "id": 460, "thread": "build-37"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanForParameterContainers", "started": "21:42:58.074", "dependents": [391], "id": 389, "thread": "build-29"}, {"duration": 4, "stepId": "io.quarkus.vertx.deployment.EventConsumerMethodsProcessor#eventConsumerMethods", "started": "21:42:57.263", "dependents": [445], "id": 91, "thread": "build-15"}, {"duration": 4, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#additionalBeans", "started": "21:42:57.277", "dependents": [441, 466], "id": 141, "thread": "build-58"}, {"duration": 4, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#continuousTestingState", "started": "21:42:59.147", "dependents": [594], "id": 529, "thread": "build-23"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerContextPropagation", "started": "21:42:57.281", "dependents": [315], "id": 156, "thread": "build-5"}, {"duration": 4, "stepId": "io.quarkus.deployment.recording.AnnotationProxyBuildStep#build", "started": "21:42:57.592", "dependents": [481, 482], "id": 324, "thread": "build-2"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForInterceptors", "started": "21:42:58.074", "dependents": [406], "id": 390, "thread": "build-55"}, {"duration": 4, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#validate", "started": "21:42:58.824", "dependents": [520], "id": 504, "thread": "build-62"}, {"duration": 4, "stepId": "io.quarkus.netty.deployment.NettyProcessor#registerEventLoopBeans", "started": "21:42:57.678", "dependents": [493, 491, 594, 492], "id": 335, "thread": "build-15"}, {"duration": 4, "stepId": "io.quarkus.devui.deployment.welcome.WelcomeProcessor#createWelcomePages", "started": "21:43:01.123", "dependents": [576], "id": 575, "thread": "build-66"}, {"duration": 4, "stepId": "io.quarkus.security.deployment.SecurityProcessor#configurePermissionCheckers", "started": "21:42:58.630", "dependents": [493, 491, 594, 492], "id": 471, "thread": "build-55"}, {"duration": 4, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#createSynthBeansForConfiguredInjectionPoints", "started": "21:42:58.630", "dependents": [493, 491, 594, 492], "id": 470, "thread": "build-42"}, {"duration": 4, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#transformInjectionPoint", "started": "21:42:57.255", "dependents": [466], "id": 60, "thread": "build-32"}, {"duration": 4, "stepId": "io.quarkus.hibernate.orm.deployment.metrics.HibernateOrmMetricsProcessor#metrics", "started": "21:42:59.148", "dependents": [594], "id": 534, "thread": "build-67"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#registerStartupObservers", "started": "21:42:58.703", "dependents": [498], "id": 497, "thread": "build-45"}, {"duration": 4, "stepId": "io.quarkus.devui.deployment.menu.ReadmeProcessor#createReadmePage", "started": "21:42:57.256", "dependents": [576], "id": 61, "thread": "build-33"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#additionalReflection", "started": "21:42:59.333", "dependents": [592], "id": 557, "thread": "build-57"}, {"duration": 4, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#devDbHandler", "started": "21:42:57.244", "dependents": [440], "id": 20, "thread": "build-5"}, {"duration": 4, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#registerRowSetSupport", "started": "21:42:57.247", "dependents": [592], "id": 31, "thread": "build-9"}, {"duration": 4, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#unremovableBeans", "started": "21:42:57.251", "dependents": [510, 498], "id": 47, "thread": "build-20"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.GraalVMFeatures#registerGeneratorClassesForReflections", "started": "21:42:57.251", "dependents": [592], "id": 44, "thread": "build-3"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#deprioritizeLegacyProviders", "started": "21:42:57.247", "dependents": [558], "id": 28, "thread": "build-10"}, {"duration": 3, "stepId": "io.quarkus.devui.deployment.menu.DependenciesProcessor#createBuildTimeActions", "started": "21:42:57.271", "dependents": [541], "id": 121, "thread": "build-50"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#runtimeConfiguration", "started": "21:42:59.412", "dependents": [561, 594], "id": 560, "thread": "build-128"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ArcProcessor#loggerProducer", "started": "21:42:57.263", "dependents": [441, 466], "id": 86, "thread": "build-38"}, {"duration": 3, "stepId": "io.quarkus.devui.deployment.menu.ReportIssuesProcessor#registerJsonRpcService", "started": "21:42:57.258", "dependents": [544, 320], "id": 67, "thread": "build-34"}, {"duration": 3, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#validate", "started": "21:42:58.825", "dependents": [520], "id": 503, "thread": "build-68"}, {"duration": 3, "stepId": "io.quarkus.deployment.steps.AdditionalClassLoaderResourcesBuildStep#appendAdditionalClassloaderResources", "started": "21:42:57.257", "dependents": [345], "id": 62, "thread": "build-23"}, {"duration": 3, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setMinLevelForInitialConfigurator", "started": "21:42:57.266", "dependents": [594], "id": 101, "thread": "build-33"}, {"duration": 3, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#transactionContext", "started": "21:42:58.370", "dependents": [468], "id": 467, "thread": "build-62"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#applicationSpecificUnwrappedExceptions", "started": "21:42:58.041", "dependents": [407], "id": 355, "thread": "build-2"}, {"duration": 3, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#build_9d6b7122fb368970c50c3a870d1f672392cd8afb", "started": "21:42:57.270", "dependents": [592, 490], "id": 115, "thread": "build-33"}, {"duration": 3, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesProcessor#feature", "started": "21:42:57.244", "dependents": [594], "id": 13, "thread": "build-9"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#asyncSupport", "started": "21:42:57.248", "dependents": [549], "id": 33, "thread": "build-5"}, {"duration": 3, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#config", "started": "21:42:57.270", "dependents": [547], "id": 114, "thread": "build-42"}, {"duration": 3, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#generateAuthorizationPolicyStorage", "started": "21:42:58.064", "dependents": [441, 458], "id": 375, "thread": "build-2"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#registerHibernateOrmMetadataForCoreDialects", "started": "21:42:57.298", "dependents": [420, 425], "id": 204, "thread": "build-77"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#unremovableBeans", "started": "21:42:57.253", "dependents": [510, 498], "id": 53, "thread": "build-15"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevServicesProcessor#devServicesAutoGenerateByDefault", "started": "21:42:59.148", "dependents": [535], "id": 532, "thread": "build-59"}, {"duration": 3, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#feature", "started": "21:42:57.267", "dependents": [594], "id": 107, "thread": "build-43"}, {"duration": 3, "stepId": "io.quarkus.deployment.steps.DevServicesConfigBuildStep#deprecated", "started": "21:42:57.278", "dependents": [535], "id": 139, "thread": "build-57"}, {"duration": 3, "stepId": "io.quarkus.deployment.steps.ReflectiveHierarchyStep#ignoreJavaClassWarnings", "started": "21:42:57.255", "dependents": [556], "id": 58, "thread": "build-5"}, {"duration": 3, "stepId": "io.quarkus.security.deployment.SecurityProcessor#makePermissionCheckerClassBeansUnremovable", "started": "21:42:57.265", "dependents": [510, 498], "id": 99, "thread": "build-29"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveVertxWebSocketIntegrationProcessor#scanner", "started": "21:42:57.251", "dependents": [549], "id": 48, "thread": "build-8"}, {"duration": 3, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#registerServiceBinding", "started": "21:42:57.315", "dependents": [250, 420, 440, 486], "id": 230, "thread": "build-58"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#convertJpaResourceAnnotationsToQualifier", "started": "21:42:58.193", "dependents": [466], "id": 428, "thread": "build-64"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ObservabilityProcessor#preAuthFailureFilter", "started": "21:42:59.412", "dependents": [565, 594, 564, 587], "id": 562, "thread": "build-38"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#responseHeaderSupport", "started": "21:42:57.246", "dependents": [549], "id": 26, "thread": "build-20"}, {"duration": 3, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#registerSafeDuplicatedContextInterceptor", "started": "21:42:57.264", "dependents": [441, 466], "id": 94, "thread": "build-42"}, {"duration": 3, "stepId": "io.quarkus.datasource.deployment.devui.DevUIDatasourceProcessor#registerJsonRpcBackend", "started": "21:42:57.246", "dependents": [544, 320], "id": 24, "thread": "build-19"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#configurationDescriptorBuilding", "started": "21:42:58.188", "dependents": [431, 430, 593, 435, 438, 434, 437, 426, 429, 532, 428, 427, 594, 487], "id": 425, "thread": "build-56"}, {"duration": 3, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#provideSecurityInformation", "started": "21:42:57.267", "dependents": [462, 463], "id": 106, "thread": "build-38"}, {"duration": 3, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#reinitializeClassesForNetty", "started": "21:42:57.244", "dependents": [490], "id": 15, "thread": "build-7"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#customExceptionMappers", "started": "21:42:57.271", "dependents": [405], "id": 120, "thread": "build-46"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ArcProcessor#launchMode", "started": "21:42:57.250", "dependents": [441, 466], "id": 35, "thread": "build-19"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#additionalAsyncTypeMethodScanners", "started": "21:42:57.244", "dependents": [549], "id": 16, "thread": "build-18"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ArcProcessor#initializeContainer", "started": "21:42:59.139", "dependents": [522, 594], "id": 521, "thread": "build-74"}, {"duration": 3, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#additionalBean", "started": "21:42:57.283", "dependents": [441, 466], "id": 162, "thread": "build-59"}, {"duration": 3, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#silenceLogging", "started": "21:42:57.244", "dependents": [257], "id": 17, "thread": "build-13"}, {"duration": 3, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#build", "started": "21:42:58.636", "dependents": [589, 594, 486, 588, 483], "id": 481, "thread": "build-45"}, {"duration": 3, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#mutinyReturnTypes", "started": "21:42:57.264", "dependents": [570, 568], "id": 95, "thread": "build-26"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#registerCustomExceptionMappers", "started": "21:42:57.248", "dependents": [405], "id": 32, "thread": "build-13"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#integrateEagerSecurity", "started": "21:42:58.285", "dependents": [549], "id": 453, "thread": "build-3"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDevModeProcessor#openCommand", "started": "21:42:59.321", "dependents": [553], "id": 551, "thread": "build-74"}, {"duration": 2, "stepId": "io.quarkus.deployment.dev.IsolatedDevModeMain$AddApplicationClassPredicateBuildStep$1@60523269", "started": "21:42:57.244", "dependents": [517, 466, 549], "id": 12, "thread": "build-10"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#handleJsonAnnotations", "started": "21:42:59.322", "dependents": [592, 554, 594], "id": 552, "thread": "build-38"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#additionalBeans", "started": "21:42:58.080", "dependents": [592, 441, 466], "id": 399, "thread": "build-33"}, {"duration": 2, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#convertRoutes", "started": "21:42:59.423", "dependents": [584, 585], "id": 566, "thread": "build-57"}, {"duration": 2, "stepId": "io.quarkus.deployment.SecureRandomProcessor#registerReflectiveMethods", "started": "21:42:57.247", "dependents": [592], "id": 23, "thread": "build-21"}, {"duration": 2, "stepId": "io.quarkus.netty.deployment.NettyProcessor#registerQualifiers", "started": "21:42:57.241", "dependents": [441, 466], "id": 4, "thread": "build-7"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.deployment.GraalVMFeatures#registerJdbcArrayTypesForReflection", "started": "21:42:57.268", "dependents": [592], "id": 105, "thread": "build-27"}, {"duration": 2, "stepId": "io.quarkus.credentials.CredentialsProcessor#unremoveable", "started": "21:42:57.271", "dependents": [510, 498], "id": 112, "thread": "build-48"}, {"duration": 2, "stepId": "io.quarkus.flyway.deployment.FlywayProcessor#startActions", "started": "21:42:59.146", "dependents": [534, 589, 532, 530, 594, 531, 588], "id": 528, "thread": "build-57"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.common.deployment.JaxrsMethodsProcessor#jaxrsMethods", "started": "21:42:58.274", "dependents": [445], "id": 444, "thread": "build-41"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.init.InitializationTaskProcessor#startApplicationInitializer", "started": "21:42:59.148", "dependents": [594], "id": 530, "thread": "build-128"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#handleInitialSql", "started": "21:42:58.193", "dependents": [436, 433], "id": 427, "thread": "build-29"}, {"duration": 2, "stepId": "io.quarkus.vertx.http.deployment.ManagementInterfaceSecurityProcessor#setupAuthenticationMechanisms", "started": "21:42:57.523", "dependents": [441, 594, 466, 587], "id": 296, "thread": "build-33"}, {"duration": 2, "stepId": "io.quarkus.security.deployment.SecurityProcessor#supportBlockingExecutionOfPermissionChecks", "started": "21:42:57.261", "dependents": [445], "id": 77, "thread": "build-36"}, {"duration": 2, "stepId": "io.quarkus.deployment.steps.DevServicesConfigBuildStep#setup", "started": "21:42:59.152", "dependents": [542, 548, 536, 538, 589, 537, 545, 547, 588], "id": 535, "thread": "build-64"}, {"duration": 2, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#loadAllBuildTimeTemplates", "started": "21:43:01.524", "dependents": [583], "id": 579, "thread": "build-66"}, {"duration": 2, "stepId": "io.quarkus.vertx.web.deployment.ReactiveRoutesMethodsProcessor#reactiveRoutesMethods", "started": "21:42:57.244", "dependents": [445], "id": 11, "thread": "build-2"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#securityContextOverrideHandler", "started": "21:42:57.240", "dependents": [559], "id": 3, "thread": "build-2"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.LookupConditionsProcessor#suppressConditionsGenerators", "started": "21:42:58.274", "dependents": [466], "id": 443, "thread": "build-15"}, {"duration": 2, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#beans", "started": "21:42:57.394", "dependents": [441, 466], "id": 260, "thread": "build-2"}, {"duration": 2, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#brandingFiles", "started": "21:42:57.263", "dependents": [438], "id": 79, "thread": "build-33"}, {"duration": 2, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#overrideContextInternalInterfaceToAddSafeGuards", "started": "21:42:57.284", "dependents": [572], "id": 161, "thread": "build-58"}, {"duration": 2, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#yamlConfig", "started": "21:42:57.283", "dependents": [547], "id": 151, "thread": "build-51"}, {"duration": 2, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#registerBean", "started": "21:42:57.241", "dependents": [441, 466], "id": 5, "thread": "build-10"}, {"duration": 2, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#createContinuousTestingPages", "started": "21:42:57.263", "dependents": [576], "id": 81, "thread": "build-29"}, {"duration": 2, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#initializeRouter", "started": "21:43:01.714", "dependents": [586, 594, 587], "id": 585, "thread": "build-66"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#startPersistenceUnits", "started": "21:42:59.148", "dependents": [589, 594, 588], "id": 531, "thread": "build-64"}, {"duration": 2, "stepId": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsProcessor#devuiJsonRpcServices", "started": "21:42:57.251", "dependents": [445], "id": 36, "thread": "build-26"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#handleClassLevelExceptionMappers", "started": "21:42:58.075", "dependents": [592, 549], "id": 388, "thread": "build-68"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ArcProcessor#exposeCustomScopeNames", "started": "21:42:57.283", "dependents": [217, 447, 475, 448, 441, 266, 207, 466], "id": 155, "thread": "build-55"}, {"duration": 2, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#createJsonRPCService", "started": "21:42:57.243", "dependents": [544, 320], "id": 9, "thread": "build-15"}, {"duration": 2, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#generateDataSourceBeans", "started": "21:42:58.185", "dependents": [493, 494, 436, 528, 434, 432, 491, 424, 594, 531, 492, 425], "id": 423, "thread": "build-56"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateConfigPropertiesInjectionPoints", "started": "21:42:58.825", "dependents": [515], "id": 502, "thread": "build-54"}, {"duration": 2, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#build", "started": "21:42:58.166", "dependents": [594], "id": 412, "thread": "build-15"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#providersFromClasspath", "started": "21:42:57.266", "dependents": [555, 558, 557], "id": 97, "thread": "build-23"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#addPersistenceUnitAnnotationToIndex", "started": "21:42:57.241", "dependents": [345], "id": 2, "thread": "build-5"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#unremovableBeans", "started": "21:42:57.254", "dependents": [510, 498], "id": 54, "thread": "build-26"}, {"duration": 2, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#runtimeOverrideConfig", "started": "21:42:57.252", "dependents": [547], "id": 45, "thread": "build-5"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.devui.ResteasyReactiveDevUIProcessor#createJsonRPCService", "started": "21:42:57.248", "dependents": [544, 320], "id": 29, "thread": "build-24"}, {"duration": 2, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#openSocket", "started": "21:43:01.727", "dependents": [592, 594], "id": 591, "thread": "build-50"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.AutoInjectFieldProcessor#annotationTransformer", "started": "21:42:58.275", "dependents": [466], "id": 446, "thread": "build-64"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#registerMonitoringComponents", "started": "21:42:57.420", "dependents": [441, 466], "id": 266, "thread": "build-26"}, {"duration": 1, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#setUpPersistenceProviderAndWaitForVertxPool", "started": "21:42:58.665", "dependents": [534, 594, 531], "id": 489, "thread": "build-29"}, {"duration": 1, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerOpenApiSchemaClassesForReflection", "started": "21:42:58.291", "dependents": [592, 556], "id": 455, "thread": "build-64"}, {"duration": 1, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#addDefaultAuthFailureHandler", "started": "21:42:59.416", "dependents": [565, 594, 587], "id": 564, "thread": "build-57"}, {"duration": 1, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#registerBean", "started": "21:42:57.270", "dependents": [441, 466], "id": 109, "thread": "build-9"}, {"duration": 1, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#mapDeploymentMethods", "started": "21:42:59.171", "dependents": [544, 546], "id": 541, "thread": "build-64"}, {"duration": 1, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createKnownInternalImportMap", "started": "21:42:57.290", "dependents": [578], "id": 179, "thread": "build-63"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#registerShutdownObservers", "started": "21:42:58.703", "dependents": [498], "id": 496, "thread": "build-56"}, {"duration": 1, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#unremovableBeans", "started": "21:42:58.079", "dependents": [510, 498], "id": 397, "thread": "build-29"}, {"duration": 1, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDarkeningDefault", "started": "21:42:57.260", "dependents": [547], "id": 66, "thread": "build-36"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#multitenancy", "started": "21:42:58.193", "dependents": [493, 491, 510, 594, 492, 498], "id": 426, "thread": "build-42"}, {"duration": 1, "stepId": "io.quarkus.vertx.http.deployment.GeneratedStaticResourcesProcessor#devMode", "started": "21:42:57.253", "dependents": [438, 295, 78], "id": 42, "thread": "build-30"}, {"duration": 1, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#generateCustomProducer", "started": "21:42:58.079", "dependents": [441, 466], "id": 396, "thread": "build-55"}, {"duration": 1, "stepId": "io.quarkus.flyway.deployment.FlywayAlwaysEnabledProcessor#indexFlyway", "started": "21:42:57.241", "dependents": [342], "id": 1, "thread": "build-9"}, {"duration": 1, "stepId": "io.quarkus.netty.deployment.NettyProcessor#cleanupUnsafeLog", "started": "21:42:57.256", "dependents": [422, 439], "id": 56, "thread": "build-25"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheJpaCommonResourceProcessor#buildNamedQueryMap", "started": "21:42:58.171", "dependents": [594], "id": 416, "thread": "build-56"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.LifecycleEventsBuildStep#startupEvent", "started": "21:43:01.726", "dependents": [591, 594], "id": 589, "thread": "build-59"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ArcProcessor#notifyBeanContainerListeners", "started": "21:42:59.143", "dependents": [594, 523], "id": 522, "thread": "build-23"}, {"duration": 1, "stepId": "io.quarkus.vertx.http.deployment.StaticResourcesProcessor#runtimeInit", "started": "21:42:59.146", "dependents": [594, 587], "id": 527, "thread": "build-67"}, {"duration": 1, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#shouldNotRemoveHttpServerOptionsCustomizers", "started": "21:42:57.251", "dependents": [510, 498], "id": 38, "thread": "build-13"}, {"duration": 1, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createRelocationMap", "started": "21:42:57.290", "dependents": [578], "id": 177, "thread": "build-48"}, {"duration": 1, "stepId": "io.quarkus.devservices.postgresql.deployment.PostgresqlDevServicesProcessor#psqlCommand", "started": "21:42:59.154", "dependents": [553], "id": 537, "thread": "build-23"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#setupPersistenceProvider", "started": "21:42:58.665", "dependents": [534, 594, 531], "id": 488, "thread": "build-70"}, {"duration": 1, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#registerHttpAuthMechanismAnnotations", "started": "21:42:57.243", "dependents": [381], "id": 7, "thread": "build-13"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#callInitializer", "started": "21:42:59.146", "dependents": [594], "id": 526, "thread": "build-59"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ArcProcessor#quarkusMain", "started": "21:42:57.290", "dependents": [441, 266, 466], "id": 180, "thread": "build-74"}, {"duration": 1, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#createAllRoutes", "started": "21:43:01.585", "dependents": [583], "id": 581, "thread": "build-66"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.ShutdownListenerBuildStep#setupShutdown", "started": "21:43:01.726", "dependents": [594], "id": 590, "thread": "build-66"}, {"duration": 1, "stepId": "io.quarkus.deployment.dev.ConfigureDisableInstrumentationBuildStep#configure", "started": "21:42:57.261", "dependents": [589, 588], "id": 74, "thread": "build-33"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#unknownConfigFiles", "started": "21:42:58.021", "dependents": [594], "id": 343, "thread": "build-78"}, {"duration": 1, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#registerScope", "started": "21:42:57.281", "dependents": [155], "id": 147, "thread": "build-64"}, {"duration": 1, "stepId": "io.quarkus.security.deployment.SecurityProcessor#authorizationController", "started": "21:42:57.268", "dependents": [441, 466], "id": 102, "thread": "build-42"}, {"duration": 1, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#registerDriver", "started": "21:42:57.276", "dependents": [250], "id": 125, "thread": "build-57"}, {"duration": 1, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDefaultLogCleanupFilters", "started": "21:42:58.185", "dependents": [547], "id": 422, "thread": "build-41"}, {"duration": 1, "stepId": "io.quarkus.security.deployment.SecurityProcessor#createPermissionSecurityChecksBuilder", "started": "21:42:58.285", "dependents": [458, 471], "id": 452, "thread": "build-70"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#processInterceptedStaticMethods", "started": "21:42:58.648", "dependents": [572, 569, 592, 567, 570, 568], "id": 485, "thread": "build-56"}, {"duration": 1, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDefaultLevels", "started": "21:42:57.388", "dependents": [439, 547], "id": 257, "thread": "build-2"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ArcProcessor#signalBeanContainerReady", "started": "21:42:59.144", "dependents": [529, 533, 528, 526, 524, 586, 559, 527, 531, 587, 589, 525, 594, 546, 558, 549], "id": 523, "thread": "build-74"}, {"duration": 1, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#registerHttpAuthMechanismAnnotation", "started": "21:42:57.269", "dependents": [381], "id": 104, "thread": "build-45"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#findEnablementStereotypes", "started": "21:42:58.039", "dependents": [365, 363, 364, 361], "id": 346, "thread": "build-68"}, {"duration": 0, "stepId": "io.quarkus.deployment.JniProcessor#setupJni", "started": "21:42:57.285", "dependents": [490], "id": 153, "thread": "build-70"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#processFooterLogs", "started": "21:42:59.171", "dependents": [573, 543, 541], "id": 540, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#installCliCommands", "started": "21:42:59.324", "dependents": [589, 588], "id": 553, "thread": "build-33"}, {"duration": 0, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#cleanupVertxWarnings", "started": "21:42:57.290", "dependents": [422, 439], "id": 172, "thread": "build-58"}, {"duration": 0, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#unremoveableSkipPredicates", "started": "21:42:57.298", "dependents": [510, 498], "id": 197, "thread": "build-65"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.ProfileBuildStep#defaultProfile", "started": "21:42:57.287", "dependents": [547], "id": 168, "thread": "build-48"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.CurateOutcomeBuildStep#removeResources", "started": "21:42:57.280", "dependents": [572], "id": 131, "thread": "build-64"}, {"duration": 0, "stepId": "io.quarkus.security.jpa.common.deployment.QuarkusSecurityJpaCommonProcessor#provideJpaSecurityDefinition", "started": "21:42:58.178", "dependents": [431], "id": 419, "thread": "build-78"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#ensureBeanLookupAvailable", "started": "21:42:57.280", "dependents": [510, 498], "id": 133, "thread": "build-61"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#candidatesForFieldAccess", "started": "21:42:58.165", "dependents": [417], "id": 411, "thread": "build-56"}, {"duration": 0, "stepId": "io.quarkus.datasource.deployment.devui.DevUIDatasourceProcessor#create", "started": "21:42:57.281", "dependents": [573, 543], "id": 138, "thread": "build-61"}, {"duration": 0, "stepId": "io.quarkus.deployment.index.ApplicationArchiveBuildStep#addConfiguredIndexedDependencies", "started": "21:42:57.277", "dependents": [342], "id": 124, "thread": "build-47"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#quarkusApplication", "started": "21:42:58.041", "dependents": [441, 466], "id": 348, "thread": "build-2"}, {"duration": 0, "stepId": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsProcessor#check", "started": "21:42:58.277", "dependents": [], "id": 445, "thread": "build-15"}, {"duration": 0, "stepId": "io.quarkus.security.jpa.deployment.QuarkusSecurityJpaProcessor#panacheEntityPredicate", "started": "21:42:58.178", "dependents": [431, 419], "id": 418, "thread": "build-56"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#marker", "started": "21:42:57.286", "dependents": [342], "id": 159, "thread": "build-72"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#produceModel", "started": "21:42:57.244", "dependents": [593, 567, 359], "id": 6, "thread": "build-17"}, {"duration": 0, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setProperty", "started": "21:42:57.292", "dependents": [594], "id": 182, "thread": "build-48"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#perClassExceptionMapperSupport", "started": "21:42:58.075", "dependents": [466], "id": 387, "thread": "build-54"}, {"duration": 0, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#enableSslInNative", "started": "21:42:57.266", "dependents": [490], "id": 88, "thread": "build-43"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.AutoInjectFieldProcessor#autoInjectQualifiers", "started": "21:42:58.274", "dependents": [448, 446], "id": 442, "thread": "build-29"}, {"duration": 0, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#registerRSASigProvider", "started": "21:42:57.279", "dependents": [196], "id": 129, "thread": "build-61"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#contributeQuarkusConfigToJpaModel", "started": "21:42:57.282", "dependents": [410], "id": 145, "thread": "build-58"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#addAllWriteableMarker", "started": "21:42:59.333", "dependents": [572], "id": 555, "thread": "build-33"}, {"duration": 0, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#missingDevUIMessageHandler", "started": "21:42:57.677", "dependents": [589, 588], "id": 334, "thread": "build-64"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.devui.ResteasyReactiveDevUIProcessor#createPages", "started": "21:42:57.303", "dependents": [573, 543], "id": 210, "thread": "build-51"}, {"duration": 0, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#preventLoggerContention", "started": "21:42:57.292", "dependents": [257], "id": 181, "thread": "build-74"}, {"duration": 0, "stepId": "io.quarkus.deployment.ForkJoinPoolProcessor#setProperty", "started": "21:42:57.246", "dependents": [594], "id": 10, "thread": "build-20"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.StaticResourcesProcessor#indexHtmlFile", "started": "21:42:57.255", "dependents": [438], "id": 51, "thread": "build-8"}, {"duration": 0, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#watchYamlConfig", "started": "21:42:57.262", "dependents": [438], "id": 72, "thread": "build-38"}, {"duration": 0, "stepId": "io.quarkus.scheduler.deployment.devui.SchedulerDevUIProcessor#page", "started": "21:42:58.638", "dependents": [573, 543], "id": 480, "thread": "build-70"}, {"duration": 0, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#produceCoroutineScope", "started": "21:42:57.274", "dependents": [441, 466], "id": 117, "thread": "build-33"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#initializeRolesAllowedConfigExp", "started": "21:42:58.824", "dependents": [594], "id": 500, "thread": "build-55"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#featureBuildItem", "started": "21:42:57.254", "dependents": [594], "id": 40, "thread": "build-29"}, {"duration": 0, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#config", "started": "21:42:57.259", "dependents": [490], "id": 59, "thread": "build-21"}, {"duration": 0, "stepId": "io.quarkus.security.jpa.deployment.QuarkusSecurityJpaProcessor#feature", "started": "21:42:57.254", "dependents": [594], "id": 39, "thread": "build-19"}, {"duration": 0, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#featureAndCapability", "started": "21:42:57.295", "dependents": [213, 594], "id": 192, "thread": "build-65"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initMtlsClientAuth", "started": "21:42:57.279", "dependents": [441, 466], "id": 128, "thread": "build-9"}, {"duration": 0, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#logCleanupFilters", "started": "21:42:57.295", "dependents": [422, 439], "id": 191, "thread": "build-61"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#feature", "started": "21:42:57.286", "dependents": [594], "id": 158, "thread": "build-71"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#unremovableAsyncObserverExceptionHandlers", "started": "21:42:57.258", "dependents": [510, 498], "id": 57, "thread": "build-21"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#enrollBeanValidationTypeSafeActivatorForReflection", "started": "21:42:57.308", "dependents": [592], "id": 219, "thread": "build-45"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#aggregateParameterContainers", "started": "21:42:58.078", "dependents": [396, 397, 449, 549], "id": 391, "thread": "build-68"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ExecutorServiceProcessor#executorServiceBean", "started": "21:42:57.552", "dependents": [493, 491, 492], "id": 312, "thread": "build-68"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#buildSetup", "started": "21:42:57.272", "dependents": [594], "id": 110, "thread": "build-51"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#includeArchivesHostingEntityPackagesInIndex", "started": "21:42:57.261", "dependents": [342], "id": 70, "thread": "build-32"}, {"duration": 0, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#logCleanup", "started": "21:42:57.263", "dependents": [422, 439], "id": 75, "thread": "build-32"}, {"duration": 0, "stepId": "io.quarkus.deployment.ConstructorPropertiesProcessor#build", "started": "21:42:58.041", "dependents": [592], "id": 349, "thread": "build-19"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#featureBuildItem", "started": "21:42:57.266", "dependents": [594], "id": 85, "thread": "build-8"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#pathInterfaceImpls", "started": "21:42:58.075", "dependents": [441, 466], "id": 386, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.ReportIssuesProcessor#createReportIssuePage", "started": "21:42:57.287", "dependents": [576], "id": 164, "thread": "build-38"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#configureHandlers", "started": "21:42:59.415", "dependents": [594], "id": 561, "thread": "build-57"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.BuildMetricsProcessor#createBuildMetricsPages", "started": "21:42:57.301", "dependents": [576], "id": 202, "thread": "build-9"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#createConfigurationPages", "started": "21:42:59.154", "dependents": [576], "id": 536, "thread": "build-59"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#feature", "started": "21:42:57.281", "dependents": [594], "id": 137, "thread": "build-64"}, {"duration": 0, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#reduceLogging", "started": "21:42:57.245", "dependents": [257], "id": 8, "thread": "build-19"}, {"duration": 0, "stepId": "io.quarkus.deployment.ExtensionLoader#booleanSupplierFactory", "started": "21:42:57.266", "dependents": [142], "id": 87, "thread": "build-44"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateLogFilterBuildStep#setupLogFilters", "started": "21:42:57.254", "dependents": [422, 439], "id": 41, "thread": "build-13"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#feature", "started": "21:42:57.248", "dependents": [594], "id": 18, "thread": "build-18"}, {"duration": 0, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#logCleanup", "started": "21:42:57.306", "dependents": [422, 439], "id": 216, "thread": "build-80"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.devmode.NotFoundProcessor#resourceNotFoundDataAvailable", "started": "21:42:57.295", "dependents": [441, 466], "id": 189, "thread": "build-20"}, {"duration": 0, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#feature", "started": "21:42:57.281", "dependents": [594], "id": 140, "thread": "build-57"}, {"duration": 0, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#transformSchedulerBeans", "started": "21:42:57.394", "dependents": [466], "id": 259, "thread": "build-78"}, {"duration": 0, "stepId": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor#configFile", "started": "21:42:57.279", "dependents": [438], "id": 127, "thread": "build-61"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateUserTypeProcessor#build", "started": "21:42:58.041", "dependents": [592], "id": 350, "thread": "build-24"}, {"duration": 0, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#feature", "started": "21:42:57.303", "dependents": [594], "id": 209, "thread": "build-65"}, {"duration": 0, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#startRecoveryService", "started": "21:42:58.695", "dependents": [594], "id": 494, "thread": "build-45"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#suppressNonRuntimeConfigChanged", "started": "21:42:57.295", "dependents": [309], "id": 193, "thread": "build-74"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.ExtensionsProcessor#createExtensionsPages", "started": "21:43:01.123", "dependents": [576], "id": 574, "thread": "build-50"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#transformSecurityAnnotations", "started": "21:42:58.075", "dependents": [466], "id": 382, "thread": "build-26"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#searchForProviders", "started": "21:42:57.305", "dependents": [342], "id": 215, "thread": "build-65"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#defineTypeOfImpliedPU", "started": "21:42:58.188", "dependents": [428, 434, 425], "id": 424, "thread": "build-37"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#applicationReflection", "started": "21:42:57.256", "dependents": [592], "id": 52, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.TestsAsBeansProcessor#testAnnotations", "started": "21:42:57.284", "dependents": [441, 266, 466], "id": 150, "thread": "build-31"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#setupVersionField", "started": "21:42:57.252", "dependents": [592], "id": 37, "thread": "build-29"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#recordEntityToPersistenceUnit", "started": "21:42:59.531", "dependents": [594], "id": 571, "thread": "build-38"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#resourceIndex", "started": "21:42:58.041", "dependents": [550, 441, 380], "id": 351, "thread": "build-19"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#produceModel", "started": "21:42:57.255", "dependents": [593, 567, 359], "id": 49, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.netty.deployment.NettyProcessor#limitArenaSize", "started": "21:42:57.265", "dependents": [594], "id": 80, "thread": "build-41"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#validateAsyncObserverExceptionHandlers", "started": "21:42:58.824", "dependents": [520], "id": 499, "thread": "build-33"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.ManagementInterfaceSecurityProcessor#initializeAuthMechanismHandler", "started": "21:42:59.146", "dependents": [594], "id": 525, "thread": "build-128"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setMinimalNettyMaxOrderSize", "started": "21:42:57.262", "dependents": [80, 118], "id": 69, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.TestsAsBeansProcessor#testClassBeans", "started": "21:42:57.301", "dependents": [441, 466], "id": 201, "thread": "build-65"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#produceJcaSecurityProviders", "started": "21:42:57.273", "dependents": [275, 134, 196], "id": 113, "thread": "build-51"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.PreloadClassesBuildStep#registerPreInitClasses", "started": "21:42:57.295", "dependents": [], "id": 188, "thread": "build-31"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.deployment.PanacheHibernateResourceProcessor#collectEntityClasses", "started": "21:42:58.043", "dependents": [568], "id": 354, "thread": "build-36"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#contributePersistenceXmlToJpaModel", "started": "21:42:57.669", "dependents": [410], "id": 331, "thread": "build-13"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.BannerProcessor#watchBannerChanges", "started": "21:42:57.267", "dependents": [438], "id": 90, "thread": "build-8"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#resolveConfigExpressionRoles", "started": "21:42:58.310", "dependents": [594], "id": 459, "thread": "build-62"}, {"duration": 0, "stepId": "io.quarkus.smallrye.jwt.deployment.SmallRyeJwtProcessor#feature", "started": "21:42:57.275", "dependents": [594], "id": 119, "thread": "build-26"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#produceLoggingCategories", "started": "21:42:57.265", "dependents": [257], "id": 82, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#contributeClassesToIndex", "started": "21:42:57.292", "dependents": [345], "id": 183, "thread": "build-20"}, {"duration": 0, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#handle", "started": "21:42:57.287", "dependents": [422, 439], "id": 167, "thread": "build-58"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#setUpDenyAllJaxRs", "started": "21:42:57.284", "dependents": [460], "id": 148, "thread": "build-68"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#conditionTransformer", "started": "21:42:58.052", "dependents": [466], "id": 366, "thread": "build-62"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.CurateOutcomeBuildStep#curateOutcome", "started": "21:42:57.268", "dependents": [121, 322, 433, 242, 576, 371, 543, 241, 142, 163, 250, 572, 131, 440, 575, 486, 580, 420, 134, 291, 126, 356, 577, 573, 213, 544, 541, 342], "id": 98, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.deployment.ResteasyReactiveServerIntegrationProcessor#unwrappedExceptions", "started": "21:42:57.284", "dependents": [407], "id": 149, "thread": "build-64"}, {"duration": 0, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#doNotRemoveVertxOptionsCustomizers", "started": "21:42:57.279", "dependents": [510, 498], "id": 135, "thread": "build-63"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#waitForVertxPool", "started": "21:42:58.665", "dependents": [488, 489], "id": 487, "thread": "build-78"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveLogFilter#setupLogFilters", "started": "21:42:57.274", "dependents": [422, 439], "id": 116, "thread": "build-42"}, {"duration": 0, "stepId": "io.quarkus.elytron.security.common.deployment.QuarkusSecurityCommonProcessor#services", "started": "21:42:57.250", "dependents": [592], "id": 25, "thread": "build-26"}, {"duration": 0, "stepId": "io.quarkus.deployment.pkg.steps.NativeImageBuildStep#ignoreBuildPropertyChanges", "started": "21:42:57.260", "dependents": [309], "id": 64, "thread": "build-33"}, {"duration": 0, "stepId": "io.quarkus.flyway.deployment.FlywayAlwaysEnabledProcessor#build", "started": "21:42:57.285", "dependents": [594], "id": 154, "thread": "build-51"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ObserverValidationProcessor#validateApplicationObserver", "started": "21:42:58.824", "dependents": [520], "id": 501, "thread": "build-15"}, {"duration": 0, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#agroal", "started": "21:42:57.292", "dependents": [594], "id": 184, "thread": "build-45"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#create", "started": "21:42:57.268", "dependents": [573, 543], "id": 96, "thread": "build-15"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#unremovableContextMethodParams", "started": "21:42:58.075", "dependents": [510, 498], "id": 385, "thread": "build-20"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForParamConverters_59e3169e3a646b7fcf3083416f558434b73816c5", "started": "21:42:58.074", "dependents": [398], "id": 379, "thread": "build-26"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#registerAnnotations", "started": "21:42:57.287", "dependents": [441, 266, 466], "id": 166, "thread": "build-5"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.HotDeploymentConfigBuildStep#configFile", "started": "21:42:57.285", "dependents": [438], "id": 152, "thread": "build-64"}, {"duration": 0, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLogFilters", "started": "21:42:57.260", "dependents": [422, 439], "id": 63, "thread": "build-32"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#additionalBeans", "started": "21:42:57.248", "dependents": [441, 466], "id": 19, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheJpaCommonResourceProcessor#buildNamedQueryMap", "started": "21:42:58.171", "dependents": [594], "id": 415, "thread": "build-15"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#notFoundRoutes", "started": "21:43:01.714", "dependents": [586], "id": 584, "thread": "build-59"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#subResourcesAsBeans", "started": "21:42:58.075", "dependents": [441, 510, 466, 498], "id": 383, "thread": "build-45"}, {"duration": 0, "stepId": "io.quarkus.deployment.SslProcessor#setupNativeSsl", "started": "21:42:57.270", "dependents": [490, 125, 423, 297], "id": 108, "thread": "build-27"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.DevServicesProcessor#createDevServicesPages", "started": "21:42:59.171", "dependents": [576], "id": 539, "thread": "build-59"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#handleFieldSecurity", "started": "21:42:59.321", "dependents": [552], "id": 550, "thread": "build-33"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#checkTransactionsSupport", "started": "21:42:57.305", "dependents": [520], "id": 214, "thread": "build-45"}, {"duration": 0, "stepId": "io.quarkus.reactive.pg.client.deployment.ReactivePgClientProcessor#unremoveableBeans", "started": "21:42:57.302", "dependents": [510, 498], "id": 206, "thread": "build-78"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initializeAuthenticationHandler", "started": "21:42:59.146", "dependents": [594], "id": 524, "thread": "build-64"}, {"duration": 0, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#autoRegisterModules", "started": "21:42:58.039", "dependents": [404], "id": 347, "thread": "build-13"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#collectEntityClasses", "started": "21:42:58.043", "dependents": [570], "id": 353, "thread": "build-65"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveAlwaysEnabledProcessor#feature", "started": "21:42:57.255", "dependents": [594], "id": 50, "thread": "build-25"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#gatherClassSecurityChecks", "started": "21:42:58.286", "dependents": [458], "id": 451, "thread": "build-78"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#createJsonRPCService", "started": "21:42:57.301", "dependents": [544, 320], "id": 203, "thread": "build-75"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigPropertiesBean", "started": "21:42:58.630", "dependents": [495], "id": 469, "thread": "build-15"}, {"duration": 0, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#filterNettyHostsFileParsingWarn", "started": "21:42:57.287", "dependents": [422, 439], "id": 165, "thread": "build-72"}, {"duration": 0, "stepId": "io.quarkus.reactive.datasource.deployment.ReactiveDataSourceProcessor#addQualifierAsBean", "started": "21:42:57.248", "dependents": [441, 466], "id": 22, "thread": "build-18"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmAlwaysEnabledProcessor#featureBuildItem", "started": "21:42:57.290", "dependents": [594], "id": 170, "thread": "build-5"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ObservabilityProcessor#methodScanner", "started": "21:42:57.312", "dependents": [549], "id": 222, "thread": "build-6"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.ApplicationInfoBuildStep#create", "started": "21:42:57.261", "dependents": [594], "id": 71, "thread": "build-15"}, {"duration": 0, "stepId": "io.quarkus.security.deployment.SecurityProcessor#transformAdditionalSecuredClassesToMethods", "started": "21:42:57.265", "dependents": [382, 458], "id": 84, "thread": "build-8"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#collectInterceptedMethods", "started": "21:42:58.075", "dependents": [395, 453], "id": 384, "thread": "build-51"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#warnOfSchemaProblems", "started": "21:43:01.726", "dependents": [594], "id": 588, "thread": "build-50"}, {"duration": 0, "stepId": "io.quarkus.hibernate.reactive.deployment.HibernateReactiveProcessor#reflections", "started": "21:42:57.280", "dependents": [592], "id": 132, "thread": "build-64"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#hotDeploymentWatchedFiles", "started": "21:42:57.276", "dependents": [438], "id": 122, "thread": "build-55"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.ReflectionDiagnosticProcessor#writeReflectionData", "started": "21:43:01.729", "dependents": [], "id": 592, "thread": "build-66"}, {"duration": 0, "stepId": "io.quarkus.caffeine.deployment.CaffeineProcessor#cacheLoaders", "started": "21:42:58.043", "dependents": [592], "id": 352, "thread": "build-19"}], "started": "2025-10-08T21:42:57.239", "items": [{"count": 1314, "class": "io.quarkus.deployment.builditem.ConfigDescriptionBuildItem"}, {"count": 877, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveClassBuildItem"}, {"count": 640, "class": "io.quarkus.deployment.builditem.GeneratedClassBuildItem"}, {"count": 569, "class": "io.quarkus.deployment.builditem.BytecodeTransformerBuildItem"}, {"count": 178, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveMethodBuildItem"}, {"count": 138, "class": "io.quarkus.hibernate.reactive.panache.common.deployment.PanacheNamedQueryEntityClassBuildStep"}, {"count": 138, "class": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheNamedQueryEntityClassBuildStep"}, {"count": 95, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveFieldBuildItem"}, {"count": 93, "class": "io.quarkus.hibernate.validator.spi.AdditionalConstrainedClassBuildItem"}, {"count": 80, "class": "io.quarkus.deployment.builditem.MainBytecodeRecorderBuildItem"}, {"count": 79, "class": "io.quarkus.arc.deployment.AdditionalBeanBuildItem"}, {"count": 55, "class": "io.quarkus.deployment.builditem.StaticBytecodeRecorderBuildItem"}, {"count": 45, "class": "io.quarkus.vertx.http.deployment.RouteBuildItem"}, {"count": 36, "class": "io.quarkus.deployment.builditem.nativeimage.RuntimeInitializedClassBuildItem"}, {"count": 36, "class": "io.quarkus.arc.deployment.SyntheticBeanBuildItem"}, {"count": 35, "class": "io.quarkus.deployment.builditem.HotDeploymentWatchedFileBuildItem"}, {"count": 34, "class": "io.quarkus.hibernate.reactive.panache.deployment.PanacheEntityClassBuildItem"}, {"count": 34, "class": "io.quarkus.deployment.builditem.ConfigClassBuildItem"}, {"count": 31, "class": "io.quarkus.arc.deployment.ConfigPropertyBuildItem"}, {"count": 28, "class": "io.quarkus.deployment.builditem.RunTimeConfigurationDefaultBuildItem"}, {"count": 26, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveHierarchyBuildItem"}, {"count": 26, "class": "io.quarkus.arc.deployment.UnremovableBeanBuildItem"}, {"count": 23, "class": "io.quarkus.deployment.builditem.CapabilityBuildItem"}, {"count": 23, "class": "io.quarkus.deployment.builditem.FeatureBuildItem"}, {"count": 20, "class": "io.quarkus.deployment.logging.LogCleanupFilterBuildItem"}, {"count": 16, "class": "io.quarkus.deployment.builditem.AdditionalIndexedClassesBuildItem"}, {"count": 14, "class": "io.quarkus.devui.spi.JsonRPCProvidersBuildItem"}, {"count": 12, "class": "io.quarkus.vertx.http.deployment.webjar.WebJarBuildItem"}, {"count": 11, "class": "io.quarkus.devui.deployment.DevUIWebJarBuildItem"}, {"count": 11, "class": "io.quarkus.devui.deployment.DevUIRoutesBuildItem"}, {"count": 11, "class": "io.quarkus.arc.deployment.AnnotationsTransformerBuildItem"}, {"count": 10, "class": "io.quarkus.deployment.builditem.SuppressNonRuntimeConfigChangedWarningBuildItem"}, {"count": 10, "class": "io.quarkus.deployment.builditem.AdditionalApplicationArchiveMarkerBuildItem"}, {"count": 9, "class": "io.quarkus.devui.spi.page.CardPageBuildItem"}, {"count": 9, "class": "io.quarkus.devui.deployment.InternalPageBuildItem"}, {"count": 8, "class": "io.quarkus.deployment.builditem.ConsoleCommandBuildItem"}, {"count": 8, "class": "io.quarkus.hibernate.orm.deployment.spi.DatabaseKindDialectBuildItem"}, {"count": 8, "class": "io.quarkus.resteasy.reactive.spi.ExceptionMapperBuildItem"}, {"count": 8, "class": "io.quarkus.deployment.builditem.nativeimage.RuntimeReinitializedClassBuildItem"}, {"count": 7, "class": "io.quarkus.resteasy.reactive.server.spi.MethodScannerBuildItem"}, {"count": 7, "class": "io.quarkus.devui.spi.buildtime.BuildTimeActionBuildItem"}, {"count": 7, "class": "io.quarkus.vertx.http.deployment.devmode.NotFoundPageDisplayableEndpointBuildItem"}, {"count": 7, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyWriterBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.builditem.SystemPropertyBuildItem"}, {"count": 6, "class": "io.quarkus.arc.deployment.BeanDefiningAnnotationBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.builditem.ServiceStartBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageSystemPropertyBuildItem"}, {"count": 6, "class": "io.quarkus.arc.deployment.AutoAddScopeBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsAllowedBuildItem"}, {"count": 5, "class": "io.quarkus.vertx.http.deployment.FilterBuildItem"}, {"count": 5, "class": "io.quarkus.arc.deployment.GeneratedBeanBuildItem"}, {"count": 5, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageResourceBuildItem"}, {"count": 5, "class": "io.quarkus.devui.deployment.BuildTimeConstBuildItem"}, {"count": 4, "class": "io.quarkus.vertx.http.deployment.HttpAuthMechanismAnnotationBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.RunTimeConfigBuilderBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageConfigBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyWriterOverrideBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyReaderBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.LogCategoryBuildItem"}, {"count": 4, "class": "io.quarkus.arc.deployment.BeanRegistrationPhaseBuildItem$BeanConfiguratorBuildItem"}, {"count": 4, "class": "io.quarkus.vertx.http.deployment.spi.RouteBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.server.spi.UnwrappedExceptionBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyReaderOverrideBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.StaticInitConfigBuilderBuildItem"}, {"count": 3, "class": "io.quarkus.jackson.spi.ClassPathJacksonModuleBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.ApplicationClassPredicateBuildItem"}, {"count": 3, "class": "io.quarkus.resteasy.reactive.spi.CustomExceptionMapperBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.ConfigMappingBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.GeneratedResourceBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.ShutdownListenerBuildItem"}, {"count": 2, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceInterceptorsContributorBuildItem"}, {"count": 2, "class": "io.quarkus.hibernate.orm.panache.deployment.PanacheEntityClassBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.ObjectSubstitutionBuildItem"}, {"count": 2, "class": "io.quarkus.devui.spi.buildtime.QuteTemplateBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.RecordableConstructorBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.ExtensionSslNativeSupportBuildItem"}, {"count": 2, "class": "io.quarkus.hibernate.orm.deployment.spi.AdditionalJpaModelBuildItem"}, {"count": 2, "class": "io.quarkus.smallrye.openapi.deployment.spi.AddToOpenAPIDefinitionBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.BytecodeRecorderObjectLoaderBuildItem"}, {"count": 2, "class": "io.quarkus.devui.spi.buildtime.StaticContentBuildItem"}, {"count": 2, "class": "io.quarkus.arc.deployment.BeanContainerListenerBuildItem"}, {"count": 2, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceConfigurationHandlerBuildItem"}, {"count": 2, "class": "io.quarkus.datasource.deployment.spi.DefaultDataSourceDbKindBuildItem"}, {"count": 2, "class": "io.quarkus.devui.spi.page.FooterPageBuildItem"}, {"count": 2, "class": "io.quarkus.hibernate.orm.deployment.PersistenceProviderSetUpBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.dev.testing.TestListenerBuildItem"}, {"count": 2, "class": "io.quarkus.devui.deployment.InternalImportMapBuildItem"}, {"count": 2, "class": "io.quarkus.arc.deployment.AutoInjectAnnotationBuildItem"}, {"count": 2, "class": "io.quarkus.hibernate.orm.panache.deployment.EntityToPersistenceUnitBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.MvnpmBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.AnnotationProxyBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.BytecodeRecorderConstantDefinitionBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.console.ConsoleInstalledBuildItem"}, {"count": 1, "class": "io.quarkus.panache.hibernate.common.deployment.HibernateModelClassCandidatesForFieldAccessBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.SynthesisFinishedBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageResourceBundleBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.core.deployment.EventLoopCountBuildItem"}, {"count": 1, "class": "io.quarkus.security.jpa.common.deployment.PanacheEntityPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.core.deployment.CoreVertxBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ContextResolversBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.DockerStatusBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveHierarchyIgnoreWarningBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.deployment.LocalCodecSelectorTypesBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.InitialRouterBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.openapi.deployment.spi.OpenApiDocumentBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.dev.ExceptionNotificationBuildItem"}, {"count": 1, "class": "io.quarkus.swaggerui.deployment.SwaggerUiBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.CompiledJavaVersionBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ValidationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.EventLoopSupplierBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.BooleanSupplierFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.validator.spi.BeanValidationAnnotationsBuildItem"}, {"count": 1, "class": "io.quarkus.tls.TlsRegistryBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ParamConverterProvidersBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.ImpliedBlockingPersistenceUnitTypeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.spi.HandlerConfigurationProviderBuildItem"}, {"count": 1, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceProviderBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.DevServicesLauncherConfigResultBuildItem"}, {"count": 1, "class": "io.quarkus.security.spi.AdditionalSecurityConstrainerEventPropsBuildItem"}, {"count": 1, "class": "io.quarkus.panache.common.deployment.PanacheEntityClassesBuildItem"}, {"count": 1, "class": "io.quarkus.panache.hibernate.common.deployment.HibernateEnhancersRegisteredBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationIndexBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ThreadFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.spi.JdbcDriverBuildItem"}, {"count": 1, "class": "io.quarkus.security.spi.PermissionsAllowedMetaAnnotationBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.logging.LoggingSetupBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InterceptorBindingRegistrarBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.spi.JdbcInitialSQLGeneratorBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ArcContainerBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.JsonRPCRuntimeMethodsBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.context.deployment.spi.ThreadContextProviderBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationClassNameBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.InitTaskCompletedBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.SecurityInformationBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.StreamingLogHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.dev.DisableInstrumentationForIndexPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.logging.LoggingDecorateBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.spi.GlobalHandlerCustomizerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CurrentContextFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ParameterContainersBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ConfigurationBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ApplicationResultBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.BodyHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildExclusionsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.LogCategoryMinLevelDefaultsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.IOThreadDetectorBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InvokerFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.SslNativeConfigBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CustomScopeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ServerDefaultProducesHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.integration.HibernateOrmIntegrationRuntimeConfiguredBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.IdeRunningProcessBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.TransformedClassesBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.EventLoopGroupBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelIndexBuildItem"}, {"count": 1, "class": "io.quarkus.security.deployment.SecurityProcessor$MethodSecurityChecks"}, {"count": 1, "class": "io.quarkus.arc.deployment.devui.ArcBeanInfoBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanDiscoveryFinishedBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.RunTimeConfigurationProxyBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceInterceptorsBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.VertxDevUILogBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildCompatibleExtensionsBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.ThemeVarsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelPersistenceUnitMappingBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.context.deployment.ContextPropagationInitializedBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ExceptionMappersBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InterceptorResolverBuildItem"}, {"count": 1, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceResultBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.IndexDependencyBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.validator.deployment.HibernateValidatorProcessor$AdditionalConstrainedClassesIndexBuildItem"}, {"count": 1, "class": "io.quarkus.security.deployment.PermissionSecurityChecksBuilderBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanArchiveIndexBuildItem"}, {"count": 1, "class": "io.quarkus.jackson.spi.JacksonModuleBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ConsoleFormatterBannerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.SuppressConditionGeneratorBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildTimeEnabledStereotypesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationArchivesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ContextHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.TransformedAnnotationsBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveResourceMethodEntriesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.GeneratedFileSystemResourceHandledBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.OutputTargetBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.AuthorizationPolicyInstancesBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.PreBeanContainerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InjectionPointTransformerBuildItem"}, {"count": 1, "class": "io.quarkus.panache.hibernate.common.deployment.HibernateMetamodelForFieldAccessBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.webjar.WebJarResultsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelPersistenceUnitContributionBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.MinNettyAllocatorMaxOrderBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.openapi.deployment.OpenApiFilteredIndexViewBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.NonApplicationRootPathBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.VertxWebRouterBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.CombinedIndexBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.Capabilities"}, {"count": 1, "class": "io.quarkus.devui.deployment.ExtensionsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ExecutorBuildItem"}, {"count": 1, "class": "io.quarkus.security.deployment.JCAProviderBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.SetupEndpointsResultBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDeploymentInfoBuildItem"}, {"count": 1, "class": "io.quarkus.reactive.pg.client.deployment.PgPoolBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ObserverRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceScanningResultBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ServerSerialisersBuildItem"}, {"count": 1, "class": "io.quarkus.scheduler.deployment.DiscoveredImplementationsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.deployment.VertxBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDeploymentBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanContainerBuildItem"}, {"count": 1, "class": "io.quarkus.devui.spi.buildtime.FooterLogBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.EffectiveIdeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.JaxRsResourceIndexBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.CurateOutcomeBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.HttpRootPathBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.DeploymentMethodBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.steps.CapabilityAggregationStep$CapabilitiesConfiguredInDescriptorsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationStartBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.RelocationImportMapBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor$HttpAuthenticationHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.reactive.datasource.deployment.VertxPoolBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.AggregatedParameterContainersBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ContextRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.scheduler.deployment.SchedulerImplementationBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CustomScopeAnnotationsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.PersistenceUnitDescriptorBuildItem"}, {"count": 1, "class": "io.quarkus.flyway.deployment.FlywayProcessor$MigrationStateBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ContextRegistrationPhaseBuildItem$ContextConfiguratorBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.spi.JdbcDataSourceSchemaReadyBuildItem"}, {"count": 1, "class": "io.quarkus.panache.common.deployment.PanacheMethodCustomizerBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.BuiltInReaderOverrideBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CompletedApplicationClassPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationInfoBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.IdeFileBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.MainClassBuildItem"}], "itemsCount": 5125, "buildTarget": "quarkus-application"}